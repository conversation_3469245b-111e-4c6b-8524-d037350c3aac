{"config": {"created": "Created Redis configuration options"}, "service": {"initialized": "RedisService initialized", "getting_key": "Getting key from Redis: {{key}}", "retrieved_value": "Retrieved value for key: {{key}}, length: {{length}}", "get_error": "Error getting key from Redis: {{key}} - {{error}}", "setting_key": "Setting key: {{key}}", "set_success": "Successfully set key: {{key}}", "set_error": "Error setting key: {{key}} - {{error}}", "deleting_key": "Deleting key: {{key}}", "delete_success": "Successfully deleted key: {{key}}", "delete_error": "Error deleting key: {{key}} - {{error}}", "setting_expire": "Setting expire for key: {{key}}", "expire_success": "Successfully set expire for key: {{key}}", "expire_error": "Error setting expire for key: {{key}} - {{error}}"}}