import { relations } from "drizzle-orm";
import {
	date, // Added date type
	integer,
	jsonb,
	numeric, // Added numeric type
	pgTable,
	timestamp,
	uniqueIndex,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { vehicleStatusEnum } from "../enums/vehicle-status.enum.js";
import { fuelEnum } from "../enums/fuel.enum.js";
import { tenants } from "./tenants.schema.js"; 
// Import new related schemas (will be created next)
import { vehicleOdometerLogs } from "./vehicle_odometer_logs.schema.js";
import { vehicleServiceRecords } from "./vehicle_service_records.schema.js";
import { vehicleRepairs } from "./vehicle_repairs.schema.js";
import { vehicleUpgrades } from "./vehicle_upgrades.schema.js";
import { vehiclePartsLogs } from "./vehicle_parts_log.schema.js";
import { vehicleExpenses } from "./vehicle_expenses.schema.js";
import { vehicleNotes } from "./vehicle_notes.schema.js";
import { vehicleReminders } from "./vehicle_reminders.schema.js";

export const vehicles = pgTable(
	"vehicle",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id")
			.references(() => tenants.id)
			.notNull(),
		licensePlate: varchar("license_plate", { length: 20 }).notNull(),
		make: varchar("make", { length: 100 }).notNull(),
		model: varchar("model", { length: 100 }).notNull(),
		color: varchar("color", { length: 50 }),
		year: integer("year"),
		fuelType: fuelEnum("fuel_type"),
		registrationId: varchar("registration_id", { length: 100 }),
		status: vehicleStatusEnum("status").default("ACTIVE").notNull(),

		// Purchase/sold information
		purchaseDate: date("purchase_date"),
		purchasePrice: numeric("purchase_price", { precision: 12, scale: 2 }),
		soldDate: date("sold_date"),
		soldPrice: numeric("sold_price", { precision: 12, scale: 2 }),

		metadata: jsonb("metadata"),
		createdAt: timestamp("created_at").defaultNow().notNull(),
	},
	(table) => [
		uniqueIndex("license_plate_tenant_id_idx").on(
			table.licensePlate,
			table.tenantId,
		),
	],
);

export const vehicleRelations = relations(vehicles, ({ one, many }) => ({
	tenant: one(tenants, {
		fields: [vehicles.tenantId],
		references: [tenants.id],
	}),
	odometerLogs: many(vehicleOdometerLogs),
	serviceRecords: many(vehicleServiceRecords),
	repairs: many(vehicleRepairs),
	upgrades: many(vehicleUpgrades),
	partsLogs: many(vehiclePartsLogs),
	expenses: many(vehicleExpenses),
	notes: many(vehicleNotes),
	reminders: many(vehicleReminders),
}));
