import type { MessageDirectionType } from "@repo/db";
import type { smsStatusEnum } from "@repo/db";
// packages/types/src/events/sms.events.payload.ts
import type { BaseEventPayload } from "./base-event.payload.js";

export interface SmsDetails {
	toPhoneNumber: string;
	messageContent: string;
	relatedCallId?: string; // UUID of PbxCall
	relatedUserId?: string; // UUID of User
	// internalMessageId is now eventId in BaseEventPayload
}

export interface SmsSendRequestPayload extends BaseEventPayload {
	data: SmsDetails;
}

// Data for logging an SMS message
export interface SmsLogData {
	id: string; // The eventId of the original sms.send.request or a new UUID for the log
	tenantId: string;
	direction: MessageDirectionType; // 'IN' or 'OUT'
	type: "SMS"; // Hardcoded since this is specifically for SMS events
	fromPhoneNumber?: string; // Sender (e.g., company number for outgoing)
	toPhoneNumber: string; // Recipient
	content: string;
	status: (typeof smsStatusEnum.enumValues)[number]; // Use the enum values
	gatewayReferenceId?: string;
	failureReason?: string;
	errorCode?: string;
	sentTimestamp?: string; // ISO 8601
	receivedTimestamp?: string; // ISO 8601 for incoming
	relatedCallId?: string;
	relatedUserId?: string;
	operatorId?: string; // Operator who initiated sending
}

export interface SmsSentPayload extends BaseEventPayload {
	data: {
		originalMessageId: string; // Corresponds to eventId of SmsSendRequestPayload
		gatewayReferenceId?: string;
		sentTimestamp: string; // ISO 8601
		// Optionally include parts of SmsLogData if SmsLogService consumes this directly
		logDetails?: Pick<
			SmsLogData,
			| "toPhoneNumber"
			| "content"
			| "relatedCallId"
			| "relatedUserId"
			| "tenantId"
		>;
	};
}

export interface SmsFailedPayload extends BaseEventPayload {
	data: {
		originalMessageId: string; // Corresponds to eventId of SmsSendRequestPayload
		reason: string;
		errorCode?: string;
		// Optionally include parts of SmsLogData
		logDetails?: Pick<
			SmsLogData,
			| "toPhoneNumber"
			| "content"
			| "relatedCallId"
			| "relatedUserId"
			| "tenantId"
		>;
	};
}

// For incoming SMS messages
export interface SmsReceivedPayload extends BaseEventPayload {
	data: {
		gatewayReferenceId?: string;
		fromPhoneNumber: string;
		toPhoneNumber: string; // Your company's number that received it
		messageContent: string;
		receivedTimestamp: string; // ISO 8601
		// Optionally include parts of SmsLogData
		logDetails?: Pick<
			SmsLogData,
			"fromPhoneNumber" | "toPhoneNumber" | "content" | "tenantId"
		>;
	};
}
