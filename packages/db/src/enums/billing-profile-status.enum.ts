import { pgEnum } from "drizzle-orm/pg-core";

const BILLING_PROFILE_STATUS_VALUES = ["ACTIVE", "INACTIVE", "DEPRECATED"] as const;

export const billingProfileStatusEnum = pgEnum(
	"billing_profile_status",
	BILLING_PROFILE_STATUS_VALUES,
);

export type BillingProfileStatusType = (typeof BILLING_PROFILE_STATUS_VALUES)[number];

export enum BillingProfileStatus {
	ACTIVE = "ACTIVE",
	INACTIVE = "INACTIVE",
	DEPRECATED = "DEPRECATED",
}
