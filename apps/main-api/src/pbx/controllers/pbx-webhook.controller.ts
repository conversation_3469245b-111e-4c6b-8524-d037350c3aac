//  For PBX systems that push events via HTTP (e.g., AGI scripts, or if FreePBX GraphQL can webhook)
// apps/main-api/src/pbx/controllers/pbx-webhook.controller.ts
import { Controller, Post, Body, Param, Logger } from '@nestjs/common';
import type { PbxEventRouterService } from '../services/pbx-event-router.service';

@Controller('webhooks/pbx')
export class PbxWebhookController {
  private readonly logger = new Logger(PbxWebhookController.name);

  constructor(private readonly eventRouter: PbxEventRouterService) {}

  // Example: A generic endpoint that can receive events from different PBX instances
  // The pbxInstanceId could be part of the URL to identify the source PBX
  @Post(':pbxInstanceId/event')
  async handlePbxEvent(
    @Param('pbxInstanceId') pbxInstanceId: string,
    @Body() rawEventData: any, // Type this more strictly if possible based on what AGI/PBX sends
    // @Req() request: FastifyRequest, // For IP whitelisting or custom headers
  ): Promise<void> {
    this.logger.log(`Received webhook event for PBX Instance ${pbxInstanceId}`);
    this.logger.debug(`Raw event data for ${pbxInstanceId}: ${JSON.stringify(rawEventData)}`);

    // TODO: Add authentication/authorization for this webhook (API Key, IP Whitelist)

    // Assuming rawEventData contains enough info to determine its original type
    // For AGI, you might have a predefined structure.
    // For GraphQL webhooks (if they exist), it would be the GraphQL payload.
    // You might need a "eventTypeHint" in the payload from AGI.
    
    // Example: if AGI sends a type hint
    const eventTypeHint = rawEventData.eventType || 'GENERIC_AGI_EVENT';

    try {
      // For now, let's assume it's some form of call data that needs routing
      // This is highly dependent on what your AGI script sends or what a GraphQL webhook would send
      // We'll treat it as a generic event that PbxEventRouterService needs to make sense of
      // For FreePBX GraphQL, if it's a CDR, eventTypeHint would be 'CDR_RECEIVED'
      await this.eventRouter.routeGenericPbxEvent(pbxInstanceId, eventTypeHint, rawEventData);
    } catch (error) {
      this.logger.error(`Error processing webhook for PBX ${pbxInstanceId}:`, error);
      // Don't rethrow to prevent sending error back to PBX unless it expects it
      // throw new HttpException('Failed to process PBX event', HttpStatus.INTERNAL_SERVER_ERROR);
    }
    // Always return a 2xx to the PBX to acknowledge receipt, even if internal processing fails later (that's for RabbitMQ DLQs)
  }
}