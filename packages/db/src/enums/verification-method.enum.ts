import { pgEnum } from "drizzle-orm/pg-core";

const VERIFICATION_METHOD_VALUES = ["SMS", "EMAIL", "PHONE_CALL", "OTHER"] as const;

export const verificationMethodEnum = pgEnum(
	"verification_method",
	VERIFICATION_METHOD_VALUES,
);

export type VerificationMethodType = (typeof VERIFICATION_METHOD_VALUES)[number];

export enum VerificationMethod {
	SMS = "SMS",
	EMAIL = "EMAIL",
	PHONE_CALL = "PHONE_CALL",
	OTHER = "OTHER",
}
