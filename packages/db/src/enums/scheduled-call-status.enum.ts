import { pgEnum } from "drizzle-orm/pg-core";

// 1. Define enum values as the single source of truth
const SCHEDULED_CALL_STATUS_VALUES = [
	"PENDING", // Initial state, awaiting first activation
	"ACTIVE_ONCE", // For one-time schedules that are now active or processed
	"ACTIVE_RECURRING", // For recurring schedules that are currently active
	"COMPLETED", // For one-time schedules that have been fully processed and will not run again
	"PAUSED", // Manually paused by an operator
	"DISABLED", // Disabled, will not activate unless re-enabled
] as const;

// 2. Create the Drizzle ORM pgEnum for database schema definition
export const scheduledCallStatusEnum = pgEnum(
	"scheduled_call_status",
	SCHEDULED_CALL_STATUS_VALUES,
);

// 3. Define TypeScript union type for strict type safety
export type ScheduledCallStatusType =
	(typeof SCHEDULED_CALL_STATUS_VALUES)[number];

// 4. Define TypeScript string enum for application logic
export enum ScheduledCallStatus {
	PENDING = "PENDING",
	ACTIVE_ONCE = "ACTIVE_ONCE",
	ACTIVE_RECURRING = "ACTIVE_RECURRING",
	COMPLETED = "COMPLETED",
	PAUSED = "PAUSED",
	DISABLED = "DISABLED",
}
