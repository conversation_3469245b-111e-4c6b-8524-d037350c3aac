import type { PbxCall } from "@repo/db";
// packages/types/src/events/schedule.events.payload.ts
import type { BaseEventPayload } from "./base-event.payload.js";

// Interface for schedule details, adapt based on your actual PbxCall or a new ScheduledCall schema
export interface ScheduleDetails {
	// Added export
	id?: string; // Optional if generated by the handler
	requestedByUserId: string; // User from dashboard or system
	passengerUserId?: string; // The actual passenger
	phoneNumber?: string;
	pickupAddress: string;
	pickupLatitude?: number;
	pickupLongitude?: number;
	dropoffAddress?: string;
	dropoffLatitude?: number;
	dropoffLongitude?: number;
	scheduledTime: string; // ISO 8601
	notes?: string;
	vehicleId?: string; // Optional at request time
	// Add any other relevant fields for creating a scheduled call/ride
}

export interface ScheduleCreateRequestedPayload extends BaseEventPayload {
	data: ScheduleDetails;
}

export interface ScheduleCreatedPayload extends BaseEventPayload {
	// Assuming PbxCall is used to store scheduled calls, or a similar dedicated type
	data: PbxCall & { scheduledActivationTime?: string }; // The full scheduled call object as created/updated in DB
}

export interface ScheduleActivationDuePayload extends BaseEventPayload {
	data: {
		scheduledCallId: string; // ID of the PbxCall or ScheduledCall record
	};
}

export interface ScheduleUpdatedPayload extends BaseEventPayload {
	data: {
		scheduledCallId: string;
		updatedFields: Partial<PbxCall>; // Or Partial<ScheduledCall>
		previousFields?: Partial<PbxCall>;
	};
}

export interface ScheduleCancelledPayload extends BaseEventPayload {
	data: {
		scheduledCallId: string;
		reason?: string;
		cancelledByUserId: string;
	};
}

export interface ScheduleRemovedPayload extends BaseEventPayload {
	data: {
		scheduledCallId: string;
		// actor should be the operator who removed the schedule
	};
}

export interface ScheduleActivatedNowPayload extends BaseEventPayload {
	// This event signifies a schedule was manually triggered to become an active call
	data: {
		originalScheduledCallId: string;
		newActiveCall: PbxCall; // The newly created PbxCall record for the active call
	};
}
