{"create": {"success": "Schedule created successfully", "error": "Failed to create schedule: {{error}}", "validation": {"title_required": "Title is required", "start_time_invalid": "Invalid start time format", "end_time_after_start": "End time must be after start time"}}, "fetch": {"all": {"success": "Schedules retrieved successfully", "error": "Failed to fetch schedules: {{error}}"}, "one": {"success": "Schedule {{id}} retrieved successfully", "error": "Failed to fetch schedule with ID {{id}}: {{error}}", "not_found": "Schedule {{id}} not found"}}, "update": {"success": "Schedule {{id}} updated successfully", "error": "Failed to update schedule with ID {{id}}: {{error}}", "unauthorized": "You are not authorized to update this schedule"}, "remove": {"success": "Schedule {{id}} removed successfully", "error": "Failed to remove schedule with ID {{id}}: {{error}}", "confirmation": "Are you sure you want to delete schedule {{id}}?"}, "validation": {"title_length": "Title must be between 2-100 characters", "location_length": "Location cannot exceed 200 characters", "invalid_status": "Invalid schedule status"}}