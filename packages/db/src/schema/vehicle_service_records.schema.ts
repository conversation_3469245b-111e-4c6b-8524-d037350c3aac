import { relations } from "drizzle-orm";
import {
	date,
	index,
	integer,
	numeric,
	pgTable,
	text,
	timestamp,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { tenants } from "./tenants.schema.js";
import { vehicles } from "./vehicle.schema.js";

export const vehicleServiceRecords = pgTable(
	"vehicle_service_record",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id")
			.references(() => tenants.id, { onDelete: "cascade" })
			.notNull(),
		vehicleId: uuid("vehicle_id")
			.references(() => vehicles.id, { onDelete: "cascade" })
			.notNull(),
		date: date("date").notNull(),
		odometer: integer("odometer"),
		description: text("description").notNull(),
		cost: numeric("cost", { precision: 10, scale: 2 }),
		notes: text("notes"),
		serviceProvider: varchar("service_provider", { length: 255 }), // Optional: Name of the service shop
		createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
	},
	(table) => [
		index("vehicle_service_record_tenant_id_idx").on(table.tenantId),
		index("vehicle_service_record_vehicle_id_idx").on(table.vehicleId),
		index("vehicle_service_record_date_idx").on(table.date),
	],
);

export const vehicleServiceRecordsRelations = relations(vehicleServiceRecords, ({ one }) => ({
	tenant: one(tenants, { fields: [vehicleServiceRecords.tenantId], references: [tenants.id] }),
	vehicle: one(vehicles, { fields: [vehicleServiceRecords.vehicleId], references: [vehicles.id] }),
}));

export type VehicleServiceRecord = typeof vehicleServiceRecords.$inferSelect;
export type NewVehicleServiceRecord = typeof vehicleServiceRecords.$inferInsert;