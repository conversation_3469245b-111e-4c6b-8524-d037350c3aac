[
  {
    "feature_name": "Operator Authentication - Get Operators",
    "functional_description": "Fetches a list of available operators for the login screen, allowing a user to select an operator to log in as.",
    "api_details": {
      "method": "GET",
      "url_path": "/api/getOperators",
      "request_body": null,
      "response_body": [
        {
          "id": "number",
          "name": "string",
          "disabled": "boolean"
        }
      ],
      "query_parameters": null,
      "authentication": "None"
    },
    "business_logic": "Backend reads from the 'operators' table in PostgreSQL. The list typically includes operator ID, name, and disabled status.",
    "data_model": {
      "entities": ["operators"],
      "relationships": "operators table contains id (PK), name, disabled status."
    }
  },
  {
    "feature_name": "Operator Authentication - Login",
    "functional_description": "Authenticates an operator and establishes a persistent WebSocket connection for real-time communication. Stores authentication state in local storage.",
    "api_details": {
      "method": "WebSocket",
      "url_path": "ws://<host>:<port>/?v=<client_version>&operator_id=<operatorId>",
      "request_body": "Operator ID is passed as a query parameter in the WebSocket URL upon connection.",
      "response_body": "Upon successful connection and authentication, the server sends a 'client:init' WebSocket event containing initial application settings, configurations (like map config, message templates, enabled modules, UI columns), and potentially initial data lists (calls, scheduled calls).",
      "query_parameters": "operator_id (in WebSocket URL)",
      "authentication": "Operator ID is validated by the backend against a list of known operators. The WebSocket connection is then associated with this operator ID."
    },
    "business_logic": "Frontend stores operator details in local storage. Backend verifies operator ID, associates WebSocket connection with the operator, and sends initial configuration data via 'client:init'. If an operator is already connected from another device, the new connection might be rejected or the old one terminated.",
    "data_model": {
      "entities": ["operators"],
      "relationships": "Backend maintains a mapping of active WebSocket connections to operator IDs. Operator details are validated against the 'operators' table."
    }
  },
  {
    "feature_name": "Operator Authentication - Logout",
    "functional_description": "Logs out the currently authenticated operator, clears local authentication state, and reconfigures the WebSocket connection to an anonymous state.",
    "api_details": {
      "method": "WebSocket",
      "url_path": "ws://<host>:<port>/?v=<client_version>",
      "request_body": "The client initiates a new WebSocket connection without an 'operator_id' or signals the existing connection to change state.",
      "response_body": "Server may send a 'client:init' event for an anonymous session.",
      "query_parameters": "None (for anonymous connection)",
      "authentication": "N/A (transitions to an unauthenticated/anonymous state)"
    },
    "business_logic": "Frontend clears operator details from local storage. Backend handles the disconnection of the authenticated session and may establish a new anonymous WebSocket connection. The 'client:exit' event is processed on the backend for the logged-out operator (e.g., to unlock calls).",
    "data_model": {
      "entities": [],
      "relationships": "Backend updates its mapping of WebSocket connections, removing the association with the logged-out operator ID."
    }
  },
  {
    "feature_name": "Call Management - Fetch Initial Calls",
    "functional_description": "Loads the initial list of live calls when the application starts or after an operator logs in.",
    "api_details": {
      "method": "WebSocket (Server Push)",
      "url_path": "N/A (WebSocket event)",
      "request_body": "Implicitly triggered after 'client:init'. The 'client:init' handler on the backend may take an optional 'from' timestamp from client options to filter calls.",
      "response_body": {
        "event": "call:init",
        "data": [
          {
            "id": "string (uuid)",
            "timestamp": "number (epoch ms)",
            "vehicles": "Array<string> (optional)",
            "phone": "string",
            "location": "string (optional)",
            "destination": "string (optional)",
            "area": "string (optional)",
            "arrivaltime": "number (minutes, optional)",
            "source": "string (e.g., 'incoming', 'manual', 'schedule')",
            "extension": "number",
            "operator_id": "number (optional, last operator)",
            "operator_ids": "Array<number> (optional, all involved operators)",
            "timeassigned": "number (epoch ms, optional)",
            "timeanswered": "number (epoch ms, optional)",
            "gisdata": "object (optional, { location?: {lat, lon, address}, destination?: {lat, lon, address} })",
            "comment": "string (optional)",
            "metadata": "object (optional, e.g., { locked?: boolean, sending?: boolean, duration?: number, served?: number, canceled?: number, lang?: string })"
          }
        ]
      },
      "query_parameters": "N/A",
      "authentication": "WebSocket (Operator Session)"
    },
    "business_logic": "Backend fetches calls from the 'calls' table (PostgreSQL) within a defined backlog period (e.g., last 8 hours). It also attempts to enrich calls with contact information and metadata (like served/canceled counts from contact history, or active order details).",
    "data_model": {
      "entities": ["calls", "contacts"],
      "relationships": "Calls are the primary entity. Contact information is linked by phone number."
    }
  },
  {
    "feature_name": "Call Management - Add New Call",
    "functional_description": "Allows an operator to manually add a new call or duplicate an existing call (source 'multiple').",
    "api_details": {
      "method": "WebSocket",
      "url_path": "N/A (WebSocket event)",
      "request_body": {
        "event": "call:add",
        "data": {
          "extension": "number (optional)",
          "phone": "string",
          "location": "string (optional)",
          "area": "string (optional)",
          "timestamp": "number (epoch ms, optional, defaults to now)",
          "timeanswered": "number (epoch ms, optional, defaults to now for manual non-numeric ext)",
          "comment": "string (optional)",
          "source": "string (e.g., 'manual', 'multiple')",
          "destination": "string (optional)",
          "vehicles": "Array<string> (optional)",
          "arrivaltime": "number (minutes, optional)",
          "gisdata": "object (optional)"
        }
      },
      "response_body": {
        "event": "call:add (broadcast)",
        "data": "Full Call object (as in Fetch Initial Calls, with server-generated id, timestamp, operator_ids, etc.)"
      },
      "query_parameters": "N/A",
      "authentication": "WebSocket (Operator Session)"
    },
    "business_logic": "Backend assigns a UUID if 'id' is not present. Sets 'timestamp' and 'timeanswered' if not provided (for certain sources). Adds current operator to 'operator_ids'. If vehicles are provided, 'timeassigned' is set, and order creation logic is triggered. The call is saved to the 'calls' table. If the source is 'multiple', the new call might automatically enter edit mode for the creating operator.",
    "data_model": {
      "entities": ["calls", "operators"],
      "relationships": "New record in 'calls' table. Linked to 'operators' via 'operator_ids'."
    }
  },
  {
    "feature_name": "Call Management - Update Call Details",
    "functional_description": "Allows an operator to edit details of an existing call, such as location, destination, assigned vehicles, arrival time, area, or phone number (for manual calls).",
    "api_details": {
      "method": "WebSocket",
      "url_path": "N/A (WebSocket event)",
      "request_body": {
        "event": "call:update",
        "data": "Call object with updated fields (id is mandatory)."
      },
      "response_body": {
        "event": "call:update (broadcast)",
        "data": "Full updated Call object."
      },
      "query_parameters": "N/A",
      "authentication": "WebSocket (Operator Session). The call must be locked by the requesting operator."
    },
    "business_logic": "Backend verifies the call is locked by the operator. Updates operator involvement ('operator_ids'). Manages vehicle assignment lifecycle: if vehicles are newly assigned, 'timeassigned' is set, and order creation logic is triggered. If vehicle assignment changes, order update logic is triggered. If vehicles are removed, 'timeassigned' might be nullified, and order deletion logic is triggered. Updates the record in the 'calls' table. The call is unlocked after the update. GIS data can also be updated.",
    "data_model": {
      "entities": ["calls", "operators"],
      "relationships": "Updates existing record in 'calls' table. 'orders.js' handles related order state."
    }
  },
  {
    "feature_name": "Call Management - Remove Call",
    "functional_description": "Allows an operator to remove a call, typically if it was manually added, duplicated, or is a scheduled entry that needs deletion.",
    "api_details": {
      "method": "WebSocket",
      "url_path": "N/A (WebSocket event)",
      "request_body": {
        "event": "call:remove",
        "data": {
          "id": "string (uuid)",
          "source": "string (must be 'multiple', 'manual', or 'schedule')"
        }
      },
      "response_body": {
        "event": "call:remove (broadcast)",
        "data": "string (id of the removed call)"
      },
      "query_parameters": "N/A",
      "authentication": "WebSocket (Operator Session). The call must be locked by the requesting operator."
    },
    "business_logic": "Backend verifies the call is locked and the source is removable. If vehicles were assigned, related order status is updated/deleted. The call is unlocked and then deleted from the 'calls' table.",
    "data_model": {
      "entities": ["calls"],
      "relationships": "Deletes record from 'calls' table."
    }
  },
  {
    "feature_name": "Call Management - Cancel Call Assignment",
    "functional_description": "Cancels a vehicle assignment for a call. The call record remains but is marked as canceled (e.g., 'timeassigned' becomes null).",
    "api_details": {
      "method": "WebSocket",
      "url_path": "N/A (WebSocket event)",
      "request_body": {
        "event": "call:cancel",
        "data": "Call object (id is mandatory, vehicles array might still contain the canceled vehicles)."
      },
      "response_body": {
        "event": "call:update (broadcast)",
        "data": "Call object with 'timeassigned' set to null."
      },
      "query_parameters": "N/A",
      "authentication": "WebSocket (Operator Session). The call must be locked by the requesting operator."
    },
    "business_logic": "Backend verifies the call is locked and has vehicles assigned. Sets 'timeassigned' to null. Updates operator involvement. Unlocks the call. Updates order status via 'orders.delete()'. Updates the call record in the 'calls' table.",
    "data_model": {
      "entities": ["calls"],
      "relationships": "Updates 'timeassigned' field in 'calls' table. 'orders.js' handles related order state."
    }
  },
  {
    "feature_name": "Call Management - Activate/Undo Cancel Call",
    "functional_description": "Re-activates a previously canceled call by restoring its vehicle assignment and 'timeassigned' field, effectively making it an active order again.",
    "api_details": {
      "method": "WebSocket",
      "url_path": "N/A (WebSocket event)",
      "request_body": {
        "event": "call:update",
        "data": "Call object with 'timeassigned' restored or recalculated, and vehicles specified."
      },
      "response_body": {
        "event": "call:update (broadcast)",
        "data": "Full updated Call object."
      },
      "query_parameters": "N/A",
      "authentication": "WebSocket (Operator Session). The call must be locked by the requesting operator."
    },
    "business_logic": "This is handled as a standard 'call:update'. The frontend prepares the call object to reflect an active state (e.g., setting 'timeassigned'). Backend 'call:update' logic applies, potentially triggering order creation/update.",
    "data_model": {
      "entities": ["calls"],
      "relationships": "Updates 'calls' table. 'orders.js' handles related order state."
    }
  },
  {
    "feature_name": "Call Management - Lock/Unlock Call for Editing",
    "functional_description": "Manages exclusive editing rights for a call to prevent concurrent modifications by different operators.",
    "api_details": {
      "method": "WebSocket",
      "url_path": "N/A (WebSocket event)",
      "request_body": {
        "event_lock": "call:edit",
        "data_lock": "string (call_id)",
        "event_unlock": "call:unlocked",
        "data_unlock": "string (call_id)"
      },
      "response_body": {
        "lock_response_self": {
          "event": "call:edit",
          "data": "string (call_id)"
        },
        "lock_response_others": {
          "event": "call:locked",
          "data": "string (call_id)"
        },
        "unlock_response_others": {
          "event": "call:unlocked",
          "data": "string (call_id)"
        },
        "error_response_self": {
          "event": "client:error",
          "data": { "event": "call:edit", "data": "call_id", "msg": "This call is already locked" }
        }
      },
      "query_parameters": "N/A",
      "authentication": "WebSocket (Operator Session)"
    },
    "business_logic": "Backend maintains an in-memory map ('lockedCalls') of operator_id to call_id. 'call:edit' attempts to lock the call for the requesting operator if not already locked by another. 'call:unlocked' releases the lock. Notifications are broadcast to other clients.",
    "data_model": {
      "entities": [],
      "relationships": "In-memory map on backend: lockedCalls (operator_id -> call_id)."
    }
  },
  {
    "feature_name": "Call Management - Dial/Originate Call",
    "functional_description": "Initiates an outgoing call from the operator's station to a customer, or between operator and customer, via the PBX/Asterisk gateway.",
    "api_details": {
      "method": "WebSocket",
      "url_path": "N/A (WebSocket event)",
      "request_body": {
        "event": "call:new",
        "data": {
          "phone": "string (customer's number)",
          "localNumber": "number (operator's extension/line, optional)",
          "id": "string (call_id if related to an existing call, optional)"
        }
      },
      "response_body": "No direct response for 'call:new'. Call progress is communicated via subsequent 'call:add' or 'call:update' events. Errors are sent via 'server:error' or 'message:new' (system type) to the originating client.",
      "query_parameters": "N/A",
      "authentication": "WebSocket (Operator Session)"
    },
    "business_logic": "Backend uses 'core.inviteCall()' to track the outgoing call attempt. It then calls 'asteriskGateway.originate(num1, num2)' where 'num1' is the operator's line (if 'localNumber' provided) or customer's number, and 'num2' is the customer's number (if 'localNumber' provided). Errors from the gateway are relayed to the client.",
    "data_model": {
      "entities": ["calls"],
      "relationships": "Potentially creates a new record in 'calls' table if the call connects. 'invitedCalls' map in 'core.js' tracks the state."
    }
  },
  {
    "feature_name": "Call Management - Answer Call (from Notification)",
    "functional_description": "Allows an operator to mark a call (typically from a Texy/app notification) as answered, which may then transition it to an editable state.",
    "api_details": {
      "method": "WebSocket",
      "url_path": "N/A (WebSocket event)",
      "request_body": {
        "event": "call:answer",
        "data": "Call object (data from the notification, including phone, source, etc.)"
      },
      "response_body": {
        "event": "call:update (broadcast)",
        "data": "Call object with 'timeanswered' set and operator assigned."
      },
      "query_parameters": "N/A",
      "authentication": "WebSocket (Operator Session)"
    },
    "business_logic": "Backend checks if the call is already answered. If not, it sets 'timeanswered' to current time, assigns the current operator ('operator_ids'), and broadcasts the 'call:update'. It then attempts to put the call into edit mode for the answering operator by internally triggering 'call:edit'. The call is saved to the 'calls' table.",
    "data_model": {
      "entities": ["calls"],
      "relationships": "Creates or updates a record in the 'calls' table."
    }
  },
  {
    "feature_name": "Scheduled Call Management - Fetch Initial",
    "functional_description": "Loads the initial list of all scheduled calls.",
    "api_details": {
      "method": "WebSocket (Server Push)",
      "url_path": "N/A (WebSocket event)",
      "request_body": "Implicitly triggered after 'client:init'.",
      "response_body": {
        "event": "schedule:init",
        "data": "Array of scheduled Call objects (similar to live calls, but may include 'repeat' array for recurrence)."
      },
      "query_parameters": "N/A",
      "authentication": "WebSocket (Operator Session)"
    },
    "business_logic": "Backend's 'schedule.js' module loads scheduled calls, likely from Redis ('schedule:*' keys). These are calls planned for the future.",
    "data_model": {
      "entities": ["scheduled_calls (logical, stored in Redis)"],
      "relationships": "Scheduled calls are stored with their details. 'repeat' field: [\"HH:MM\" or 0, ... (7 days)]"
    }
  },
  {
    "feature_name": "Scheduled Call Management - Fetch Active",
    "functional_description": "Loads scheduled calls that are due soon or have recently become active based on their scheduled time and arrivaltime.",
    "api_details": {
      "method": "WebSocket (Server Push)",
      "url_path": "N/A (WebSocket event)",
      "request_body": "Implicitly triggered after 'client:init'.",
      "response_body": {
        "event": "schedule:active",
        "data": "Array of Call objects that are considered active scheduled calls."
      },
      "query_parameters": "N/A",
      "authentication": "WebSocket (Operator Session)"
    },
    "business_logic": "Backend's 'schedule.js' filters its list of scheduled calls to find those whose (timestamp - arrivaltime) is near or past the current time but haven't fully passed their active window.",
    "data_model": {
      "entities": ["scheduled_calls (logical, stored in Redis)"],
      "relationships": "Filters the list of scheduled calls."
    }
  },
  {
    "feature_name": "Scheduled Call Management - Add",
    "functional_description": "Allows an operator to schedule a new call for a future time, potentially with recurrence.",
    "api_details": {
      "method": "WebSocket",
      "url_path": "N/A (WebSocket event)",
      "request_body": {
        "event": "schedule:add",
        "data": "Call object with scheduling details: 'timestamp' (epoch ms for specific date/time or base for recurring), 'arrivaltime' (minutes prior to notify/activate), 'repeat' (Array<\"HH:MM\" or 0> for recurring days, optional), and other call details (phone, location, etc.)."
      },
      "response_body": {
        "event": "schedule:add (broadcast)",
        "data": "Full scheduled Call object with server-assigned ID if new."
      },
      "query_parameters": "N/A",
      "authentication": "WebSocket (Operator Session)"
    },
    "business_logic": "Backend ('schedule.js') may create a placeholder 'call' record if it's a new entity. Assigns operator. If vehicles are assigned, 'timeassigned' is set. For recurring calls, 'timestamp' is calculated using 'nextOccurence'. The scheduled call is saved to Redis ('schedule:<id>') and an in-memory timer is set for its activation (timestamp - arrivaltime).",
    "data_model": {
      "entities": ["scheduled_calls (Redis)", "calls (PostgreSQL, placeholder if new)"],
      "relationships": "Stored in Redis. Timers managed in backend memory."
    }
  },
  {
    "feature_name": "Scheduled Call Management - Update",
    "functional_description": "Allows an operator to modify an existing scheduled call's details, time, or recurrence.",
    "api_details": {
      "method": "WebSocket",
      "url_path": "N/A (WebSocket event)",
      "request_body": {
        "event": "schedule:update",
        "data": "Call object with updated scheduling details (id is mandatory)."
      },
      "response_body": {
        "event": "schedule:update (broadcast)",
        "data": "Full updated scheduled Call object."
      },
      "query_parameters": "N/A",
      "authentication": "WebSocket (Operator Session). Call must be locked."
    },
    "business_logic": "Backend ('schedule.js') updates operator involvement. Handles vehicle and 'timeassigned' changes. Recalculates 'timestamp' for recurring calls if needed. Updates the entry in Redis and reschedules the in-memory activation timer if 'timestamp' or 'arrivaltime' changed. Unlocks the call.",
    "data_model": {
      "entities": ["scheduled_calls (Redis)"],
      "relationships": "Updates entry in Redis. Timers managed in backend memory."
    }
  },
  {
    "feature_name": "Scheduled Call Management - Remove",
    "functional_description": "Allows an operator to delete a scheduled call.",
    "api_details": {
      "method": "WebSocket",
      "url_path": "N/A (WebSocket event)",
      "request_body": {
        "event": "schedule:remove",
        "data": { "id": "string (uuid)" }
      },
      "response_body": {
        "event": "schedule:remove (broadcast)",
        "data": "string (id of the removed scheduled call)"
      },
      "query_parameters": "N/A",
      "authentication": "WebSocket (Operator Session)"
    },
    "business_logic": "Backend ('schedule.js') deletes the scheduled call entry from Redis and clears its associated activation timer. May update a linked placeholder 'call' record (e.g., nullify 'arrivaltime').",
    "data_model": {
      "entities": ["scheduled_calls (Redis)"],
      "relationships": "Deletes entry from Redis. Clears in-memory timer."
    }
  },
  {
    "feature_name": "Scheduled Call Management - Cancel (Activate Now)",
    "functional_description": "Operator cancels a future schedule, effectively converting it into an immediate, active call to be handled.",
    "api_details": {
      "method": "WebSocket",
      "url_path": "N/A (WebSocket event)",
      "request_body": {
        "event": "schedule:cancel",
        "data": "Call object representing the scheduled call to be canceled/activated."
      },
      "response_body": [
        {
          "event": "schedule:remove (broadcast)",
          "data": "string (id of the removed scheduled call)"
        },
        {
          "event": "call:add (broadcast)",
          "data": "New active Call object based on the scheduled call."
        }
      ],
      "query_parameters": "N/A",
      "authentication": "WebSocket (Operator Session)"
    },
    "business_logic": "Backend ('schedule.js') removes the scheduled entry from Redis and clears its timer. It then creates a new active call record using the details from the scheduled call, setting its 'timestamp' to the current time and adjusting 'arrivaltime'. This new active call is processed via the standard 'call:add' logic.",
    "data_model": {
      "entities": ["scheduled_calls (Redis)", "calls (PostgreSQL)"],
      "relationships": "Removes from Redis, adds to 'calls' table."
    }
  },
  {
    "feature_name": "Search Calls",
    "functional_description": "Allows operators to search for past calls using various criteria like phone number, date range, location, vehicle, etc.",
    "api_details": {
      "method": "WebSocket",
      "url_path": "N/A (WebSocket event)",
      "request_body": {
        "event": "call:list",
        "data": {
          "count": "number (max results, e.g., 200)",
          "sort": "string ('asc' or 'desc')",
          "source": "string (optional)",
          "answered": "boolean (optional)",
          "operator_id": "number (optional)",
          "booking": "string (optional, e.g., 'served', 'unserved', 'canceled')",
          "phone": "string (optional, substring match)",
          "from": "number (epoch ms, optional, for 'timestamp' or 'timeassigned')",
          "to": "number (epoch ms, optional, for 'timestamp' or 'timeassigned')",
          "location": "string (optional, substring match)",
          "destination": "string (optional, substring match)",
          "vehicle": "number (optional, exact match in vehicles array)",
          "area": "string (optional, exact match)",
          "extension": "number (optional, exact match)"
        }
      },
      "response_body": [
        {
          "event": "contact:info (unicast to self)",
          "data": "Object mapping phone numbers from results to their contact details."
        },
        {
          "event": "call:list (unicast to self)",
          "data": "Array of Call objects matching the search criteria."
        }
      ],
      "query_parameters": "N/A",
      "authentication": "WebSocket (Operator Session)"
    },
    "business_logic": "Backend constructs a SQL query based on the filter parameters and queries the 'calls' table in PostgreSQL. It also fetches associated contact information for the resulting calls and sends both sets of data to the client.",
    "data_model": {
      "entities": ["calls", "contacts"],
      "relationships": "Queries 'calls' table. Fetches related 'contacts' data."
    }
  },
  {
    "feature_name": "Messaging - Send SMS",
    "functional_description": "Allows an operator to send an SMS message to a specified phone number, optionally linking it to an existing call.",
    "api_details": {
      "method": "WebSocket",
      "url_path": "N/A (WebSocket event)",
      "request_body": {
        "event": "message:new",
        "data": {
          "temp_id": "string (client-generated temporary ID)",
          "type": "string ('sms')",
          "msg": "string (SMS content)",
          "phone": "string (recipient phone number)",
          "call_id": "string (optional, links SMS to a call)"
        }
      },
      "response_body": [
        {
          "event": "message:sent (unicast to self, on success)",
          "data": "string (temp_id)"
        },
        {
          "event": "server:error (unicast to self, on failure)",
          "data": { "event": "message:new", "key": "temp_id", "msg": "error description" }
        },
        {
          "event": "message:new (broadcast to others, type 'system', on success)",
          "data": { "operator_id", "type": "system", "from": "SMS ➜ <phone>", "timestamp", "autohide", "msg": [["<sms_content>", null]] }
        }
      ],
      "query_parameters": "N/A",
      "authentication": "WebSocket (Operator Session)"
    },
    "business_logic": "Backend uses 'asteriskGateway.sendSMS()' to send the message via the configured SMS gateway. It tracks the message using 'temp_id' for asynchronous response handling. Notifies the sender of success/failure and other operators of the sent message.",
    "data_model": {
      "entities": [],
      "relationships": "No direct DB storage for SMS content in this flow, but 'call_id' can link it. SMS gateway configuration in 'asteriskGW.json'."
    }
  },
  {
    "feature_name": "Messaging - Receive Incoming Messages/Notifications",
    "functional_description": "Client application receives various types of messages and notifications pushed by the server, including incoming SMS, system messages, and app-specific (Texy) notifications.",
    "api_details": {
      "method": "WebSocket (Server Push)",
      "url_path": "N/A (WebSocket event)",
      "request_body": "N/A",
      "response_body": {
        "event": "message:new",
        "data_sms": { "id": "string", "type": "sms", "timestamp": "number", "phone": "string", "autohide": "number", "msg": "string", "userInfo": "object", "callInfo": "object" },
        "data_system": { "operator_id": "number", "type": "system", "timestamp": "number", "from": "string", "autohide": "number", "msg": "Array<[string, object]>" },
        "data_texy": { "type": "texy/taxiphone/bookmark", "timestamp": "number", "phone": "string", "callInfo": { "id", "location", "destination", "area" }, "autohide": "number", "msg": "string" },
        "data_toast": { "type": "info/success/error", "msg": "string", "from": "string", "autohide": "number" }
      },
      "query_parameters": "N/A",
      "authentication": "WebSocket (Operator Session or Anonymous)"
    },
    "business_logic": "Backend pushes these messages. Frontend displays them as toasts or notifications. Texy/app notifications can be 'answered' by the operator, triggering a 'call:answer' event.",
    "data_model": {
      "entities": [],
      "relationships": "N/A for client; backend generates these based on various system events or external inputs (e.g., incoming SMS from gateway, Texy app messages)."
    }
  },
  {
    "feature_name": "Statistics - Fetch Daily Statistics",
    "functional_description": "Retrieves aggregated call statistics (answered, unanswered, served, etc.) for a specified date range, typically displayed in charts and tables.",
    "api_details": {
      "method": "WebSocket",
      "url_path": "N/A (WebSocket event)",
      "request_body": {
        "event": "stats:daily",
        "data": {
          "from": { "year": "number", "month": "number", "date": "number" },
          "to": { "year": "number", "month": "number", "date": "number" }
        }
      },
      "response_body": {
        "event": "stats:daily (unicast to self)",
        "data": {
          "YYYY-MM-DD": {
            "answered": "number",
            "unanswered": "number",
            "served": "number",
            "unserved": "number",
            "canceled": "number",
            "manual": "number",
            "servedByVehicle": "string (JSON array of [vehicle, count] pairs, optional)"
          }
        }
      },
      "query_parameters": "N/A",
      "authentication": "WebSocket (Operator Session)"
    },
    "business_logic": "Backend ('stats.js') iterates through the date range. For each day, it calls 'getDayStats()'. This function first checks Redis for cached daily stats ('stats:YYYY:MM:DD'). If not found, it queries the 'calls' table (PostgreSQL) using 'core.getCallCount' with various filters to aggregate counts for different statuses, then caches the results in Redis. 'servedByVehicle' is a grouped count.",
    "data_model": {
      "entities": ["calls (PostgreSQL)", "stats_cache (Redis)"],
      "relationships": "Aggregates data from 'calls'. Caches results in Redis keys like 'stats:YYYY:MM:DD'."
    }
  },
  {
    "feature_name": "Statistics - Fetch Detailed Statistics (Per Operator)",
    "functional_description": "Retrieves detailed call statistics for a specific day, broken down by individual operators.",
    "api_details": {
      "method": "WebSocket",
      "url_path": "N/A (WebSocket event)",
      "request_body": {
        "event": "stats:detailed",
        "data": { "year": "number", "month": "number", "date": "number" }
      },
      "response_body": {
        "event": "stats:detailed (unicast to self)",
        "data": {
          "YYYY-MM-DD": {
            "operator_id_1": { "answered": "number", "...other_stats": "number" },
            "operator_id_2": { "...stats" }
          }
        }
      },
      "query_parameters": "N/A",
      "authentication": "WebSocket (Operator Session)"
    },
    "business_logic": "Backend ('stats.js') calls 'getDayStats()' with a flag to group by operator_id. This function checks Redis for cached per-operator stats (e.g., 'stats:YYYY:MM:DD:<status>:op'). If not found, it queries the 'calls' table (PostgreSQL), grouping results by operator_id, and then caches them in Redis.",
    "data_model": {
      "entities": ["calls (PostgreSQL)", "stats_cache_operator (Redis)"],
      "relationships": "Aggregates data from 'calls', grouped by operator. Caches results in Redis."
    }
  },
  {
    "feature_name": "Mapping - Get Address from Coordinates (Reverse Geocoding)",
    "functional_description": "Converts geographic coordinates (latitude and longitude) into a human-readable address using an external geocoding service.",
    "api_details": {
      "method": "GET",
      "url_path": "Configurable, e.g., from 'mapConfig.API_URL.nominatim' + 'reverse.php' (External API)",
      "request_body": null,
      "response_body": "JSON object from the geocoding service (e.g., Nominatim response format, includes 'display_name').",
      "query_parameters": "lat, lon, format, addressdetails, zoom, accept-language",
      "authentication": "Depends on the external service (likely none or API key if configured by backend/frontend)."
    },
    "business_logic": "Frontend directly calls this external API via its 'RestService'. The backend is not directly involved in proxying this specific request based on the provided code, but 'mapConfig' is supplied by the backend.",
    "data_model": {
      "entities": [],
      "relationships": "N/A (consumes external service)."
    }
  },
  {
    "feature_name": "Mapping - Get Locations from Address (Forward Geocoding/Search)",
    "functional_description": "Searches for geographic locations based on a textual query using an external geocoding service.",
    "api_details": {
      "method": "GET",
      "url_path": "Configurable, e.g., from 'mapConfig.API_URL.photon' (External API)",
      "request_body": null,
      "response_body": "JSON object from the geocoding service (e.g., Photon response format, array of features with 'properties' and 'geometry').",
      "query_parameters": "q (query text), lat, lon (for search bias), lang, limit",
      "authentication": "Depends on the external service."
    },
    "business_logic": "Frontend directly calls this external API via its 'RestService'. The service processes the response to fit the application's needs (e.g., filtering by radius). 'mapConfig' is supplied by the backend.",
    "data_model": {
      "entities": [],
      "relationships": "N/A (consumes external service)."
    }
  },
  {
    "feature_name": "Contact Management - Get Contact Info",
    "functional_description": "Retrieves detailed information for a contact based on their phone number, including name, historical call statistics (served, canceled), and common locations/destinations.",
    "api_details": {
      "method": "WebSocket",
      "url_path": "N/A (WebSocket event)",
      "request_body": {
        "event": "contact:info",
        "data": "string (phone number)"
      },
      "response_body": {
        "event": "contact:info (unicast to self)",
        "data": {
          "<phone_number>": {
            "name": "string (optional)",
            "served": "number (optional)",
            "canceled": "number (optional)",
            "location": "Array<string> (optional, common locations, may include area like 'Street | Area')",
            "destination": "Array<string> (optional, common destinations)",
            "firstcontact": "number (epoch ms, optional)",
            "lang": "string (optional, preferred language)"
          }
        }
      },
      "query_parameters": "N/A",
      "authentication": "WebSocket (Operator Session)"
    },
    "business_logic": "Backend ('contacts.js') first checks Redis cache for the contact's summary ('contacts:<phone>') and sorted sets for locations/destinations. If not fully cached, it queries the 'calls' table (PostgreSQL) to aggregate historical data (locations, destinations, served/canceled counts) and then updates the Redis cache.",
    "data_model": {
      "entities": ["calls (PostgreSQL)", "contact_cache (Redis)"],
      "relationships": "Aggregates data from 'calls' table. Caches summary in Redis hashes ('contacts:<phone>') and sorted sets for frequent locations/destinations ('contacts:<phone>:location', 'contacts:<phone>:destination')."
    }
  },
  {
    "feature_name": "Contact Management - Set Contact Name",
    "functional_description": "Allows an operator to assign or update the name associated with a phone number.",
    "api_details": {
      "method": "WebSocket",
      "url_path": "N/A (WebSocket event)",
      "request_body": {
        "event": "contact:update",
        "data": {
          "<phone_number>": { "name": "string" }
        }
      },
      "response_body": {
        "event": "contact:update (broadcast)",
        "data": "Same as request body."
      },
      "query_parameters": "N/A",
      "authentication": "WebSocket (Operator Session)"
    },
    "business_logic": "Backend ('contacts.js') updates the contact's name in the Redis cache ('contacts:<phone>' hash) and broadcasts this update to all connected clients.",
    "data_model": {
      "entities": ["contact_cache (Redis)"],
      "relationships": "Updates 'name' field in Redis hash 'contacts:<phone>'."
    }
  },
  {
    "feature_name": "Client Settings - Update Language/Resolution",
    "functional_description": "Client application sends its current settings, such as preferred language and screen resolution, to the server.",
    "api_details": {
      "method": "WebSocket",
      "url_path": "N/A (WebSocket event)",
      "request_body": {
        "event": "client:settings",
        "data": {
          "lang": "string (e.g., 'en', 'mk')",
          "resolution": { "width": "number", "height": "number" }
        }
      },
      "response_body": "None direct. Server acknowledges receipt internally.",
      "query_parameters": "N/A",
      "authentication": "WebSocket (Operator Session or Anonymous)"
    },
    "business_logic": "Backend's 'eventHandlers.js' receives these settings. Currently, it appears to be primarily for logging or potential future use, as no specific server-side action is taken with this data in the provided code snippets.",
    "data_model": {
      "entities": [],
      "relationships": "N/A. Server might log this information."
    }
  },
  {
    "feature_name": "Translations - Report Missing Translation",
    "functional_description": "Client application reports a missing translation key to the backend, presumably for administrative review and dictionary update.",
    "api_details": {
      "method": "GET",
      "url_path": "/api/updateTranslationDictionary",
      "request_body": null,
      "response_body": {
        "lang": "string",
        "key": "string",
        "plural": "boolean"
      },
      "query_parameters": "lang, key, plural",
      "authentication": "None"
    },
    "business_logic": "Backend's 'routes.js' handles this. The current implementation echoes back the received parameters. The intended logic is to update a translation dictionary (file or database).",
    "data_model": {
      "entities": ["translation_dictionary (logical)"],
      "relationships": "Intended to update translation resources."
    }
  },
  {
    "feature_name": "System - WebSocket Keep-Alive (Ping/Pong)",
    "functional_description": "Maintains the WebSocket connection liveness through a ping/pong mechanism initiated by both client and server.",
    "api_details": {
      "method": "WebSocket",
      "url_path": "N/A (WebSocket event)",
      "request_body": {
        "event_client_ping": "ping", "data_client_ping": "timestamp_string",
        "event_server_pong": "pong", "data_server_pong": "timestamp_string (echoed)"
      },
      "response_body": {
        "event_server_ping": "ping", "data_server_ping": "timestamp_string",
        "event_client_pong": "pong", "data_client_pong": "timestamp_string (echoed)"
      },
      "query_parameters": "N/A",
      "authentication": "N/A (protocol level)"
    },
    "business_logic": "Both client ('SocketService') and server ('websocket.js') periodically send 'ping' messages. The recipient must respond with a 'pong' echoing the data. Failure to receive a 'pong' within a timeout period can lead to connection termination and reconnection attempts.",
    "data_model": {
      "entities": [],
      "relationships": "N/A."
    }
  },
  {
    "feature_name": "System - Client Reload Instruction",
    "functional_description": "Server can instruct the client application to reload itself, typically used after deploying a new client version.",
    "api_details": {
      "method": "WebSocket (Server Push)",
      "url_path": "N/A (WebSocket event)",
      "request_body": "N/A",
      "response_body": {
        "event": "client:reload",
        "data": "string (new client version)"
      },
      "query_parameters": "N/A",
      "authentication": "N/A"
    },
    "business_logic": "Backend ('websocket.js') sends this event if it detects the connected client's version (from WebSocket URL query param 'v') is older than the server's expected 'clientVersion'. Frontend ('SocketService') receives this and executes 'location.reload(true)'.",
    "data_model": {
      "entities": [],
      "relationships": "N/A."
    }
  },
  {
    "feature_name": "System - Server Error Reporting to Client",
    "functional_description": "Server reports specific operational errors related to client requests back to the originating client.",
    "api_details": {
      "method": "WebSocket (Server Push)",
      "url_path": "N/A (WebSocket event)",
      "request_body": "N/A",
      "response_body": {
        "event": "server:error",
        "data": {
          "event": "string (original event name that caused error)",
          "data": "any (original data from the failed request, optional)",
          "key": "string (e.g., temp_id for messages, optional)",
          "msg": "string (error message description)"
        }
      },
      "query_parameters": "N/A",
      "authentication": "N/A (sent to specific client session)"
    },
    "business_logic": "Backend sends this event when an error occurs while processing a client's WebSocket request (e.g., failed SMS send, call already locked). Frontend services handle these errors, often by displaying a notification or reverting a UI state.",
    "data_model": {
      "entities": [],
      "relationships": "N/A."
    }
  },
  {
    "feature_name": "System - Client Error Reporting to Server",
    "functional_description": "Client application can report internal errors (e.g., failure to parse a WebSocket message) to the server for logging.",
    "api_details": {
      "method": "WebSocket",
      "url_path": "N/A (WebSocket event)",
      "request_body": {
        "event": "client:error",
        "data": {
          "info": "string (error context, e.g., 'JSON.parse error')",
          "msg": "string (error message)",
          "data": "any (problematic data, optional)"
        }
      },
      "response_body": "None direct.",
      "query_parameters": "N/A",
      "authentication": "WebSocket (Operator Session or Anonymous)"
    },
    "business_logic": "Backend's 'websocket.js' has a path to log these errors if an incoming message is malformed or an 'event' field is missing. The frontend 'SocketService' doesn't explicitly send this, but the backend anticipates it.",
    "data_model": {
      "entities": [],
      "relationships": "Server logs these errors."
    }
  },
  {
    "feature_name": "Telephony - Line Status Update (External)",
    "functional_description": "External phone systems (e.g., Fanvil IP phones) report line status events (incoming, established, terminated, hold, resume) to the backend via HTTP GET.",
    "api_details": {
      "method": "GET",
      "url_path": "/api/lineStatusUpd",
      "request_body": null,
      "response_body": "Empty 200 OK, or 422/424 on error.",
      "query_parameters": "num (phone number), ext (extension), call_id (phone's call identifier), event (e.g., 'incoming', 'established', 'terminated', 'hold', 'resume')",
      "authentication": "None (relies on network access control)"
    },
    "business_logic": "Backend ('routes.js') receives these updates. It maps 'call_id' to an internal key if needed (for Fanvil). Based on the event type: 'established' and 'terminated' events update call metadata ('duration') for live calls tracked in 'core.js' (liveCalls, lastAnsweredCalls maps) and broadcast 'call:metadata' updates via WebSocket. 'hold' and 'resume' also trigger 'call:metadata' with appropriate duration indicators.",
    "data_model": {
      "entities": ["calls (metadata update)"],
      "relationships": "Updates metadata of existing calls. Interacts with in-memory call state maps ('liveCalls', 'lastAnsweredCalls')."
    }
  },
  {
    "feature_name": "Telephony - Get Contact Name for Caller ID (External)",
    "functional_description": "External phone systems query for a contact's name based on a phone number to display on caller ID. Returns name in Latin characters.",
    "api_details": {
      "method": "GET",
      "url_path": "/api/getContactName",
      "request_body": null,
      "response_body": "string (contact name, Latin characters) or empty if not found/no name.",
      "query_parameters": "number (phone number)",
      "authentication": "None"
    },
    "business_logic": "Backend ('routes.js') normalizes the phone number and queries 'contacts.getInfo()'. If a name exists, it's converted to Latin characters using 'utils.toLatin()' and returned.",
    "data_model": {
      "entities": ["contact_cache (Redis)"],
      "relationships": "Reads contact name from Redis."
    }
  },
  {
    "feature_name": "Telephony - Vehicle Arrived Notification (External)",
    "functional_description": "External system (e.g., driver app via an intermediary) notifies that a vehicle has arrived at the destination. Backend may send an SMS to the customer.",
    "api_details": {
      "method": "GET",
      "url_path": "/api/vehicleAtDestination",
      "request_body": null,
      "response_body": "String (e.g., 'vehicle=<num>&phone=<num>&action=sms') or 422/424 error.",
      "query_parameters": "driverNum (driver's phone number)",
      "authentication": "None"
    },
    "business_logic": "Backend ('routes.js' -> 'orders.js') maps 'driverNum' to a 'vehicle'. Finds the active order for that vehicle. If an order is found and SMS is configured, it sends an SMS ('vehicleAtDestination' template) to the customer via 'asteriskGateway.sendSMS()'.",
    "data_model": {
      "entities": ["active_drivers_map (in-memory)", "assigned_calls_map (in-memory)"],
      "relationships": "Uses in-memory maps to find vehicle and order details."
    }
  }
]