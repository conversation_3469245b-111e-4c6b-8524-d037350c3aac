# Enums Convention

## **1. Creating an Enum File (`*.enum.ts`)**

**Purpose**: Define database enums with TypeScript types for comprehensive type safety, seamless Drizzle ORM integration, and robust application logic.

**Location**: Typically within a dedicated `src/enums/` directory.

**Steps**:

1. **Create a new `.enum.ts` file** (e.g., `src/enums/user-role.enum.ts`). Use PascalCase for the file name.
2. **Import `pgEnum`** from `drizzle-orm/pg-core`.
3. **Define enum values as a `const` array** for a single source of truth.
    * Name the array with a clear, descriptive, and often pluralized constant name (e.g., `USER_ROLE_VALUES`).
    * Use `as const` for literal type inference.
4. **Create the Drizzle ORM `pgEnum`**.
    * Name the exported `pgEnum` constant using camelCase (e.g., `userRoleEnum`).
    * The first argument to `pgEnum` should be the **exact column name** you intend to use in your database schema (typically `snake_case`).
5. **Add a TypeScript union type** for advanced type safety.
    * Derive this type directly from your `const` array using `(typeof VALUES)[number]`.
    * Name it descriptively (e.g., `UserRoleType`).
6. **Add a TypeScript `enum` (string enum)** for application logic, enhanced readability, and tooling support.
    * Name the enum using PascalCase (e.g., `UserRole`).
    * Assign each enum member its corresponding string value (e.g., `ADMIN = "ADMIN"`).
    * Use Screaming Snake Case for enum member names (e.g., `ADMIN`, `EDITOR`).

**Example Structure**:

```ts
// src/enums/user-role.enum.ts
import { pgEnum } from "drizzle-orm/pg-core";

// 1. Define enum values as the single source of truth.
const USER_ROLE_VALUES = ["ADMIN", "EDITOR", "VIEWER"] as const;

// 2. Create the Drizzle ORM pgEnum for database schema definition.
//    'user_role' is the column name that will be used in the database.
export const userRoleEnum = pgEnum("user_role", USER_ROLE_VALUES);

// 3. Define a TypeScript union type for strict type safety in application logic.
export type UserRoleType = (typeof USER_ROLE_VALUES)[number];

// 4. Define a TypeScript string enum for enhanced readability, autocompletion,
//    and compatibility with application logic (e.g., DTOs, services).
export enum UserRole {
  ADMIN = "ADMIN",
  EDITOR = "EDITOR",
  VIEWER = "VIEWER",
}
```

**Integration Recommendations**:

* **Drizzle Schema**: Use the `pgEnum` constant (e.g., `userRoleEnum`) directly in your Drizzle schema definition files for columns.
* **NestJS DTOs & Validation**:
  * Import and use the string enum (e.g., `UserRole`) for DTO properties.
  * Apply `@IsEnum(UserRole)` from `class-validator` for robust validation of incoming data.
  * For OpenAPI/Swagger documentation, use `@ApiProperty({ enum: UserRole, enumName: 'UserRole' })` to generate correct API schemas.
* **Application Logic**: Prefer using the string enum (e.g., `UserRole.ADMIN`) in services, controllers, and other business logic for better readability and maintainability.
* **Database Migrations**: Remember to generate and apply Drizzle migrations whenever you modify enum values in your `const` array to update your database schema.

Usage in a Drizzle schema file:
```ts
// schema.ts
import { pgTable, serial, text } from "drizzle-orm/pg-core";
import { userRoleEnum } from "./user-role.enum"; // Import your enum

export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  role: userRoleEnum("role").notNull().default(userRoleEnum.enumValues[2]), // Example default
});

Usage in a NestJS DTO (for API requests):

// create-user.dto.ts
import { IsEnum, IsString } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { UserRole } from '../enums/user-role.enum'; // Import the string enum

export class CreateUserDto {
  @IsString()
  name: string;

  @ApiProperty({ enum: UserRole, description: 'The role of the user' })
  @IsEnum(UserRole)
  role: UserRole;
}
---
