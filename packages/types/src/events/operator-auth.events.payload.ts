// packages/types/src/events/operator-auth.events.payload.ts
import type { BaseEventPayload } from "./base-event.payload.js";

export interface OperatorLoginRequestedPayload extends BaseEventPayload {
	data: {
		operatorId: string; // UUID of the operator
		clientVersion: string;
		webSocketSessionId: string; // ID of the new WebSocket connection
	};
}

export interface ClientInitData {
	// Define the structure of your client:init data
	// Example:
	mapConfig?: Record<string, any>;
	messageTemplates?: Record<string, string>;
	enabledModules?: string[];
	uiColumnSettings?: Record<string, any>;
	// Potentially initial data lists (though often fetched separately or pushed via other events)
}

export interface OperatorLoginSucceededPayload extends BaseEventPayload {
	data: {
		operatorId: string;
		webSocketSessionId: string;
		clientInitData: ClientInitData;
	};
}

export interface OperatorLoginFailedPayload extends BaseEventPayload {
	data: {
		operatorId?: string; // If attempt was made for a specific operator
		webSocketSessionId: string;
		reason: string;
	};
}

export interface OperatorLogoutCompletedPayload extends BaseEventPayload {
	data: {
		operatorId: string;
		webSocketSessionId: string; // ID of the WebSocket connection that was closed/anonymized
	};
}