import { relations } from "drizzle-orm";
import {
	boolean,
	integer,
	pgTable,
	timestamp,
	uniqueIndex, // Moved uniqueIndex import
	uuid, // Moved uuid import
	varchar, // Moved varchar import
} from "drizzle-orm/pg-core"; // Group imports
import { promoStatusEnum } from "../enums/promo-status.enum.js";
import { promotionsEnum } from "../enums/promotion.enum.js";
import { tenants } from "./tenants.schema.js";

export const promos = pgTable(
	"promos",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id")
			.notNull()
			.references(() => tenants.id),
		code: varchar("code", { length: 20 }).notNull(),
		description: varchar("description", { length: 255 }),
		discountValue: integer("discount_value").notNull(),
		discountType: promotionsEnum("discount_type").notNull(),
		maxUses: integer("max_uses"),
		uses: integer("uses").default(0).notNull(),
		startDate: timestamp("start_date").notNull(),
		endDate: timestamp("end_date").notNull(),
		isActive: boolean("is_active").default(true).notNull(),
		status: promoStatusEnum("status").default("ACTIVE").notNull(),
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at")
			.defaultNow()
			.$onUpdate(() => new Date())
			.notNull(),
	},
	(table) => [uniqueIndex("promo_code_idx").on(table.code)],
);

export const promosRelations = relations(promos, ({ one }) => ({
	tenant: one(tenants, {
		fields: [promos.tenantId],
		references: [tenants.id],
	}),
}));

export type Promo = typeof promos.$inferSelect;
export type NewPromo = typeof promos.$inferInsert;
