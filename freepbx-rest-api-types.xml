This file is a merged representation of the entire codebase, combined into a single document by Repomix.
The content has been processed where empty lines have been removed, security check has been disabled.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Empty lines have been removed from all files
- Security check has been disabled - content may contain sensitive information
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
src/
  ari-client/
    interfaces/
      applications.types.ts
      asterisk.types.ts
      bridges.types.ts
      channels.types.ts
      endpoints.types.ts
      events.types.ts
      index.ts
      playbacks.types.ts
      requests.ts
      sounds.types.ts
      websocket.types.ts
    resources/
      applications.ts
      asterisk.ts
      baseResource.ts
      bridges.ts
      channels.ts
      endpoints.ts
      playbacks.ts
      sounds.ts
    ariClient.ts
    baseClient.ts
    utils.ts
    websocketClient.ts
  index.ts
.gitignore
.npmignore
biome.json
build.js
package.json
README.md
tsconfig.json
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="src/ari-client/interfaces/applications.types.ts">
import type { WebSocketEvent } from "./events.types";
export interface Application {
  name: string;
  description?: string; // Descrição opcional do aplicativo
}
export interface ApplicationDetails extends Application {
  subscribedEvents?: WebSocketEvent["type"][]; // Lista de eventos específicos
}
</file>

<file path="src/ari-client/interfaces/asterisk.types.ts">
// Representa informações gerais sobre o servidor Asterisk
export interface AsteriskInfo {
  build: {
    os: string;
    kernel: string;
    machine: string;
    options: string[];
    modules: string[];
  };
  system: {
    entity_id: string;
    version: string;
    uptime: {
      startup_time: string;
      last_reload_time: string;
    };
  };
  config: {
    name: string;
    default_language: string;
  };
}
// Representa um módulo carregado no Asterisk
export interface Module {
  name: string;
  description: string;
  support_level: string;
  use_count: number;
  status: string; // Ex: "running" ou "not_running"
}
// Representa um canal de log configurado
export interface Logging {
  channel: string; // Nome do canal de log
  type: string; // Ex: "console", "file", etc.
  configuration: string; // Detalhes da configuração
  status: string; // Ex: "enabled", "disabled"
}
// Representa uma variável global do Asterisk
export interface Variable {
  name: string; // Nome da variável
  value: string; // Valor associado à variável
}
export interface AsteriskPing {
  asterisk_id: string; // Asterisk id info
  ping: string; // Always string value is pong
  timestamp: string; // The timestamp string of request received time
}
</file>

<file path="src/ari-client/interfaces/bridges.types.ts">
export interface Bridge {
  id: string;
  technology: string;
  bridge_type: "mixing" | "holding" | "dtmf_events" | "proxy_media";
  bridge_class?: string;
  creator?: string;
  name?: string;
  channels?: string[]; // Array de IDs de canais associados à bridge
  creationtime?: string; // Timestamp da criação da bridge
  video_mode?: "none" | "talker" | "single"; // Modos de vídeo suportados pela bridge
  video_source_id?: string; // ID do canal que é a fonte do vídeo (se aplicável)
}
export interface CreateBridgeRequest {
  type: "mixing" | "holding" | "dtmf_events" | "proxy_media";
  name?: string; // Nome opcional para a bridge
  bridgeId?: string; // ID opcional da bridge
}
export interface AddChannelRequest {
  channel: string | string[]; // Um ou mais canais para adicionar à bridge
  role?: "participant" | "announcer"; // Função do canal na bridge
}
export interface RemoveChannelRequest {
  channel: string | string[]; // Um ou mais canais para remover da bridge
}
export interface PlayMediaRequest {
  media: string; // URI do arquivo de mídia a ser reproduzido
  lang?: string; // Idioma da mídia (opcional)
  offsetms?: number; // Offset inicial em milissegundos
  skipms?: number; // Tempo de pular entre controles
  playbackId?: string; // ID opcional do playback
}
export interface BridgePlayback {
  id: string;
  media_uri: string;
  state: "queued" | "playing" | "done" | "failed";
  bridge: Bridge;
}
</file>

<file path="src/ari-client/interfaces/channels.types.ts">
export interface Channel {
  id: string;
  name: string;
  state:
    | "Down"
    | "Rsrved"
    | "OffHook"
    | "Dialing"
    | "Ring"
    | "Ringing"
    | "Up"
    | "Busy"
    | "Dialing Offhook"
    | "Pre-ring"
    | "Unknown";
  caller: {
    number: string;
    name: string;
  };
  connected: {
    number: string;
    name: string;
  };
  accountcode: string;
  dialplan: ChannelDialplan;
  creationtime: string;
  language: string;
}
export interface OriginateRequest {
  endpoint: string;
  extension?: string;
  context?: string;
  priority?: number;
  label?: string;
  app?: string;
  appArgs?: string;
  callerId?: string;
  timeout?: number;
  variables?: Record<string, string>;
  channelId?: string;
  otherChannelId?: string;
  originator?: string;
  formats?: string;
}
export interface ChannelDialplan {
  context: string;
  exten: string;
  priority: number;
  app_name?: string;
  app_data?: string;
}
export interface ChannelVar {
  variable: string;
  value: string;
}
export interface ChannelPlayback {
  id: string;
  media_uri: string;
  target_uri: string;
  state: "queued" | "playing" | "paused" | "done";
}
export interface PlaybackOptions {
  lang?: string;
  offsetms?: number;
  skipms?: number;
}
export interface RecordingOptions {
  name: string;
  format: string;
  maxDurationSeconds?: number;
  maxSilenceSeconds?: number;
  ifExists?: "fail" | "overwrite" | "append";
  beep?: boolean;
  terminateOn?: "none" | "any" | "*" | "#";
}
export interface SnoopOptions {
  spy?: string;
  whisper?: string;
  app: string;
  appArgs?: string;
  snoopId?: string;
  [key: string]: unknown; // Adicionado para compatibilidade
}
export interface RTPStats {
  jitter: string;
  loss: string;
  rtt: string;
}
export interface ExternalMediaOptions {
  app: string;
  external_host: string;
  format: string;
  encapsulation?: string;
  transport?: string;
  connection_type?: string;
  direction?: string;
  data?: string;
  [key: string]: string | undefined; // Adicionado para compatibilidade
}
</file>

<file path="src/ari-client/interfaces/endpoints.types.ts">
// endpoints.types.ts
/**
 * Represents a basic endpoint.
 */
export interface Endpoint {
  technology: string; // The technology of the endpoint, e.g., "PJSIP"
  resource: string; // The resource name of the endpoint, e.g., "9001"
  state: string; // The current state of the endpoint, e.g., "available"
}
/**
 * Represents detailed information about an endpoint.
 */
export interface EndpointDetails {
  technology: string; // The technology of the endpoint, e.g., "PJSIP"
  resource: string; // The resource name of the endpoint, e.g., "9001"
  state: string; // The current state of the endpoint, e.g., "available"
  channel_ids: string[]; // List of channel IDs associated with the endpoint
  variables?: Record<string, string>; // Optional custom variables
}
</file>

<file path="src/ari-client/interfaces/events.types.ts">
import type { ChannelInstance } from "../resources/channels";
import type { PlaybackInstance, Playbacks } from "../resources/playbacks";
import type { Bridge } from "./bridges.types";
import type { Channel } from "./channels.types";
import type { Playback } from "./playbacks.types";
export type ChannelEvent = Extract<WebSocketEvent, { channel: Channel }>;
export type PlaybackEvent = Extract<WebSocketEvent, { playback: Playback }>;
export type BridgeEvent = Extract<WebSocketEvent, { bridge: Bridge }>;
export type WebSocketEventType =
  | "DeviceStateChanged"
  | "PlaybackStarted"
  | "PlaybackContinuing"
  | "PlaybackFinished"
  | "RecordingStarted"
  | "RecordingFinished"
  | "RecordingFailed"
  | "ApplicationMoveFailed"
  | "ApplicationReplaced"
  | "BridgeCreated"
  | "BridgeDestroyed"
  | "BridgeMerged"
  | "BridgeBlindTransfer"
  | "BridgeAttendedTransfer"
  | "BridgeVideoSourceChanged"
  | "ChannelCreated"
  | "ChannelDestroyed"
  | "ChannelEnteredBridge"
  | "ChannelLeftBridge"
  | "ChannelStateChange"
  | "ChannelDtmfReceived"
  | "ChannelDialplan"
  | "ChannelCallerId"
  | "ChannelUserevent"
  | "ChannelHangupRequest"
  | "ChannelVarset"
  | "ChannelTalkingStarted"
  | "ChannelTalkingFinished"
  | "ChannelHold"
  | "ChannelUnhold"
  | "ContactStatusChange"
  | "EndpointStateChange"
  | "Dial"
  | "StasisEnd"
  | "StasisStart"
  | "TextMessageReceived"
  | "ChannelConnectedLine"
  | "PeerStatusChange";
export const bridgeEvents = [
  "BridgeCreated",
  "BridgeDestroyed",
  "BridgeMerged",
  "BridgeBlindTransfer",
  "BridgeAttendedTransfer",
  "BridgeVideoSourceChanged",
];
export type WebSocketEvent =
  | {
      type: "ChannelDtmfReceived";
      digit: string; // O dígito DTMF recebido
      duration_ms: number; // Duração do DTMF em milissegundos
      channel: Channel; // O canal em que o DTMF foi recebido
      instanceChannel?: ChannelInstance;
      application: string;
    }
  | {
      type: "ChannelDialplan";
      channel: Channel; // O canal em que o DTMF foi recebido
      instanceChannel?: ChannelInstance;
      dialplan_app: string;
      dialplan_app_data: string;
      application: string;
    }
  | {
      type: "ChannelVarset";
      variable: string;
      value: string;
      channel?: Channel; // Opcional, conforme especificado no JSON
      application: string;
      instanceChannel?: ChannelInstance;
    }
  | {
      type: "StasisStart";
      args: string[]; // Lista de argumentos fornecidos
      channel: Channel; // Obrigatório
      instanceChannel?: ChannelInstance;
      replace_channel?: Channel; // Opcional
      application: string;
    }
  | {
      type: "PlaybackStarted";
      playback: Playbacks;
      asterisk_id: string;
      application: string;
      instancePlayback?: PlaybackInstance;
    }
  | {
      type: "PlaybackContinuing";
      playback: Playbacks;
      playbackId: string;
      asterisk_id: string;
      application: string;
      instancePlayback?: PlaybackInstance;
    }
  | {
      type: "PlaybackFinished";
      playback: Playbacks;
      playbackId: string;
      asterisk_id: string;
      application: string;
      instancePlayback?: PlaybackInstance;
    }
  | {
      type: "BridgeCreated";
      bridgeId: string;
      bridgeType: string;
      channels: Channel[]; // Array de Channels associados à Bridge
      application: string;
    }
  | {
      type: "BridgeDestroyed";
      bridgeId: string;
      application: string;
    }
  | {
      type: "ChannelCreated";
      channel: Channel; // Evento possui detalhes do canal criado
      instanceChannel?: ChannelInstance;
      application: string;
    }
  | {
      type: "ChannelDestroyed";
      channel: Channel; // Evento possui detalhes do canal destruído
      instanceChannel?: ChannelInstance;
      application: string;
    }
  | {
      type: "ApplicationMoveFailed";
      application: string;
      channel: Channel;
      instanceChannel?: ChannelInstance;
    }
  | {
      type: "RecordingStarted";
      recordingId: string;
      name: string;
      application: string;
    }
  | {
      type: "RecordingFinished";
      recordingId: string;
      name: string;
      application: string;
    }
  | {
      type: "RecordingFailed";
      recordingId: string;
      name: string;
      reason: string;
      application: string;
    }
  | {
      type: "DeviceStateChanged";
      device: string;
      state: string; // Pode ser "ONLINE", "OFFLINE", etc.
      application: string;
    }
  | {
      type: "BridgeMerged";
      bridgeId: string; // ID da bridge resultante
      bridges: string[]; // IDs das bridges originais
      application: string;
    }
  | {
      type: "BridgeBlindTransfer";
      bridgeId: string; // ID da bridge onde ocorreu a transferência
      channel: Channel; // Canal que iniciou a transferência
      instanceChannel?: ChannelInstance;
      transferee: Channel; // Canal transferido
      application: string;
    }
  | {
      type: "BridgeAttendedTransfer";
      bridgeId: string; // ID da bridge onde ocorreu a transferência
      transferer: Channel; // Canal que iniciou a transferência
      transferee: Channel; // Canal sendo transferido
      destination: Channel; // Canal de destino da transferência
      application: string;
    }
  | {
      type: "BridgeVideoSourceChanged";
      bridgeId: string; // ID da bridge onde a fonte de vídeo mudou
      old_video_source_id?: string; // ID da antiga fonte de vídeo (opcional)
      new_video_source_id: string; // ID da nova fonte de vídeo
      application: string;
    }
  | {
      type: "ChannelEnteredBridge";
      bridgeId: string; // ID da bridge que o canal entrou
      channel: Channel; // Detalhes do canal que entrou na bridge
      instanceChannel?: ChannelInstance;
      application: string;
    }
  | {
      type: "ChannelLeftBridge";
      bridgeId: string; // ID da bridge que o canal deixou
      channel: Channel; // Detalhes do canal que deixou a bridge
      instanceChannel?: ChannelInstance;
      application: string;
    }
  | {
      type: "ChannelStateChange";
      channel: Channel; // Detalhes do canal cuja mudança de estado ocorreu
      instanceChannel?: ChannelInstance;
      application: string;
    }
  | {
      type: "ChannelTalkingStarted";
      channel: Channel; // Canal onde começou a detecção de fala
      instanceChannel?: ChannelInstance;
      application: string;
    }
  | {
      type: "ChannelTalkingFinished";
      channel: Channel; // Canal onde terminou a detecção de fala
      instanceChannel?: ChannelInstance;
      application: string;
    }
  | {
      type: "ChannelUnhold";
      channel: Channel; // Canal que foi retirado de espera
      instanceChannel?: ChannelInstance;
      application: string;
    }
  | {
      type: "ChannelHold";
      channel: Channel; // Canal que foi colocado em espera
      instanceChannel?: ChannelInstance;
      application: string;
    }
  | {
      type: "ContactStatusChange";
      contact_info: Record<string, any>; // Informações sobre o contato
      status: string; // Novo status do contato
      aor?: string; // Opcional, AOR associado
      application: string;
    }
  | {
      type: "EndpointStateChange";
      endpoint: Record<string, any>; // Informações do endpoint
      state: string; // Novo estado do endpoint
      application: string;
    }
  | {
      type: "Dial";
      caller: Channel; // Canal que iniciou a operação de discagem
      peer: Channel; // Canal alvo da operação de discagem
      dialstring?: string; // Opcional, string de discagem usada
      application: string;
    }
  | {
      type: "StasisEnd";
      channel: Channel; // Canal que deixou a aplicação Stasis
      instanceChannel?: ChannelInstance;
      application: string;
    }
  | {
      type: "TextMessageReceived";
      to: string; // Destinatário da mensagem
      from: string; // Remetente da mensagem
      body: string; // Conteúdo da mensagem
      variables?: Record<string, any>; // Variáveis associadas (opcional)
      application: string;
    }
  | {
      type: "ChannelConnectedLine";
      channel: Channel; // Canal cuja linha conectada foi alterada
      instanceChannel?: ChannelInstance;
      application: string;
    }
  | {
      type: "ChannelHangupRequest"; // Tipo do evento
      cause: number; // Representação inteira da causa do hangup
      soft: boolean; // Indica se o hangup foi solicitado de forma "soft"
      channel: Channel; // Informações do canal onde o hangup foi solicitado
      instanceChannel?: ChannelInstance;
      application: string;
    }
  | {
      type: "PeerStatusChange";
      peer: string; // Nome do peer
      peer_status: string; // Novo status do peer
      application: string;
    }
  | { type: string; [key: string]: any }; // Caso genérico para eventos desconhecidos
</file>

<file path="src/ari-client/interfaces/index.ts">
export type { AriClientConfig, AriApplication } from "./requests.js";
export type {
  Channel,
  OriginateRequest,
  PlaybackOptions,
  RTPStats,
  RecordingOptions,
  SnoopOptions,
  ExternalMediaOptions,
  ChannelPlayback,
  ChannelVar,
  ChannelDialplan,
} from "./channels.types.js";
export type { Endpoint, EndpointDetails } from "./endpoints.types.js";
export type { Playback, PlaybackControlRequest } from "./playbacks.types.js";
export type { Application, ApplicationDetails } from "./applications.types.js";
export type { Sound, SoundListRequest } from "./sounds.types.js";
export type {
  AsteriskInfo,
  Module,
  Logging,
  Variable,
  AsteriskPing,
} from "./asterisk.types";
export type {
  WebSocketEvent,
  WebSocketEventType,
  ChannelEvent,
  PlaybackEvent,
  BridgeEvent,
} from "./events.types.js";
export type {
  AddChannelRequest,
  RemoveChannelRequest,
  CreateBridgeRequest,
  Bridge,
  BridgePlayback,
  PlayMediaRequest,
} from "./bridges.types";
</file>

<file path="src/ari-client/interfaces/playbacks.types.ts">
/**
 * Representa os detalhes de um Playback no Asterisk.
 */
export interface Playback {
  id: string; // ID único para esta operação de playback
  media_uri: string; // URI da mídia que está sendo reproduzida
  next_media_uri?: string; // URI da próxima mídia a ser reproduzida, caso exista uma fila
  target_uri: string; // URI do canal ou bridge onde a mídia está sendo reproduzida
  language?: string; // Idioma solicitado para a reprodução, se suportado
  state:
    | "queued" // Playback enfileirado e aguardando para iniciar
    | "playing" // Playback atualmente em execução
    | "paused" // Playback pausado
    | "stopped" // Playback interrompido
    | "done" // Playback finalizado com sucesso
    | "failed"; // Playback falhou durante a execução
}
/**
 * Define as operações disponíveis para controlar um Playback.
 */
export interface PlaybackControlRequest {
  operation: "pause" | "unpause" | "reverse" | "forward";
}
</file>

<file path="src/ari-client/interfaces/requests.ts">
export interface AriClientConfig {
  host: string; // Ex.: "localhost"
  port: number; // Ex.: 8088
  username: string; // Ex.: "ipcomari"
  password: string; // Ex.: "password123"
  secure?: boolean; // Indica se é uma conexão segura (default: true)
}
export interface AriApplication {
  name: string;
  // [key: string]: any; // Caso existam outros campos desconhecidos, remova isso se os campos forem conhecidos.
}
</file>

<file path="src/ari-client/interfaces/sounds.types.ts">
export interface Sound {
  id: string;
  text?: string;
  formats: string[];
  language: string;
}
export interface SoundListRequest {
  lang?: string;
  format?: string;
}
</file>

<file path="src/ari-client/interfaces/websocket.types.ts">
import type { WebSocketEvent } from "./events.types";
export type WebSocketEventListener = (data: WebSocketEvent) => void;
/**
 * Tipo específico para um listener de um tipo de evento específico
 */
export type TypedWebSocketEventListener<T extends WebSocketEvent["type"]> = (
  data: Extract<WebSocketEvent, { type: T }>,
) => void;
</file>

<file path="src/ari-client/resources/applications.ts">
import type { BaseClient } from "../baseClient.js";
import type {
  Application,
  ApplicationDetails,
} from "../interfaces/applications.types.js";
export interface ApplicationMessage {
  event: string;
  data?: Record<string, any>;
}
export class Applications {
  constructor(private client: BaseClient) {}
  /**
   * Lists all applications.
   *
   * @returns A promise that resolves to an array of Application objects.
   * @throws {Error} If the API response is not an array.
   */
  async list(): Promise<Application[]> {
    const applications = await this.client.get<unknown>("/applications");
    if (!Array.isArray(applications)) {
      throw new Error("Resposta da API /applications não é um array.");
    }
    return applications as Application[];
  }
  /**
   * Retrieves details of a specific application.
   *
   * @param appName - The name of the application to retrieve details for.
   * @returns A promise that resolves to an ApplicationDetails object.
   * @throws {Error} If there's an error fetching the application details.
   */
  async getDetails(appName: string): Promise<ApplicationDetails> {
    try {
      return await this.client.get<ApplicationDetails>(
        `/applications/${appName}`,
      );
    } catch (error) {
      console.error(`Erro ao obter detalhes do aplicativo ${appName}:`, error);
      throw error;
    }
  }
  /**
   * Sends a message to a specific application.
   *
   * @param appName - The name of the application to send the message to.
   * @param body - The message to be sent, containing an event and optional data.
   * @returns A promise that resolves when the message is successfully sent.
   */
  async sendMessage(appName: string, body: ApplicationMessage): Promise<void> {
    await this.client.post<void>(`/applications/${appName}/messages`, body);
  }
}
</file>

<file path="src/ari-client/resources/asterisk.ts">
import type { BaseClient } from "../baseClient.js";
import type {
  AsteriskInfo,
  AsteriskPing,
  Logging,
  Module,
  Variable,
} from "../interfaces";
function toQueryParams<T>(options: T): string {
  return new URLSearchParams(
    Object.entries(options as Record<string, string>)
      .filter(([, value]) => value !== undefined)
      .map(([key, value]) => [key, String(value)]),
  ).toString();
}
export class Asterisk {
  constructor(private client: BaseClient) {}
  async ping(): Promise<AsteriskPing> {
    return this.client.get<AsteriskPing>("/asterisk/ping");
  }
  /**
   * Retrieves information about the Asterisk server.
   */
  async get(): Promise<AsteriskInfo> {
    return this.client.get<AsteriskInfo>("/asterisk/info");
  }
  /**
   * Lists all loaded modules in the Asterisk server.
   */
  async list(): Promise<Module[]> {
    return this.client.get<Module[]>("/asterisk/modules");
  }
  /**
   * Manages a specific module in the Asterisk server.
   *
   * @param moduleName - The name of the module to manage.
   * @param action - The action to perform on the module: "load", "unload", or "reload".
   * @returns A promise that resolves when the action is completed successfully.
   * @throws {Error} Throws an error if the HTTP method or action is invalid.
   */
  async manage(
    moduleName: string,
    action: "load" | "unload" | "reload",
  ): Promise<void> {
    const url = `/asterisk/modules/${moduleName}`;
    switch (action) {
      case "load":
        await this.client.post<void>(`${url}?action=load`);
        break;
      case "unload":
        await this.client.delete<void>(url);
        break;
      case "reload":
        await this.client.put<void>(url, {});
        break;
      default:
        throw new Error(`Ação inválida: ${action}`);
    }
  }
  /**
   * Retrieves all configured logging channels.
   */
  async listLoggingChannels(): Promise<Logging[]> {
    return this.client.get<Logging[]>("/asterisk/logging");
  }
  /**
   * Adds or removes a log channel in the Asterisk server.
   */
  async manageLogChannel(
    logChannelName: string,
    action: "add" | "remove",
    configuration?: { type?: string; configuration?: string },
  ): Promise<void> {
    const queryParams = toQueryParams(configuration || {});
    return this.client.post<void>(
      `/asterisk/logging/${logChannelName}?action=${encodeURIComponent(action)}&${queryParams}`,
    );
  }
  /**
   * Retrieves the value of a global variable.
   */
  async getGlobalVariable(variableName: string): Promise<Variable> {
    return this.client.get<Variable>(
      `/asterisk/variables?variable=${encodeURIComponent(variableName)}`,
    );
  }
  /**
   * Sets a global variable.
   */
  async setGlobalVariable(variableName: string, value: string): Promise<void> {
    return this.client.post<void>(
      `/asterisk/variables?variable=${encodeURIComponent(variableName)}&value=${encodeURIComponent(value)}`,
    );
  }
}
</file>

<file path="src/ari-client/resources/baseResource.ts">
import { EventEmitter } from "events";
import type { AriClient } from "../ariClient"; // Referência ao seu AriClient
export abstract class BaseResource {
  protected readonly client: AriClient;
  private readonly emitter: EventEmitter;
  private readonly resourceId: string;
  private readonly listenersMap = new Map<
    string,
    ((...args: any[]) => void)[]
  >(); // 🔹 Armazena listeners para remoção futura
  protected constructor(client: AriClient, resourceId: string) {
    this.client = client;
    this.resourceId = resourceId;
    this.emitter = new EventEmitter();
  }
  /**
   * Registra um listener para eventos do recurso.
   * @param event O tipo de evento a escutar.
   * @param callback Função callback a ser chamada quando o evento ocorre.
   */
  public on<T extends string>(event: T, callback: (data: any) => void): void {
    const eventKey = `${event}-${this.resourceId}`;
    // 🔹 Verifica se o listener já foi adicionado
    const existingListeners = this.listenersMap.get(eventKey) || [];
    if (existingListeners.includes(callback)) {
      console.warn(`Listener já registrado para ${eventKey}, reutilizando.`);
      return;
    }
    console.log({
      baseEvent: "on",
      event,
      name: eventKey,
    });
    this.emitter.on(eventKey, callback);
    // 🔹 Armazena o listener para remoção futura
    if (!this.listenersMap.has(eventKey)) {
      this.listenersMap.set(eventKey, []);
    }
    this.listenersMap.get(eventKey)!.push(callback as (...args: any[]) => void);
  }
  /**
   * Remove um listener específico do evento.
   * @param event O tipo de evento.
   * @param callback Função callback a ser removida.
   */
  public removeListener<T extends string>(
    event: T,
    callback: (data: any) => void,
  ): void {
    const eventKey = `${event}-${this.resourceId}`;
    console.log({
      baseEvent: "removeListener - baseResources",
      event,
      name: eventKey,
    });
    this.emitter.off(eventKey, callback);
    // 🔹 Remove do mapa de listeners
    const storedListeners = this.listenersMap.get(eventKey) || [];
    this.listenersMap.set(
      eventKey,
      storedListeners.filter((l) => l !== callback),
    );
  }
  /**
   * Remove todos os listeners de um tipo de evento.
   * @param event O tipo de evento.
   */
  public removeAllListeners<T extends string>(event: T): void {
    const eventKey = `${event}-${this.resourceId}`;
    console.log({
      baseEvent: "removeAllListeners",
      event,
      name: eventKey,
    });
    this.emitter.removeAllListeners(eventKey);
    this.listenersMap.delete(eventKey);
  }
  /**
   * Remove todos os listeners de todos os eventos associados a este recurso.
   */
  public clearAllListeners(): void {
    console.log(`Removing all event listeners for resource ${this.resourceId}`);
    this.listenersMap.forEach((listeners, eventKey) => {
      listeners.forEach((listener) => {
        this.emitter.off(eventKey, listener as (...args: any[]) => void);
      });
    });
    this.listenersMap.clear();
    this.emitter.removeAllListeners();
  }
  /**
   * Emite um evento específico para este recurso.
   * @param event O tipo de evento.
   * @param data Os dados associados ao evento.
   */
  public emit<T extends string>(event: T, data: any): void {
    const eventKey = `${event}-${this.resourceId}`;
    if (!this.emitter.listenerCount(eventKey)) {
      console.warn(
        `No listeners registered for event ${eventKey}, skipping emit.`,
      );
      return;
    }
    console.log({
      baseEvent: "emit - baseResources",
      event,
      name: eventKey,
    });
    this.emitter.emit(eventKey, data);
  }
}
</file>

<file path="src/ari-client/resources/bridges.ts">
import { EventEmitter } from "events";
import { isAxiosError } from "axios";
import type { AriClient } from "../ariClient";
import type { BaseClient } from "../baseClient.js";
import type {
  AddChannelRequest,
  Bridge,
  BridgePlayback,
  CreateBridgeRequest,
  PlayMediaRequest,
  RemoveChannelRequest,
  WebSocketEvent,
} from "../interfaces";
import { bridgeEvents } from "../interfaces/events.types";
import { toQueryParams } from "../utils";
/**
 * Extracts an error message from various error types.
 *
 * This utility function attempts to retrieve the most relevant error message
 * from different error objects, including Axios errors and standard Error instances.
 *
 * @param error - The error object to extract the message from.
 *                Can be of type AxiosError, Error, or any unknown type.
 *
 * @returns A string containing the extracted error message.
 *          For Axios errors, it prioritizes the response data message,
 *          then falls back to the error message, and finally a default Axios error message.
 *          For standard Error instances, it returns the error message.
 *          For unknown error types, it returns a generic error message.
 */
const getErrorMessage = (error: unknown): string => {
  if (isAxiosError(error)) {
    return (
      error.response?.data?.message ||
      error.message ||
      "Um erro do axios ocorreu"
    );
  }
  if (error instanceof Error) {
    return error.message;
  }
  return "Um erro desconhecido ocorreu";
};
/**
 * Represents an instance of a Bridge that provides methods to control
 * bridges, manage event listeners, and manipulate its state.
 */
export class BridgeInstance {
  private readonly eventEmitter = new EventEmitter();
  private readonly listenersMap = new Map<
    string,
    ((...args: any[]) => void)[]
  >(); // 🔹 Guarda listeners para remoção posterior
  private bridgeData: Bridge | null = null;
  public readonly id: string;
  /**
   * Creates a new BridgeInstance.
   *
   * @param client - The AriClient instance for making API calls.
   * @param baseClient - The BaseClient instance for making HTTP requests.
   * @param bridgeId - Optional. The ID of the bridge. If not provided, a new ID will be generated.
   */
  constructor(
    private readonly client: AriClient,
    private readonly baseClient: BaseClient,
    bridgeId?: string,
  ) {
    this.id = bridgeId || `bridge-${Date.now()}`;
  }
  /**
   * Registers a listener for specific bridge events.
   *
   * @param event - The type of event to listen for.
   * @param listener - The callback function to be called when the event occurs.
   */
  /**
   * Registers a listener for specific bridge events.
   *
   * This method allows you to attach an event listener to the bridge instance for a specific event type.
   * When the specified event occurs, the provided listener function will be called with the event data.
   *
   * @template T - The specific type of WebSocketEvent to listen for.
   *                               It receives the event data as its parameter.
   * @returns {void}
   *
   * @example
   * bridge.on('BridgeCreated', (event) => {
   *
   * });
   * @param event
   * @param listener
   */
  on<T extends WebSocketEvent["type"]>(
    event: T,
    listener: (data: Extract<WebSocketEvent, { type: T }>) => void,
  ): void {
    if (!event) {
      throw new Error("Event type is required");
    }
    // 🔹 Verifica se o listener já está registrado
    const existingListeners = this.listenersMap.get(event) || [];
    if (existingListeners.includes(listener)) {
      console.warn(
        `Listener já registrado para evento ${event}, reutilizando.`,
      );
      return;
    }
    const wrappedListener = (data: WebSocketEvent) => {
      if ("bridge" in data && data.bridge?.id === this.id) {
        listener(data as Extract<WebSocketEvent, { type: T }>);
      }
    };
    this.eventEmitter.on(event, wrappedListener);
    // 🔹 Armazena o listener para futura remoção
    if (!this.listenersMap.has(event)) {
      this.listenersMap.set(event, []);
    }
    this.listenersMap
      .get(event)!
      .push(wrappedListener as (...args: any[]) => void);
  }
  /**
   * Registers a one-time listener for specific bridge events.
   *
   * @param event - The type of event to listen for.
   * @param listener - The callback function to be called when the event occurs.
   */
  once<T extends WebSocketEvent["type"]>(
    event: T,
    listener: (data: Extract<WebSocketEvent, { type: T }>) => void,
  ): void {
    if (!event) {
      throw new Error("Event type is required");
    }
    const eventKey = `${event}-${this.id}`;
    // 🔹 Verifica se já existe um listener igual para evitar duplicação
    const existingListeners = this.listenersMap.get(eventKey) || [];
    if (existingListeners.includes(listener)) {
      console.warn(
        `One-time listener já registrado para evento ${eventKey}, reutilizando.`,
      );
      return;
    }
    const wrappedListener = (data: WebSocketEvent) => {
      if ("bridge" in data && data.bridge?.id === this.id) {
        listener(data as Extract<WebSocketEvent, { type: T }>);
        // 🔹 Remove automaticamente o listener após a primeira execução
        this.off(event, wrappedListener);
      }
    };
    this.eventEmitter.once(event, wrappedListener);
    // 🔹 Armazena o listener para futura remoção
    if (!this.listenersMap.has(eventKey)) {
      this.listenersMap.set(eventKey, []);
    }
    this.listenersMap
      .get(eventKey)!
      .push(wrappedListener as (...args: any[]) => void);
  }
  /**
   * Removes event listener(s) from the bridge.
   *
   * @param event - The type of event to remove listeners for.
   * @param listener - Optional. The specific listener to remove. If not provided, all listeners for the event will be removed.
   */
  off<T extends WebSocketEvent["type"]>(
    event: T,
    listener?: (data: Extract<WebSocketEvent, { type: T }>) => void,
  ): void {
    if (!event) {
      throw new Error("Event type is required");
    }
    if (listener) {
      this.eventEmitter.off(event, listener);
      const storedListeners = this.listenersMap.get(event) || [];
      this.listenersMap.set(
        event,
        storedListeners.filter((l) => l !== listener),
      );
    } else {
      this.eventEmitter.removeAllListeners(event);
      this.listenersMap.delete(event);
    }
  }
  /**
   * Cleans up the BridgeInstance, resetting its state and clearing resources.
   */
  public cleanup(): void {
    // Limpar dados do bridge
    this.bridgeData = null;
    // Remover todos os listeners
    this.removeAllListeners();
    // Limpar o map de listeners
    this.listenersMap.clear();
    console.log(`Bridge instance ${this.id} cleaned up`);
  }
  /**
   * Emits an event if it corresponds to the current bridge.
   *
   * @param event - The WebSocketEvent to emit.
   */
  emitEvent(event: WebSocketEvent): void {
    if (!event) {
      console.warn("Invalid event received");
      return;
    }
    if ("bridge" in event && event.bridge?.id === this.id) {
      this.eventEmitter.emit(event.type, event);
    }
  }
  /**
   * Removes all event listeners from this bridge instance.
   */
  removeAllListeners(): void {
    console.log(`Removing all event listeners for bridge ${this.id}`);
    this.listenersMap.forEach((listeners, event) => {
      listeners.forEach((listener) => {
        this.eventEmitter.off(
          event as string,
          listener as (...args: any[]) => void,
        );
      });
    });
    this.listenersMap.clear();
    this.eventEmitter.removeAllListeners();
  }
  /**
   * Retrieves the current details of the bridge.
   *
   * @returns A Promise that resolves to the Bridge object containing the current details.
   * @throws An error if the retrieval fails.
   */
  async get(): Promise<Bridge> {
    try {
      if (!this.id) {
        throw new Error("No bridge associated with this instance");
      }
      this.bridgeData = await this.baseClient.get<Bridge>(
        `/bridges/${this.id}`,
      );
      return this.bridgeData;
    } catch (error: unknown) {
      const message = getErrorMessage(error);
      console.error(`Error retrieving details for bridge ${this.id}:`, message);
      throw new Error(`Failed to get bridge details: ${message}`);
    }
  }
  /**
   * Adds channels to the bridge.
   *
   * @param request - The AddChannelRequest object containing the channels to add.
   * @throws An error if the operation fails.
   */
  async add(request: AddChannelRequest): Promise<void> {
    try {
      const queryParams = toQueryParams({
        channel: Array.isArray(request.channel)
          ? request.channel.join(",")
          : request.channel,
        ...(request.role && { role: request.role }),
      });
      await this.baseClient.post<void>(
        `/bridges/${this.id}/addChannel?${queryParams}`,
      );
    } catch (error: unknown) {
      const message = getErrorMessage(error);
      console.error(`Error adding channels to bridge ${this.id}:`, message);
      throw new Error(`Failed to add channels: ${message}`);
    }
  }
  /**
   * Removes channels from the bridge.
   *
   * @param request - The RemoveChannelRequest object containing the channels to remove.
   * @throws An error if the operation fails.
   */
  async remove(request: RemoveChannelRequest): Promise<void> {
    try {
      const queryParams = toQueryParams({
        channel: Array.isArray(request.channel)
          ? request.channel.join(",")
          : request.channel,
      });
      await this.baseClient.post<void>(
        `/bridges/${this.id}/removeChannel?${queryParams}`,
      );
    } catch (error: unknown) {
      const message = getErrorMessage(error);
      console.error(`Error removing channels from bridge ${this.id}:`, message);
      throw new Error(`Failed to remove channels: ${message}`);
    }
  }
  /**
   * Plays media on the bridge.
   *
   * @param request - The PlayMediaRequest object containing the media details to play.
   * @returns A Promise that resolves to a BridgePlayback object.
   * @throws An error if the operation fails.
   */
  async playMedia(request: PlayMediaRequest): Promise<BridgePlayback> {
    try {
      const queryParams = new URLSearchParams({
        ...(request.lang && { lang: request.lang }),
        ...(request.offsetms && { offsetms: request.offsetms.toString() }),
        ...(request.skipms && { skipms: request.skipms.toString() }),
        ...(request.playbackId && { playbackId: request.playbackId }),
      }).toString();
      const result = await this.baseClient.post<BridgePlayback>(
        `/bridges/${this.id}/play?${queryParams}`,
        { media: request.media },
      );
      return result;
    } catch (error: unknown) {
      const message = getErrorMessage(error);
      console.error(`Error playing media on bridge ${this.id}:`, message);
      throw new Error(`Failed to play media: ${message}`);
    }
  }
  /**
   * Stops media playback on the bridge.
   *
   * @param playbackId - The ID of the playback to stop.
   * @throws An error if the operation fails.
   */
  async stopPlayback(playbackId: string): Promise<void> {
    try {
      await this.baseClient.delete<void>(
        `/bridges/${this.id}/play/${playbackId}`,
      );
    } catch (error: unknown) {
      const message = getErrorMessage(error);
      console.error(`Error stopping playback on bridge ${this.id}:`, message);
      throw new Error(`Failed to stop playback: ${message}`);
    }
  }
  /**
   * Sets the video source for the bridge.
   *
   * @param channelId - The ID of the channel to set as the video source.
   * @throws An error if the operation fails.
   */
  async setVideoSource(channelId: string): Promise<void> {
    try {
      await this.baseClient.post<void>(
        `/bridges/${this.id}/videoSource/${channelId}`,
      );
    } catch (error: unknown) {
      const message = getErrorMessage(error);
      console.error(
        `Error setting video source for bridge ${this.id}:`,
        message,
      );
      throw new Error(`Failed to set video source: ${message}`);
    }
  }
  /**
   * Removes the video source from the bridge.
   *
   * @throws An error if the operation fails.
   */
  async clearVideoSource(): Promise<void> {
    try {
      await this.baseClient.delete<void>(`/bridges/${this.id}/videoSource`);
    } catch (error: unknown) {
      const message = getErrorMessage(error);
      console.error(
        `Error removing video source from bridge ${this.id}:`,
        message,
      );
      throw new Error(`Failed to remove video source: ${message}`);
    }
  }
  /**
   * Checks if the bridge has listeners for a specific event.
   *
   * @param event - The event type to check for listeners.
   * @returns A boolean indicating whether there are listeners for the event.
   */
  hasListeners(event: string): boolean {
    return this.eventEmitter.listenerCount(event) > 0;
  }
  /**
   * Retrieves the current bridge data without making an API call.
   *
   * @returns The current Bridge object or null if no data is available.
   */
  getCurrentData(): Bridge | null {
    return this.bridgeData;
  }
}
export class Bridges {
  private readonly bridgeInstances = new Map<string, BridgeInstance>();
  private eventQueue = new Map<string, NodeJS.Timeout>();
  constructor(
    private readonly baseClient: BaseClient,
    private readonly client: AriClient,
  ) {}
  /**
   * Creates or retrieves a Bridge instance.
   *
   * This method manages the creation and retrieval of BridgeInstance objects.
   * If an ID is provided and an instance with that ID already exists, it returns the existing instance.
   * If an ID is provided but no instance exists, it creates a new instance with that ID.
   * If no ID is provided, it creates a new instance with a generated ID.
   *
   * @param {Object} params - The parameters for creating or retrieving a Bridge instance.
   * @param {string} [params.id] - Optional. The ID of the Bridge instance to create or retrieve.
   *
   * @returns {BridgeInstance} A BridgeInstance object, either newly created or retrieved from existing instances.
   *
   * @throws {Error} If there's an error in creating or retrieving the Bridge instance.
   */
  Bridge({ id }: { id?: string }): BridgeInstance {
    try {
      if (!id) {
        const instance = new BridgeInstance(this.client, this.baseClient);
        this.bridgeInstances.set(instance.id, instance);
        return instance;
      }
      if (!this.bridgeInstances.has(id)) {
        const instance = new BridgeInstance(this.client, this.baseClient, id);
        this.bridgeInstances.set(id, instance);
        return instance;
      }
      return this.bridgeInstances.get(id)!;
    } catch (error: unknown) {
      const message = getErrorMessage(error);
      console.warn(`Error creating/retrieving bridge instance:`, message);
      throw new Error(`Failed to manage bridge instance: ${message}`);
    }
  }
  /**
   * Removes all bridge instances and cleans up their resources.
   * This method ensures proper cleanup of all bridges and their associated listeners.
   */
  public remove(): void {
    // Salvar os IDs antes de começar a limpeza
    const bridgeIds = Array.from(this.bridgeInstances.keys());
    for (const bridgeId of bridgeIds) {
      try {
        const instance = this.bridgeInstances.get(bridgeId);
        if (instance) {
          instance.cleanup(); // Usar o novo método cleanup
          this.bridgeInstances.delete(bridgeId);
          console.log(`Bridge instance ${bridgeId} removed and cleaned up`);
        }
      } catch (error) {
        console.error(`Error cleaning up bridge ${bridgeId}:`, error);
      }
    }
    // Garantir que o map está vazio
    this.bridgeInstances.clear();
    console.log("All bridge instances have been removed and cleaned up");
  }
  /**
   * Removes a bridge instance from the collection of managed bridges.
   *
   * This function removes the specified bridge instance, cleans up its event listeners,
   * and logs the removal. If the bridge instance doesn't exist, it logs a warning.
   *
   * @param {string} bridgeId - The unique identifier of the bridge instance to be removed.
   * @throws {Error} Throws an error if the bridgeId is not provided.
   * @returns {void}
   */
  public removeBridgeInstance(bridgeId: string): void {
    if (!bridgeId) {
      throw new Error("Bridge ID is required");
    }
    const instance = this.bridgeInstances.get(bridgeId);
    if (instance) {
      try {
        instance.cleanup();
        this.bridgeInstances.delete(bridgeId);
        console.log(`Bridge instance ${bridgeId} removed from memory`);
      } catch (error) {
        console.error(`Error removing bridge instance ${bridgeId}:`, error);
        throw error;
      }
    } else {
      console.warn(`Attempt to remove non-existent instance: ${bridgeId}`);
    }
  }
  /**
   * Propagates a WebSocket event to a specific bridge instance.
   *
   * This function checks if the received event is valid and related to a bridge,
   * then emits the event to the corresponding bridge instance if it exists.
   *
   * @param {WebSocketEvent} event - The WebSocket event to be propagated.
   *                                 This should be an object containing information about the event,
   *                                 including the bridge ID and event type.
   *
   * @returns {void}
   *
   * @remarks
   * - If the event is invalid (null or undefined), a warning is logged and the function returns early.
   * - The function checks if the event is bridge-related and if the event contains a valid bridge ID.
   * - If a matching bridge instance is found, the event is emitted to that instance.
   * - If no matching bridge instance is found, a warning is logged.
   */
  public propagateEventToBridge(event: WebSocketEvent): void {
    if (!event || !("bridge" in event) || !event.bridge?.id) {
      console.warn("Invalid WebSocket event received");
      return;
    }
    const key = `${event.type}-${event.bridge.id}`;
    const existing = this.eventQueue.get(key);
    if (existing) {
      clearTimeout(existing);
    }
    this.eventQueue.set(
      key,
      setTimeout(() => {
        const instance = this.bridgeInstances.get(event.bridge!.id!);
        if (instance) {
          instance.emitEvent(event);
        } else {
          console.warn(
            `No instance found for bridge ${event.bridge!.id}. Event ignored.`,
          );
        }
        this.eventQueue.delete(key);
      }, 100),
    );
  }
  /**
   * Performs a cleanup of the Bridges instance, clearing all event queues and removing all bridge instances.
   *
   * This method is responsible for:
   * 1. Clearing all pending timeouts in the event queue.
   * 2. Removing all bridge instances managed by this Bridges object.
   *
   * It should be called when the Bridges instance is no longer needed or before reinitializing
   * to ensure all resources are properly released.
   *
   * @returns {void}
   */
  public cleanup(): void {
    // Limpar event queue
    this.eventQueue.forEach((timeout) => clearTimeout(timeout));
    this.eventQueue.clear();
    // Limpar todas as instâncias
    this.remove();
  }
  /**
   * Lists all active bridges in the system.
   *
   * This asynchronous function retrieves a list of all currently active bridges
   * by making a GET request to the "/bridges" endpoint using the base client.
   *
   * @returns {Promise<Bridge[]>} A promise that resolves to an array of Bridge objects.
   *                              Each Bridge object represents an active bridge in the system.
   *
   * @throws {Error} If there's an error in fetching the bridges or if the request fails.
   *
   * @example
   * try {
   *   const bridges = await bridgesInstance.list();
   *
   * } catch (error) {
   *   console.error('Failed to fetch bridges:', error);
   * }
   */
  async list(): Promise<Bridge[]> {
    return this.baseClient.get<Bridge[]>("/bridges");
  }
  /**
   * Creates a new bridge in the system.
   *
   * This asynchronous function sends a POST request to create a new bridge
   * using the provided configuration details.
   *
   * @param request - The configuration details for creating the new bridge.
   * @param request.type - The type of bridge to create (e.g., 'mixing', 'holding').
   * @param request.name - Optional. A custom name for the bridge.
   * @param request.bridgeId - Optional. A specific ID for the bridge. If not provided, one will be generated.
   *
   * @returns A Promise that resolves to a Bridge object representing the newly created bridge.
   *          The Bridge object contains details such as id, technology, bridge_type, bridge_class, channels, etc.
   *
   * @throws Will throw an error if the bridge creation fails or if there's a network issue.
   */
  async createBridge(request: CreateBridgeRequest): Promise<Bridge> {
    return this.baseClient.post<Bridge>("/bridges", request);
  }
  /**
   * Retrieves detailed information about a specific bridge.
   *
   * This asynchronous function fetches the complete details of a bridge
   * identified by its unique ID. It makes a GET request to the ARI endpoint
   * for the specified bridge.
   *
   * @param bridgeId - The unique identifier of the bridge to retrieve details for.
   *                   This should be a string that uniquely identifies the bridge in the system.
   *
   * @returns A Promise that resolves to a Bridge object containing all the details
   *          of the specified bridge. This includes information such as the bridge's
   *          ID, type, channels, and other relevant properties.
   *
   * @throws Will throw an error if the bridge cannot be found, if there's a network issue,
   *         or if the server responds with an error.
   */
  async get(bridgeId: string): Promise<Bridge> {
    return this.baseClient.get<Bridge>(`/bridges/${bridgeId}`);
  }
  /**
   * Destroys (deletes) a specific bridge in the system.
   *
   * This asynchronous function sends a DELETE request to remove a bridge
   * identified by its unique ID. Once destroyed, the bridge and all its
   * associated resources are permanently removed from the system.
   *
   * @param bridgeId - The unique identifier of the bridge to be destroyed.
   *                   This should be a string that uniquely identifies the bridge in the system.
   *
   * @returns A Promise that resolves to void when the bridge is successfully destroyed.
   *          If the operation is successful, the bridge no longer exists in the system.
   *
   * @throws Will throw an error if the bridge cannot be found, if there's a network issue,
   *         or if the server responds with an error during the deletion process.
   */
  async destroy(bridgeId: string): Promise<void> {
    return this.baseClient.delete<void>(`/bridges/${bridgeId}`);
  }
  /**
   * Adds one or more channels to a specified bridge.
   *
   * This asynchronous function sends a POST request to add channels to an existing bridge.
   * It can handle adding a single channel or multiple channels in one operation.
   *
   * @param bridgeId - The unique identifier of the bridge to which channels will be added.
   * @param request - An object containing the details of the channel(s) to be added.
   * @param request.channel - A single channel ID or an array of channel IDs to add to the bridge.
   * @param request.role - Optional. Specifies the role of the channel(s) in the bridge.
   *
   * @returns A Promise that resolves to void when the operation is successful.
   *
   * @throws Will throw an error if the request fails, such as if the bridge doesn't exist
   *         or if there's a network issue.
   */
  async addChannels(
    bridgeId: string,
    request: AddChannelRequest,
  ): Promise<void> {
    const queryParams = toQueryParams({
      channel: Array.isArray(request.channel)
        ? request.channel.join(",")
        : request.channel,
      ...(request.role && { role: request.role }),
    });
    await this.baseClient.post<void>(
      `/bridges/${bridgeId}/addChannel?${queryParams}`,
    );
  }
  /**
   * Removes one or more channels from a specified bridge.
   *
   * This asynchronous function sends a POST request to remove channels from an existing bridge.
   * It can handle removing a single channel or multiple channels in one operation.
   *
   * @param bridgeId - The unique identifier of the bridge from which channels will be removed.
   * @param request - An object containing the details of the channel(s) to be removed.
   * @param request.channel - A single channel ID or an array of channel IDs to remove from the bridge.
   *
   * @returns A Promise that resolves to void when the operation is successful.
   *
   * @throws Will throw an error if the request fails, such as if the bridge doesn't exist,
   *         if the channels are not in the bridge, or if there's a network issue.
   */
  async removeChannels(
    bridgeId: string,
    request: RemoveChannelRequest,
  ): Promise<void> {
    const queryParams = toQueryParams({
      channel: Array.isArray(request.channel)
        ? request.channel.join(",")
        : request.channel,
    });
    await this.baseClient.post<void>(
      `/bridges/${bridgeId}/removeChannel?${queryParams}`,
    );
  }
  /**
   * Plays media on a specified bridge.
   *
   * This asynchronous function initiates media playback on a bridge identified by its ID.
   * It allows for customization of the playback through various options in the request.
   *
   * @param bridgeId - The unique identifier of the bridge on which to play the media.
   * @param request - An object containing the media playback request details.
   * @param request.media - The media to be played (e.g., sound file, URL).
   * @param request.lang - Optional. The language of the media content.
   * @param request.offsetms - Optional. The offset in milliseconds to start playing from.
   * @param request.skipms - Optional. The number of milliseconds to skip before playing.
   * @param request.playbackId - Optional. A custom ID for the playback session.
   *
   * @returns A Promise that resolves to a BridgePlayback object, containing details about the initiated playback.
   *
   * @throws Will throw an error if the playback request fails or if there's a network issue.
   */
  async playMedia(
    bridgeId: string,
    request: PlayMediaRequest,
  ): Promise<BridgePlayback> {
    const queryParams = toQueryParams({
      ...(request.lang && { lang: request.lang }),
      ...(request.offsetms && { offsetms: request.offsetms.toString() }),
      ...(request.skipms && { skipms: request.skipms.toString() }),
      ...(request.playbackId && { playbackId: request.playbackId }),
    });
    return this.baseClient.post<BridgePlayback>(
      `/bridges/${bridgeId}/play?${queryParams}`,
      { media: request.media },
    );
  }
  /**
   * Stops media playback on a specified bridge.
   *
   * This asynchronous function sends a DELETE request to stop the playback of media
   * on a bridge identified by its ID and a specific playback session.
   *
   * @param bridgeId - The unique identifier of the bridge where the playback is to be stopped.
   * @param playbackId - The unique identifier of the playback session to be stopped.
   *
   * @returns A Promise that resolves to void when the playback is successfully stopped.
   *
   * @throws Will throw an error if the request fails, such as if the bridge or playback session
   *         doesn't exist, or if there's a network issue.
   */
  async stopPlayback(bridgeId: string, playbackId: string): Promise<void> {
    await this.baseClient.delete<void>(
      `/bridges/${bridgeId}/play/${playbackId}`,
    );
  }
  /**
   * Sets the video source for a specified bridge.
   *
   * This asynchronous function configures a channel as the video source for a given bridge.
   * It sends a POST request to the ARI endpoint to update the bridge's video source.
   *
   * @param bridgeId - The unique identifier of the bridge for which to set the video source.
   * @param channelId - The unique identifier of the channel to be set as the video source.
   *
   * @returns A Promise that resolves to void when the video source is successfully set.
   *
   * @throws Will throw an error if the request fails, such as if the bridge or channel
   *         doesn't exist, or if there's a network issue.
   */
  async setVideoSource(bridgeId: string, channelId: string): Promise<void> {
    const queryParams = toQueryParams({ channelId });
    await this.baseClient.post<void>(
      `/bridges/${bridgeId}/videoSource?${queryParams}`,
    );
  }
  /**
   * Clears the video source for a specified bridge.
   *
   * This asynchronous function removes the currently set video source from a bridge.
   * It sends a DELETE request to the ARI endpoint to clear the video source configuration.
   *
   * @param bridgeId - The unique identifier of the bridge from which to clear the video source.
   *                   This should be a string that uniquely identifies the bridge in the system.
   *
   * @returns A Promise that resolves to void when the video source is successfully cleared.
   *          If the operation is successful, the bridge will no longer have a designated video source.
   *
   * @throws Will throw an error if the request fails, such as if the bridge doesn't exist,
   *         if there's no video source set, or if there's a network issue.
   */
  async clearVideoSource(bridgeId: string): Promise<void> {
    await this.baseClient.delete<void>(`/bridges/${bridgeId}/videoSource`);
  }
  /**
   * Retrieves the count of active bridge instances.
   *
   * This function returns the total number of bridge instances currently
   * managed by the Bridges class. It provides a quick way to check how many
   * active bridges are present in the system.
   *
   * @returns {number} The count of active bridge instances.
   */
  getInstanceCount(): number {
    return this.bridgeInstances.size;
  }
  /**
   * Checks if a bridge instance exists in the collection of managed bridges.
   *
   * This function verifies whether a bridge instance with the specified ID
   * is currently being managed by the Bridges class.
   *
   * @param bridgeId - The unique identifier of the bridge instance to check.
   *                   This should be a string that uniquely identifies the bridge in the system.
   *
   * @returns A boolean value indicating whether the bridge instance exists.
   *          Returns true if the bridge instance is found, false otherwise.
   */
  hasInstance(bridgeId: string): boolean {
    return this.bridgeInstances.has(bridgeId);
  }
  /**
   * Retrieves all active bridge instances currently managed by the Bridges class.
   *
   * This method provides a way to access all the BridgeInstance objects that are
   * currently active and being managed. It returns a new Map to prevent direct
   * modification of the internal bridgeInstances collection.
   *
   * @returns A new Map object containing all active bridge instances, where the keys
   *          are the bridge IDs (strings) and the values are the corresponding
   *          BridgeInstance objects. If no bridges are active, an empty Map is returned.
   */
  getAllInstances(): Map<string, BridgeInstance> {
    return new Map(this.bridgeInstances);
  }
}
</file>

<file path="src/ari-client/resources/channels.ts">
import { EventEmitter } from "events";
import { isAxiosError } from "axios";
import { v4 as uuidv4 } from "uuid";
import type { AriClient } from "../ariClient";
import type { BaseClient } from "../baseClient.js";
import type {
  Channel,
  ChannelPlayback,
  ChannelVar,
  ExternalMediaOptions,
  OriginateRequest,
  PlaybackOptions,
  RTPStats,
  RecordingOptions,
  SnoopOptions,
  WebSocketEvent,
} from "../interfaces";
import { toQueryParams } from "../utils";
import type { PlaybackInstance } from "./playbacks";
/**
 * Utility function to extract error message
 */
const getErrorMessage = (error: unknown): string => {
  if (isAxiosError(error)) {
    return (
      error.response?.data?.message ||
      error.message ||
      "An axios error occurred"
    );
  }
  if (error instanceof Error) {
    return error.message;
  }
  return "An unknown error occurred";
};
/**
 * Represents an instance of a communication channel managed by the AriClient.
 */
export class ChannelInstance {
  private readonly eventEmitter = new EventEmitter();
  private channelData: Channel | null = null;
  private readonly listenersMap = new Map<
    string,
    ((...args: any[]) => void)[]
  >(); // 🔹 Guarda listeners para remoção posterior
  public readonly id: string;
  constructor(
    private readonly client: AriClient,
    private readonly baseClient: BaseClient,
    channelId?: string,
  ) {
    this.id = channelId || `channel-${Date.now()}`;
  }
  /**
   * Registers an event listener for specific channel events
   */
  on<T extends WebSocketEvent["type"]>(
    event: T,
    listener: (data: Extract<WebSocketEvent, { type: T }>) => void,
  ): void {
    if (!event) {
      throw new Error("Event type is required");
    }
    // 🔹 Verifica se o listener já está registrado para evitar duplicação
    const existingListeners = this.listenersMap.get(event) || [];
    if (existingListeners.includes(listener)) {
      console.warn(
        `Listener já registrado para evento ${event}, reutilizando.`,
      );
      return;
    }
    const wrappedListener = (data: WebSocketEvent) => {
      if ("channel" in data && data.channel?.id === this.id) {
        listener(data as Extract<WebSocketEvent, { type: T }>);
      }
    };
    this.eventEmitter.on(event, wrappedListener);
    // 🔹 Armazena o listener para futura remoção
    if (!this.listenersMap.has(event)) {
      this.listenersMap.set(event, []);
    }
    this.listenersMap
      .get(event)!
      .push(wrappedListener as (...args: any[]) => void);
  }
  /**
   * Registers a one-time event listener
   */
  once<T extends WebSocketEvent["type"]>(
    event: T,
    listener: (data: Extract<WebSocketEvent, { type: T }>) => void,
  ): void {
    if (!event) {
      throw new Error("Event type is required");
    }
    const eventKey = `${event}-${this.id}`;
    // 🔹 Verifica se já existe um listener igual para evitar duplicação
    const existingListeners = this.listenersMap.get(eventKey) || [];
    if (existingListeners.includes(listener)) {
      console.warn(
        `One-time listener já registrado para evento ${eventKey}, reutilizando.`,
      );
      return;
    }
    const wrappedListener = (data: WebSocketEvent) => {
      if ("channel" in data && data.channel?.id === this.id) {
        listener(data as Extract<WebSocketEvent, { type: T }>);
        // 🔹 Remove automaticamente o listener após a primeira execução
        this.off(event, wrappedListener);
      }
    };
    this.eventEmitter.once(event, wrappedListener);
    // 🔹 Armazena o listener para futura remoção
    if (!this.listenersMap.has(eventKey)) {
      this.listenersMap.set(eventKey, []);
    }
    this.listenersMap
      .get(eventKey)!
      .push(wrappedListener as (...args: any[]) => void);
  }
  /**
   * Removes event listener(s) for a specific WebSocket event type.
   * If a specific listener is provided, only that listener is removed.
   * Otherwise, all listeners for the given event type are removed.
   *
   * @param {T} event - The type of WebSocket event to remove listener(s) for
   * @param {(data: WebSocketEvent) => void} [listener] - Optional specific listener to remove
   * @throws {Error} If no event type is provided
   */
  off<T extends WebSocketEvent["type"]>(
    event: T,
    listener?: (data: Extract<WebSocketEvent, { type: T }>) => void,
  ): void {
    if (!event) {
      throw new Error("Event type is required");
    }
    if (listener) {
      this.eventEmitter.off(event, listener);
      const storedListeners = this.listenersMap.get(event) || [];
      this.listenersMap.set(
        event,
        storedListeners.filter((l) => l !== listener),
      );
    } else {
      this.eventEmitter.removeAllListeners(event);
      this.listenersMap.delete(event);
    }
  }
  /**
   * Cleans up the ChannelInstance, resetting its state and clearing resources.
   */
  public cleanup(): void {
    // Limpar dados do canal
    this.channelData = null;
    // Remover todos os listeners
    this.removeAllListeners();
    // Limpar o map de listeners
    this.listenersMap.clear();
    console.log(`Channel instance ${this.id} cleaned up`);
  }
  /**
   * Emits an event if it matches the current channel
   */
  emitEvent(event: WebSocketEvent): void {
    if (!event) {
      console.warn("Received invalid event");
      return;
    }
    if ("channel" in event && event.channel?.id === this.id) {
      this.eventEmitter.emit(event.type, event);
    }
  }
  /**
   * Removes all event listeners associated with the current instance.
   * This ensures that there are no lingering event handlers for the channel.
   *
   * @return {void} This method does not return a value.
   */
  removeAllListeners(): void {
    console.log(`Removing all event listeners for channel ${this.id}`);
    this.listenersMap.forEach((listeners, event) => {
      listeners.forEach((listener) => {
        this.eventEmitter.off(
          event as string,
          listener as (...args: any[]) => void,
        );
      });
    });
    this.listenersMap.clear();
    this.eventEmitter.removeAllListeners();
  }
  /**
   * Answers the channel
   */
  async answer(): Promise<void> {
    try {
      await this.baseClient.post<void>(`/channels/${this.id}/answer`);
    } catch (error: unknown) {
      const message = getErrorMessage(error);
      console.error(`Error answering channel ${this.id}:`, message);
      throw new Error(`Failed to answer channel: ${message}`);
    }
  }
  /**
   * Originates a new channel
   *
   * @param data - Channel origination configuration
   * @returns Promise resolving to the created channel
   * @throws Error if channel already exists or origination fails
   */
  async originate(data: OriginateRequest): Promise<Channel> {
    if (this.channelData) {
      throw new Error("Channel has already been created");
    }
    try {
      this.channelData = await this.baseClient.post<Channel, OriginateRequest>(
        "/channels",
        data,
      );
      return this.channelData;
    } catch (error: unknown) {
      const message = getErrorMessage(error);
      console.error(`Error originating channel:`, message);
      throw new Error(`Failed to originate channel: ${message}`);
    }
  }
  /**
   * Continues the execution of a dialplan for the current channel.
   *
   * @param {string} [context] - The dialplan context to continue execution in, if specified.
   * @param {string} [extension] - The dialplan extension to proceed with, if provided.
   * @param {number} [priority] - The priority within the dialplan extension to resume at, if specified.
   * @param {string} [label] - The label to start from within the dialplan, if given.
   * @return {Promise<void>} Resolves when the dialplan is successfully continued.
   */
  async continueDialplan(
    context?: string,
    extension?: string,
    priority?: number,
    label?: string,
  ): Promise<void> {
    try {
      if (!this.channelData) {
        this.channelData = await this.getDetails();
      }
      await this.baseClient.post<void>(`/channels/${this.id}/continue`, {
        context,
        extension,
        priority,
        label,
      });
    } catch (error: unknown) {
      const message = getErrorMessage(error);
      console.error(`Error continuing dialplan for channel ${this.id}:`, message);
      throw new Error(`Failed to continue dialplan: ${message}`);
    }
  }
  /**
   * Initiates a snoop operation on this channel with the provided options.
   * Snooping allows you to listen in or interact with an existing call.
   *
   * @param {SnoopOptions} options - Configuration options for the snooping operation.
   * @return {Promise<Channel>} A promise that resolves to the snooped channel data.
   * @throws {Error} If the channel is not initialized or if snooping fails.
   */
  async snoop(options: SnoopOptions): Promise<Channel> {
    try {
      if (!this.channelData) {
        this.channelData = await this.getDetails();
      }
      const queryParams = toQueryParams(options);
      return await this.baseClient.post<Channel>(
        `/channels/${this.id}/snoop?${queryParams}`
      );
    } catch (error: unknown) {
      const message = getErrorMessage(error);
      console.error(`Error snooping on channel ${this.id}:`, message);
      throw new Error(`Failed to snoop channel: ${message}`);
    }
  }
  /**
   * Initiates a snoop operation on this channel with a specific snoop ID.
   *
   * @param {string} snoopId - The unique identifier for the snoop operation.
   * @param {SnoopOptions} options - Configuration options for the snooping operation.
   * @return {Promise<Channel>} A promise that resolves to the snooped channel data.
   * @throws {Error} If the channel is not initialized or if snooping fails.
   */
  async snoopWithId(snoopId: string, options: SnoopOptions): Promise<Channel> {
    try {
      if (!this.channelData) {
        this.channelData = await this.getDetails();
      }
      const queryParams = toQueryParams(options);
      return await this.baseClient.post<Channel>(
        `/channels/${this.id}/snoop/${snoopId}?${queryParams}`
      );
    } catch (error: unknown) {
      const message = getErrorMessage(error);
      console.error(`Error snooping with ID on channel ${this.id}:`, message);
      throw new Error(`Failed to snoop channel with ID: ${message}`);
    }
  }
  /**
   * Plays media on the channel
   */
  async play(
    options: { media: string; lang?: string },
    playbackId?: string,
  ): Promise<PlaybackInstance> {
    if (!options.media) {
      throw new Error("Media URL is required");
    }
    try {
      if (!this.channelData) {
        this.channelData = await this.getDetails();
      }
      const playback = this.client.Playback(playbackId || uuidv4());
      await this.baseClient.post<void>(
        `/channels/${this.id}/play/${playback.id}`,
        options,
      );
      return playback;
    } catch (error: unknown) {
      const message = getErrorMessage(error);
      console.error(`Error playing media on channel ${this.id}:`, message);
      throw new Error(`Failed to play media: ${message}`);
    }
  }
  /**
   * Gets the current channel details
   */
  async getDetails(): Promise<Channel> {
    try {
      if (this.channelData) {
        return this.channelData;
      }
      if (!this.id) {
        throw new Error("No channel ID associated with this instance");
      }
      const details = await this.baseClient.get<Channel>(
        `/channels/${this.id}`,
      );
      this.channelData = details;
      return details;
    } catch (error: unknown) {
      const message = getErrorMessage(error);
      console.error(
        `Error retrieving channel details for ${this.id}:`,
        message,
      );
      throw new Error(`Failed to get channel details: ${message}`);
    }
  }
  /**
   * Checks if the channel has any listeners for a specific event
   */
  hasListeners(event: string): boolean {
    return this.eventEmitter.listenerCount(event) > 0;
  }
  /**
   * Gets the count of listeners for a specific event
   */
  getListenerCount(event: string): number {
    return this.eventEmitter.listenerCount(event);
  }
  /**
   * Fetches a specific channel variable.
   *
   * @param {string} variable - The name of the variable to retrieve. This parameter is required.
   * @return {Promise<ChannelVar>} A promise that resolves with the value of the requested channel variable.
   * @throws {Error} If the 'variable' parameter is not provided.
   */
  async getVariable(variable: string): Promise<ChannelVar> {
    if (!variable) {
      throw new Error("The 'variable' parameter is required.");
    }
    return this.baseClient.get<ChannelVar>(
      `/channels/${this.id}/variable?variable=${encodeURIComponent(variable)}`,
    );
  }
  /**
   * Terminates the active call associated with the current channel.
   * This method ensures that channel details are initialized before attempting to hang up.
   * If the channel ID is invalid or cannot be determined, an error is thrown.
   *
   * @return {Promise<void>} A promise that resolves when the call is successfully terminated.
   */
  async hangup(): Promise<void> {
    if (!this.channelData) {
      this.channelData = await this.getDetails();
    }
    if (!this.channelData?.id) {
      throw new Error("Não foi possível inicializar o canal. ID inválido.");
    }
    await this.baseClient.delete(`/channels/${this.channelData.id}`);
  }
  /**
   * Plays media on the specified channel using the provided media URL and optional playback options.
   *
   * @param {string} media - The URL or identifier of the media to be played.
   * @param {PlaybackOptions} [options] - Optional playback settings such as volume, playback speed, etc.
   * @return {Promise<ChannelPlayback>} A promise that resolves with the playback details for the channel.
   * @throws {Error} Throws an error if the channel has not been created.
   */
  async playMedia(
    media: string,
    options?: PlaybackOptions,
  ): Promise<ChannelPlayback> {
    if (!this.channelData) {
      throw new Error("O canal ainda não foi criado.");
    }
    const queryParams = options
      ? `?${new URLSearchParams(options as Record<string, string>).toString()}`
      : "";
    return this.baseClient.post<ChannelPlayback>(
      `/channels/${this.channelData.id}/play${queryParams}`,
      { media },
    );
  }
  /**
   * Stops the playback for the given playback ID.
   *
   * @param {string} playbackId - The unique identifier for the playback to be stopped.
   * @return {Promise<void>} A promise that resolves when the playback is successfully stopped.
   * @throws {Error} Throws an error if the instance is not associated with a channel.
   */
  async stopPlayback(playbackId: string): Promise<void> {
    if (!this.channelData?.id) {
      throw new Error("Canal não associado a esta instância.");
    }
    await this.baseClient.delete<void>(
      `/channels/${this.channelData.id}/play/${playbackId}`,
    );
  }
  /**
   * Pauses the playback of the specified media on a channel.
   *
   * @param {string} playbackId - The unique identifier of the playback to be paused.
   * @return {Promise<void>} A promise that resolves when the playback has been successfully paused.
   * @throws {Error} Throws an error if the channel is not associated with the current instance.
   */
  async pausePlayback(playbackId: string): Promise<void> {
    if (!this.channelData?.id) {
      throw new Error("Canal não associado a esta instância.");
    }
    await this.baseClient.post<void>(
      `/channels/${this.channelData.id}/play/${playbackId}/pause`,
    );
  }
  /**
   * Resumes playback of the specified playback session on the associated channel.
   *
   * @param {string} playbackId - The unique identifier of the playback session to be resumed.
   * @return {Promise<void>} A promise that resolves when the playback has been successfully resumed.
   * @throws {Error} Throws an error if the channel is not associated with this instance.
   */
  async resumePlayback(playbackId: string): Promise<void> {
    if (!this.channelData?.id) {
      throw new Error("Canal não associado a esta instância.");
    }
    await this.baseClient.delete<void>(
      `/channels/${this.channelData.id}/play/${playbackId}/pause`,
    );
  }
  /**
   * Rewinds the playback of a media by a specified amount of milliseconds.
   *
   * @param {string} playbackId - The unique identifier for the playback session to be rewound.
   * @param {number} skipMs - The number of milliseconds to rewind the playback.
   * @return {Promise<void>} A promise that resolves when the rewind operation is complete.
   */
  async rewindPlayback(playbackId: string, skipMs: number): Promise<void> {
    if (!this.channelData?.id) {
      throw new Error("Canal não associado a esta instância.");
    }
    await this.baseClient.post<void>(
      `/channels/${this.channelData.id}/play/${playbackId}/rewind`,
      { skipMs },
    );
  }
  /**
   * Fast forwards the playback by a specific duration in milliseconds.
   *
   * @param {string} playbackId - The unique identifier of the playback to be fast-forwarded.
   * @param {number} skipMs - The number of milliseconds to fast forward the playback.
   * @return {Promise<void>} A Promise that resolves when the fast-forward operation is complete.
   * @throws {Error} If no channel is associated with this instance.
   */
  async fastForwardPlayback(playbackId: string, skipMs: number): Promise<void> {
    if (!this.channelData?.id) {
      throw new Error("Canal não associado a esta instância.");
    }
    await this.baseClient.post<void>(
      `/channels/${this.channelData.id}/play/${playbackId}/forward`,
      { skipMs },
    );
  }
  /**
   * Mutes the specified channel for the given direction.
   *
   * @param {("both" | "in" | "out")} [direction="both"] - The direction to mute the channel. It can be "both" to mute incoming and outgoing, "in" to mute incoming, or "out" to mute outgoing.
   * @return {Promise<void>} A promise that resolves when the channel is successfully muted.
   * @throws {Error} If the channel is not associated with this instance.
   */
  async muteChannel(direction: "both" | "in" | "out" = "both"): Promise<void> {
    if (!this.channelData?.id) {
      throw new Error("Canal não associado a esta instância.");
    }
    await this.baseClient.post<void>(
      `/channels/${this.channelData.id}/mute?direction=${direction}`,
    );
  }
  /**
   * Unmutes a previously muted channel in the specified direction.
   *
   * @param {"both" | "in" | "out"} direction - The direction in which to unmute the channel.
   *        Defaults to "both", which unmutes both incoming and outgoing communication.
   * @return {Promise<void>} A promise that resolves once the channel has been successfully unmuted.
   * @throws {Error} If the channel is not associated with the current instance.
   */
  async unmuteChannel(
    direction: "both" | "in" | "out" = "both",
  ): Promise<void> {
    if (!this.channelData?.id) {
      throw new Error("Canal não associado a esta instância.");
    }
    await this.baseClient.delete<void>(
      `/channels/${this.channelData.id}/mute?direction=${direction}`,
    );
  }
  /**
   * Places the associated channel on hold if the channel is valid and linked to this instance.
   *
   * @return {Promise<void>} A promise that resolves when the hold action is successfully executed.
   * @throws {Error} Throws an error if the channel is not associated with this instance.
   */
  async holdChannel(): Promise<void> {
    if (!this.channelData?.id) {
      throw new Error("Canal não associado a esta instância.");
    }
    await this.baseClient.post<void>(`/channels/${this.channelData.id}/hold`);
  }
  /**
   * Removes the hold status from a specific channel associated with this instance.
   * The method sends a delete request to the server to release the hold on the channel.
   * If no channel is associated with this instance, an error will be thrown.
   *
   * @return {Promise<void>} A promise that resolves when the channel hold has been successfully removed.
   * @throws {Error} If no channel is associated with this instance.
   */
  async unholdChannel(): Promise<void> {
    if (!this.channelData?.id) {
      throw new Error("Canal não associado a esta instância.");
    }
    await this.baseClient.delete<void>(`/channels/${this.channelData.id}/hold`);
  }
}
/**
 * The `Channels` class provides a comprehensive interface for managing
 * and interacting with communication channels.
 */
export class Channels {
  private readonly channelInstances = new Map<string, ChannelInstance>();
  private eventQueue = new Map<string, NodeJS.Timeout>();
  constructor(
    private readonly baseClient: BaseClient,
    private readonly client: AriClient,
  ) {}
  /**
   * Creates or retrieves a ChannelInstance.
   *
   * @param {Object} [params] - Optional parameters for getting/creating a channel instance
   * @param {string} [params.id] - Optional ID of an existing channel
   * @returns {ChannelInstance} The requested or new channel instance
   * @throws {Error} If channel creation/retrieval fails
   *
   * @example
   * // Create new channel without ID
   * const channel1 = client.channels.Channel();
   *
   * // Create/retrieve channel with specific ID
   * const channel2 = client.channels.Channel({ id: 'some-id' });
   */
  Channel(params?: { id?: string }): ChannelInstance {
    try {
      const id = params?.id;
      if (!id) {
        const instance = new ChannelInstance(this.client, this.baseClient);
        this.channelInstances.set(instance.id, instance);
        return instance;
      }
      if (!this.channelInstances.has(id)) {
        const instance = new ChannelInstance(this.client, this.baseClient, id);
        this.channelInstances.set(id, instance);
        return instance;
      }
      return this.channelInstances.get(id)!;
    } catch (error: unknown) {
      const message = getErrorMessage(error);
      console.error(`Error creating/retrieving channel instance:`, message);
      throw new Error(`Failed to manage channel instance: ${message}`);
    }
  }
  public cleanup(): void {
    // Limpar event queue
    this.eventQueue.forEach((timeout) => clearTimeout(timeout));
    this.eventQueue.clear();
    // Limpar todas as instâncias
    this.remove();
  }
  /**
   * Removes all channel instances and cleans up their resources.
   * This method ensures proper cleanup of all channels and their associated listeners.
   */
  public remove(): void {
    // Salvar os IDs antes de começar a limpeza
    const channelIds = Array.from(this.channelInstances.keys());
    for (const channelId of channelIds) {
      try {
        const instance = this.channelInstances.get(channelId);
        if (instance) {
          instance.cleanup(); // Usar o novo método cleanup
          this.channelInstances.delete(channelId);
          console.log(`Channel instance ${channelId} removed and cleaned up`);
        }
      } catch (error) {
        console.error(`Error cleaning up channel ${channelId}:`, error);
      }
    }
    // Garantir que o map está vazio
    this.channelInstances.clear();
    console.log("All channel instances have been removed and cleaned up");
  }
  /**
   * Retrieves the details of a specific channel.
   *
   * @param {string} id - The unique identifier of the channel to retrieve.
   * @returns {Promise<Channel>} A promise that resolves to the Channel object containing the channel details.
   * @throws {Error} If no channel ID is associated with this instance or if there's an error retrieving the channel details.
   */
  async get(id: string): Promise<Channel> {
    try {
      if (!id) {
        throw new Error("No channel ID associated with this instance");
      }
      return await this.baseClient.get<Channel>(`/channels/${id}`);
    } catch (error: unknown) {
      const message = getErrorMessage(error);
      console.error(`Error retrieving channel details for ${id}:`, message);
      throw new Error(`Failed to get channel details: ${message}`);
    }
  }
  /**
   * Removes a channel instance from the collection.
   */
  public removeChannelInstance(channelId: string): void {
    if (!channelId) {
      throw new Error("Channel ID is required");
    }
    const instance = this.channelInstances.get(channelId);
    if (instance) {
      try {
        instance.cleanup();
        this.channelInstances.delete(channelId);
        console.log(`Channel instance ${channelId} removed from memory`);
      } catch (error) {
        console.error(`Error removing channel instance ${channelId}:`, error);
        throw error;
      }
    } else {
      console.warn(`Attempt to remove non-existent instance: ${channelId}`);
    }
  }
  /**
   * Propagates a WebSocket event to a specific channel.
   */
  public propagateEventToChannel(event: WebSocketEvent): void {
    if (!event || !("channel" in event) || !event.channel?.id) {
      console.warn("Invalid WebSocket event received");
      return;
    }
    const key = `${event.type}-${event.channel.id}`;
    const existing = this.eventQueue.get(key);
    if (existing) {
      clearTimeout(existing);
    }
    this.eventQueue.set(
      key,
      setTimeout(() => {
        const instance = this.channelInstances.get(event.channel!.id!);
        if (instance) {
          instance.emitEvent(event);
        } else {
          console.warn(
            `No instance found for channel ${event.channel!.id}. Event ignored.`,
          );
        }
        this.eventQueue.delete(key);
      }, 100),
    );
  }
  /**
   * Initiates a new channel.
   */
  async originate(data: OriginateRequest): Promise<Channel> {
    if (!data.endpoint) {
      throw new Error("Endpoint is required for channel origination");
    }
    try {
      return await this.baseClient.post<Channel>("/channels", data);
    } catch (error: unknown) {
      const message = getErrorMessage(error);
      console.error(`Error originating channel:`, message);
      throw new Error(`Failed to originate channel: ${message}`);
    }
  }
  /**
   * Lists all active channels.
   */
  async list(): Promise<Channel[]> {
    try {
      const channels = await this.baseClient.get<unknown>("/channels");
      if (!Array.isArray(channels)) {
        throw new Error("API response for /channels is not an array");
      }
      return channels as Channel[];
    } catch (error: unknown) {
      const message = getErrorMessage(error);
      console.error(`Error listing channels:`, message);
      throw new Error(`Failed to list channels: ${message}`);
    }
  }
  /**
   * Gets the count of active channel instances.
   */
  getInstanceCount(): number {
    return this.channelInstances.size;
  }
  /**
   * Checks if a channel instance exists.
   */
  hasInstance(channelId: string): boolean {
    return this.channelInstances.has(channelId);
  }
  /**
   * Gets all active channel instances.
   */
  getAllInstances(): Map<string, ChannelInstance> {
    return new Map(this.channelInstances);
  }
  /**
   * Terminates an active call on the specified channel.
   *
   * @param {string} channelId - The unique identifier of the channel to hang up.
   * @param {Object} [options] - Optional parameters for the hangup request.
   * @param {string} [options.reason_code] - A code indicating the reason for the hangup.
   * @param {string} [options.reason] - A descriptive reason for the hangup.
   * @return {Promise<void>} A promise that resolves when the call has been successfully terminated.
   */
  async hangup(
    channelId: string,
    options?: { reason_code?: string; reason?: string },
  ): Promise<void> {
    const queryParams = new URLSearchParams({
      ...(options?.reason_code && { reason_code: options.reason_code }),
      ...(options?.reason && { reason: options.reason }),
    });
    return this.baseClient.delete<void>(
      `/channels/${channelId}?${queryParams.toString()}`,
    );
  }
  /**
   * Initiates snooping on a specified channel with the provided options.
   *
   * @param {string} channelId - The unique identifier of the channel to snoop on.
   * @param {SnoopOptions} options - Configuration options for the snooping operation.
   * @return {Promise<Channel>} A promise that resolves to the snooped channel data.
   */
  async snoopChannel(
    channelId: string,
    options: SnoopOptions,
  ): Promise<Channel> {
    const queryParams = toQueryParams(options);
    return this.baseClient.post<Channel>(
      `/channels/${channelId}/snoop?${queryParams}`,
    );
  }
  /**
   * Starts a silence mode for the specified channel.
   *
   * @param {string} channelId - The unique identifier of the channel where silence mode should be started.
   * @return {Promise<void>} A promise that resolves when the silence mode is successfully started.
   */
  async startSilence(channelId: string): Promise<void> {
    return this.baseClient.post<void>(`/channels/${channelId}/silence`);
  }
  /**
   * Stops the silence mode for a specific channel.
   *
   * @param {string} channelId - The unique identifier of the channel for which silence mode should be stopped.
   * @return {Promise<void>} A promise that resolves when the operation is complete.
   */
  async stopSilence(channelId: string): Promise<void> {
    return this.baseClient.delete<void>(`/channels/${channelId}/silence`);
  }
  /**
   * Retrieves the Real-Time Protocol (RTP) statistics for a specific channel.
   *
   * @param {string} channelId - The unique identifier of the channel for which RTP statistics are fetched.
   * @return {Promise<RTPStats>} A promise that resolves to the RTP statistics for the specified channel.
   */
  async getRTPStatistics(channelId: string): Promise<RTPStats> {
    return this.baseClient.get<RTPStats>(
      `/channels/${channelId}/rtp_statistics`,
    );
  }
  /**
   * Creates an external media channel with the given options.
   *
   * @param {ExternalMediaOptions} options - The configuration options for creating the external media channel.
   * @return {Promise<Channel>} A promise that resolves with the created external media channel.
   */
  async createExternalMedia(options: ExternalMediaOptions): Promise<Channel> {
    const queryParams = toQueryParams(options);
    return this.baseClient.post<Channel>(
      `/channels/externalMedia?${queryParams}`,
    );
  }
  /**
   * Initiates playback of a specific media item on a channel using the provided playback ID.
   *
   * @param {string} channelId - The unique identifier of the channel where playback will occur.
   * @param {string} playbackId - The unique identifier for the specific playback session.
   * @param {string} media - The media content to be played.
   * @param {PlaybackOptions} [options] - Optional playback configuration parameters.
   * @return {Promise<ChannelPlayback>} A promise that resolves with the playback details for the channel.
   */
  async playWithId(
    channelId: string,
    playbackId: string,
    media: string,
    options?: PlaybackOptions,
  ): Promise<ChannelPlayback> {
    const queryParams = options ? `?${toQueryParams(options)}` : "";
    return this.baseClient.post<ChannelPlayback>(
      `/channels/${channelId}/play/${playbackId}${queryParams}`,
      { media },
    );
  }
  /**
   * Initiates a snoop operation on a specific channel using the provided channel ID and snoop ID.
   *
   * @param {string} channelId - The unique identifier of the channel to snoop on.
   * @param {string} snoopId - The unique identifier for the snoop operation.
   * @param {SnoopOptions} options - Additional options and parameters for the snoop operation.
   * @return {Promise<Channel>} A promise that resolves to the channel details after the snoop operation is initiated.
   */
  async snoopChannelWithId(
    channelId: string,
    snoopId: string,
    options: SnoopOptions,
  ): Promise<Channel> {
    const queryParams = toQueryParams(options);
    return this.baseClient.post<Channel>(
      `/channels/${channelId}/snoop/${snoopId}?${queryParams}`,
    );
  }
  /**
   * Starts Music on Hold for the specified channel with the provided Music on Hold class.
   *
   * @param {string} channelId - The unique identifier of the channel.
   * @param {string} mohClass - The Music on Hold class to be applied.
   * @return {Promise<void>} A promise that resolves when the operation is complete.
   */
  async startMohWithClass(channelId: string, mohClass: string): Promise<void> {
    const queryParams = `mohClass=${encodeURIComponent(mohClass)}`;
    await this.baseClient.post<void>(
      `/channels/${channelId}/moh?${queryParams}`,
    );
  }
  /**
   * Retrieves the value of a specified variable for a given channel.
   *
   * @param {string} channelId - The unique identifier of the channel.
   * @param {string} variable - The name of the variable to retrieve.
   * @return {Promise<ChannelVar>} A promise that resolves to the value of the channel variable.
   * @throws {Error} Throws an error if the 'variable' parameter is not provided.
   */
  async getChannelVariable(
    channelId: string,
    variable: string,
  ): Promise<ChannelVar> {
    if (!variable) {
      throw new Error("The 'variable' parameter is required.");
    }
    return this.baseClient.get<ChannelVar>(
      `/channels/${channelId}/variable?variable=${encodeURIComponent(variable)}`,
    );
  }
  /**
   * Sets a variable for a specific channel.
   *
   * @param {string} channelId - The unique identifier of the channel.
   * @param {string} variable - The name of the variable to be set. This parameter is required.
   * @param {string} [value] - The value of the variable to be set. This parameter is optional.
   * @return {Promise<void>} A promise that resolves when the variable is successfully set.
   * @throws {Error} Throws an error if the `variable` parameter is not provided.
   */
  async setChannelVariable(
    channelId: string,
    variable: string,
    value?: string,
  ): Promise<void> {
    if (!variable) {
      throw new Error("The 'variable' parameter is required.");
    }
    const queryParams = new URLSearchParams({
      variable,
      ...(value && { value }),
    });
    await this.baseClient.post<void>(
      `/channels/${channelId}/variable?${queryParams}`,
    );
  }
  /**
   * Moves a specified channel to the given application with optional arguments.
   *
   * @param {string} channelId - The unique identifier of the channel to be moved.
   * @param {string} app - The target application to which the channel will be moved.
   * @param {string} [appArgs] - Optional arguments to be passed to the target application.
   * @return {Promise<void>} A promise that resolves when the operation is completed.
   */
  async moveToApplication(
    channelId: string,
    app: string,
    appArgs?: string,
  ): Promise<void> {
    await this.baseClient.post<void>(`/channels/${channelId}/move`, {
      app,
      appArgs,
    });
  }
  /**
   * Continues the execution of a dialplan for a specific channel.
   *
   * @param {string} channelId - The unique identifier of the channel.
   * @param {string} [context] - The dialplan context to continue execution in, if specified.
   * @param {string} [extension] - The dialplan extension to proceed with, if provided.
   * @param {number} [priority] - The priority within the dialplan extension to resume at, if specified.
   * @param {string} [label] - The label to start from within the dialplan, if given.
   * @return {Promise<void>} Resolves when the dialplan is successfully continued.
   */
  async continueDialplan(
    channelId: string,
    context?: string,
    extension?: string,
    priority?: number,
    label?: string,
  ): Promise<void> {
    await this.baseClient.post<void>(`/channels/${channelId}/continue`, {
      context,
      extension,
      priority,
      label,
    });
  }
  /**
   * Stops the music on hold for the specified channel.
   *
   * @param {string} channelId - The unique identifier of the channel where music on hold should be stopped.
   * @return {Promise<void>} Resolves when the music on hold is successfully stopped.
   */
  async stopMusicOnHold(channelId: string): Promise<void> {
    await this.baseClient.delete<void>(`/channels/${channelId}/moh`);
  }
  /**
   * Initiates the music on hold for the specified channel.
   *
   * @param {string} channelId - The unique identifier of the channel where the music on hold will be started.
   * @return {Promise<void>} A promise that resolves when the operation has been successfully invoked.
   */
  async startMusicOnHold(channelId: string): Promise<void> {
    await this.baseClient.post<void>(`/channels/${channelId}/moh`);
  }
  /**
   * Starts recording for a specific channel based on the provided options.
   *
   * @param {string} channelId - The unique identifier of the channel to start recording.
   * @param {RecordingOptions} options - The recording options to configure the recording process.
   * @return {Promise<Channel>} A promise that resolves to the channel object with updated recording state.
   */
  async record(channelId: string, options: RecordingOptions): Promise<Channel> {
    const queryParams = toQueryParams(options);
    return this.baseClient.post<Channel>(
      `/channels/${channelId}/record?${queryParams}`,
    );
  }
  /**
   * Initiates a call on the specified channel with optional parameters for caller identification and timeout duration.
   *
   * @param {string} channelId - The ID of the channel where the call will be initiated.
   * @param {string} [caller] - Optional parameter specifying the name or identifier of the caller.
   * @param {number} [timeout] - Optional parameter defining the timeout duration for the call in seconds.
   * @return {Promise<void>} A promise that resolves when the call has been successfully initiated.
   */
  async dial(
    channelId: string,
    caller?: string,
    timeout?: number,
  ): Promise<void> {
    const queryParams = new URLSearchParams({
      ...(caller && { caller }),
      ...(timeout && { timeout: timeout.toString() }),
    });
    await this.baseClient.post<void>(
      `/channels/${channelId}/dial?${queryParams}`,
    );
  }
  /**
   * Redirects a channel to the specified endpoint.
   *
   * This method sends a POST request to update the redirect endpoint for the given channel.
   *
   * @param {string} channelId - The unique identifier of the channel to be redirected.
   * @param {string} endpoint - The new endpoint to redirect the channel to.
   * @return {Promise<void>} A promise that resolves when the operation is complete.
   */
  async redirectChannel(channelId: string, endpoint: string): Promise<void> {
    await this.baseClient.post<void>(
      `/channels/${channelId}/redirect?endpoint=${encodeURIComponent(endpoint)}`,
    );
  }
  /**
   * Answers a specified channel by sending a POST request to the corresponding endpoint.
   *
   * @param {string} channelId - The unique identifier of the channel to be answered.
   * @return {Promise<void>} A promise that resolves when the channel has been successfully answered.
   */
  async answerChannel(channelId: string): Promise<void> {
    await this.baseClient.post<void>(`/channels/${channelId}/answer`);
  }
  /**
   * Rings the specified channel by sending a POST request to the appropriate endpoint.
   *
   * @param {string} channelId - The unique identifier of the channel to be rung.
   * @return {Promise<void>} A promise that resolves when the operation completes successfully.
   */
  async ringChannel(channelId: string): Promise<void> {
    await this.baseClient.post<void>(`/channels/${channelId}/ring`);
  }
  /**
   * Stops the ring channel for the specified channel ID.
   *
   * This method sends a DELETE request to the server to stop the ring action
   * associated with the provided channel ID.
   *
   * @param {string} channelId - The unique identifier of the channel for which the ring action should be stopped.
   * @return {Promise<void>} A promise that resolves when the ring channel is successfully stopped.
   */
  async stopRingChannel(channelId: string): Promise<void> {
    await this.baseClient.delete<void>(`/channels/${channelId}/ring`);
  }
  /**
   * Sends DTMF (Dual-Tone Multi-Frequency) signals to a specified channel.
   *
   * @param {string} channelId - The ID of the channel to which the DTMF signals should be sent.
   * @param {string} dtmf - The DTMF tones to be sent, represented as a string. Each character corresponds to a specific tone.
   * @param {Object} [options] - Optional configuration for the DTMF signal timing.
   * @param {number} [options.before] - Time in milliseconds to wait before sending the first DTMF tone.
   * @param {number} [options.between] - Time in milliseconds to wait between sending successive DTMF tones.
   * @param {number} [options.duration] - Duration in milliseconds for each DTMF tone.
   * @param {number} [options.after] - Time in milliseconds to wait after sending the last DTMF tone.
   * @return {Promise<void>} A promise that resolves when the DTMF signals are successfully sent.
   */
  async sendDTMF(
    channelId: string,
    dtmf: string,
    options?: {
      before?: number;
      between?: number;
      duration?: number;
      after?: number;
    },
  ): Promise<void> {
    const queryParams = toQueryParams({ dtmf, ...options });
    await this.baseClient.post<void>(
      `/channels/${channelId}/dtmf?${queryParams}`,
    );
  }
  /**
   * Mutes a specified channel in the given direction.
   *
   * @param {string} channelId - The unique identifier of the channel to be muted.
   * @param {"both" | "in" | "out"} [direction="both"] - The direction for muting, can be "both", "in", or "out". Default is "both".
   * @return {Promise<void>} A promise that resolves when the channel is successfully muted.
   */
  async muteChannel(
    channelId: string,
    direction: "both" | "in" | "out" = "both",
  ): Promise<void> {
    await this.baseClient.post<void>(
      `/channels/${channelId}/mute?direction=${direction}`,
    );
  }
  /**
   * Unmutes a previously muted channel, allowing communication in the specified direction(s).
   *
   * @param {string} channelId - The unique identifier of the channel to be unmuted.
   * @param {"both" | "in" | "out"} [direction="both"] - The direction of communication to unmute. Valid options are "both", "in" (incoming messages), or "out" (outgoing messages). Defaults to "both".
   * @return {Promise<void>} A promise that resolves when the channel is successfully unmuted.
   */
  async unmuteChannel(
    channelId: string,
    direction: "both" | "in" | "out" = "both",
  ): Promise<void> {
    await this.baseClient.delete<void>(
      `/channels/${channelId}/mute?direction=${direction}`,
    );
  }
  /**
   * Places a specific channel on hold by sending a POST request to the server.
   *
   * @param {string} channelId - The unique identifier of the channel to be placed on hold.
   * @return {Promise<void>} A promise that resolves when the channel hold operation is completed.
   */
  async holdChannel(channelId: string): Promise<void> {
    await this.baseClient.post<void>(`/channels/${channelId}/hold`);
  }
  /**
   * Removes the hold status from a specific channel by its ID.
   *
   * @param {string} channelId - The unique identifier of the channel to unhold.
   * @return {Promise<void>} A promise that resolves when the channel hold is successfully removed.
   */
  async unholdChannel(channelId: string): Promise<void> {
    await this.baseClient.delete<void>(`/channels/${channelId}/hold`);
  }
  /**
   * Creates a new communication channel with the specified configuration.
   *
   * @param {OriginateRequest} data - The configuration data required to create the channel, including relevant details such as endpoint and channel variables.
   * @return {Promise<Channel>} A promise that resolves with the details of the created channel.
   */
  async createChannel(data: OriginateRequest): Promise<Channel> {
    return this.baseClient.post<Channel>("/channels/create", data);
  }
  /**
   * Initiates a new channel with the specified channel ID and originates a call using the provided data.
   *
   * @param {string} channelId - The unique identifier of the channel to be created.
   * @param {OriginateRequest} data - The data required to originate the call, including details such as endpoint and caller information.
   * @return {Promise<Channel>} A promise that resolves to the created Channel object.
   */
  async originateWithId(
    channelId: string,
    data: OriginateRequest,
  ): Promise<Channel> {
    return this.baseClient.post<Channel>(`/channels/${channelId}`, data);
  }
}
</file>

<file path="src/ari-client/resources/endpoints.ts">
import type { BaseClient } from "../baseClient.js";
import type { Endpoint, EndpointDetails } from "../interfaces/endpoints.types";
export class Endpoints {
  constructor(private client: BaseClient) {}
  /**
   * Lists all available endpoints.
   *
   * @returns A promise that resolves to an array of Endpoint objects representing all available endpoints.
   * @throws {Error} If the API response is not an array.
   */
  async list(): Promise<Endpoint[]> {
    const endpoints = await this.client.get<unknown>("/endpoints");
    if (!Array.isArray(endpoints)) {
      throw new Error("Resposta da API /endpoints não é um array.");
    }
    return endpoints as Endpoint[];
  }
  /**
   * Retrieves details of a specific endpoint.
   *
   * @param technology - The technology of the endpoint (e.g., "PJSIP").
   * @param resource - The specific resource name of the endpoint (e.g., "9001").
   * @returns A promise that resolves to an EndpointDetails object containing the details of the specified endpoint.
   */
  async getDetails(
    technology: string,
    resource: string,
  ): Promise<EndpointDetails> {
    return this.client.get<EndpointDetails>(
      `/endpoints/${technology}/${resource}`,
    );
  }
  /**
   * Sends a message to a specific endpoint.
   *
   * @param technology - The technology of the endpoint (e.g., "PJSIP").
   * @param resource - The specific resource name of the endpoint (e.g., "9001").
   * @param message - The message payload to send to the endpoint.
   * @returns A promise that resolves when the message has been successfully sent.
   */
  async sendMessage(
    technology: string,
    resource: string,
    message: Record<string, unknown>,
  ): Promise<void> {
    await this.client.post<void>(
      `/endpoints/${technology}/${resource}/sendMessage`,
      message,
    );
  }
}
</file>

<file path="src/ari-client/resources/playbacks.ts">
import { EventEmitter } from "events";
import { isAxiosError } from "axios";
import type { AriClient } from "../ariClient";
import type { BaseClient } from "../baseClient.js";
import type { Playback, WebSocketEvent } from "../interfaces";
/**
 * Utility function to extract error message
 * @param error - The error object to process
 * @returns A formatted error message
 */
const getErrorMessage = (error: unknown): string => {
  if (isAxiosError(error)) {
    return (
      error.response?.data?.message ||
      error.message ||
      "An axios error occurred"
    );
  }
  if (error instanceof Error) {
    return error.message;
  }
  return "An unknown error occurred";
};
/**
 * Represents a playback instance that provides methods for controlling media playback,
 * managing event listeners, and handling playback state.
 */
export class PlaybackInstance {
  private readonly eventEmitter = new EventEmitter();
  private readonly listenersMap = new Map<
    string,
    ((...args: any[]) => void)[]
  >(); // 🔹 Guarda listeners para remoção posterior
  private playbackData: Playback | null = null;
  public readonly id: string;
  /**
   * Creates a new PlaybackInstance.
   *
   * @param {AriClient} client - ARI client for communication
   * @param {BaseClient} baseClient - Base client for HTTP requests
   * @param {string} [playbackId] - Optional playback ID, generates timestamp-based ID if not provided
   */
  constructor(
    private readonly client: AriClient,
    private readonly baseClient: BaseClient,
    private readonly playbackId: string = `playback-${Date.now()}`,
  ) {
    this.id = playbackId;
  }
  /**
   * Registers an event listener for a specific WebSocket event type.
   *
   * @param {T} event - Event type to listen for
   * @param {(data: WebSocketEvent) => void} listener - Callback function for the event
   */
  on<T extends WebSocketEvent["type"]>(
    event: T,
    listener: (data: Extract<WebSocketEvent, { type: T }>) => void,
  ): void {
    if (!event) {
      throw new Error("Event type is required");
    }
    // 🔹 Verifica se o listener já está registrado para evitar duplicação
    const existingListeners = this.listenersMap.get(event) || [];
    if (existingListeners.includes(listener)) {
      console.warn(
        `Listener já registrado para evento ${event}, reutilizando.`,
      );
      return;
    }
    const wrappedListener = (data: WebSocketEvent) => {
      if ("playback" in data && data.playback?.id === this.id) {
        listener(data as Extract<WebSocketEvent, { type: T }>);
      }
    };
    this.eventEmitter.on(event, wrappedListener);
    // 🔹 Armazena o listener para futura remoção
    if (!this.listenersMap.has(event)) {
      this.listenersMap.set(event, []);
    }
    this.listenersMap
      .get(event)!
      .push(wrappedListener as (...args: any[]) => void);
  }
  /**
   * Registers a one-time event listener for a specific WebSocket event type.
   *
   * @param {T} event - Event type to listen for
   * @param {(data: WebSocketEvent) => void} listener - Callback function for the event
   */
  once<T extends WebSocketEvent["type"]>(
    event: T,
    listener: (data: Extract<WebSocketEvent, { type: T }>) => void,
  ): void {
    if (!event) {
      throw new Error("Event type is required");
    }
    const eventKey = `${event}-${this.id}`;
    // 🔹 Verifica se já existe um listener igual para evitar duplicação
    const existingListeners = this.listenersMap.get(eventKey) || [];
    if (existingListeners.includes(listener)) {
      console.warn(
        `One-time listener já registrado para evento ${eventKey}, reutilizando.`,
      );
      return;
    }
    const wrappedListener = (data: WebSocketEvent) => {
      if ("playback" in data && data.playback?.id === this.id) {
        listener(data as Extract<WebSocketEvent, { type: T }>);
        // 🔹 Remove automaticamente o listener após a primeira execução
        this.off(event, wrappedListener);
      }
    };
    this.eventEmitter.once(event, wrappedListener);
    // 🔹 Armazena o listener para futura remoção
    if (!this.listenersMap.has(eventKey)) {
      this.listenersMap.set(eventKey, []);
    }
    this.listenersMap
      .get(eventKey)!
      .push(wrappedListener as (...args: any[]) => void);
  }
  /**
   * Removes event listener(s) for a specific WebSocket event type.
   *
   * @param {T} event - Event type to remove listener(s) for
   * @param {(data: WebSocketEvent) => void} [listener] - Optional specific listener to remove
   */
  off<T extends WebSocketEvent["type"]>(
    event: T,
    listener?: (data: Extract<WebSocketEvent, { type: T }>) => void,
  ): void {
    if (!event) {
      throw new Error("Event type is required");
    }
    if (listener) {
      this.eventEmitter.off(event, listener);
      const storedListeners = this.listenersMap.get(event) || [];
      this.listenersMap.set(
        event,
        storedListeners.filter((l) => l !== listener),
      );
    } else {
      this.eventEmitter.removeAllListeners(event);
      this.listenersMap.delete(event);
    }
  }
  /**
   * Cleans up the PlaybackInstance, resetting its state and clearing resources.
   */
  public cleanup(): void {
    // Limpar dados do playback
    this.playbackData = null;
    // Remover todos os listeners
    this.removeAllListeners();
    // Limpar o map de listeners
    this.listenersMap.clear();
    console.log(`Playback instance ${this.id} cleaned up`);
  }
  /**
   * Emits a WebSocket event if it matches the current playback instance.
   *
   * @param {WebSocketEvent} event - Event to emit
   */
  emitEvent(event: WebSocketEvent): void {
    if (!event) {
      console.warn("Received invalid event");
      return;
    }
    if ("playback" in event && event.playback?.id === this.id) {
      this.eventEmitter.emit(event.type, event);
    }
  }
  /**
   * Retrieves current playback data.
   *
   * @returns {Promise<Playback>} Current playback data
   * @throws {Error} If playback is not properly initialized
   */
  async get(): Promise<Playback> {
    if (!this.id) {
      throw new Error("No playback associated with this instance");
    }
    try {
      this.playbackData = await this.baseClient.get<Playback>(
        `/playbacks/${this.id}`,
      );
      return this.playbackData;
    } catch (error: unknown) {
      const message = getErrorMessage(error);
      console.warn(`Error retrieving playback data for ${this.id}:`, message);
      throw new Error(`Failed to get playback data: ${message}`);
    }
  }
  /**
   * Controls playback with specified operation.
   *
   * @param {"pause" | "unpause" | "reverse" | "forward"} operation - Control operation to perform
   * @throws {Error} If playback is not properly initialized or operation fails
   */
  async control(
    operation: "pause" | "unpause" | "reverse" | "forward",
  ): Promise<void> {
    if (!this.id) {
      throw new Error("No playback associated with this instance");
    }
    try {
      await this.baseClient.post<void>(
        `/playbacks/${this.id}/control?operation=${operation}`,
      );
    } catch (error: unknown) {
      const message = getErrorMessage(error);
      console.warn(`Error controlling playback ${this.id}:`, message);
      throw new Error(`Failed to control playback: ${message}`);
    }
  }
  /**
   * Stops the current playback.
   *
   * @throws {Error} If playback is not properly initialized or stop operation fails
   */
  async stop(): Promise<void> {
    if (!this.id) {
      throw new Error("No playback associated with this instance");
    }
    try {
      await this.baseClient.delete<void>(`/playbacks/${this.id}`);
    } catch (error: unknown) {
      const message = getErrorMessage(error);
      console.warn(`Error stopping playback ${this.id}:`, message);
      throw new Error(`Failed to stop playback: ${message}`);
    }
  }
  /**
   * Removes all event listeners associated with this playback instance.
   * This method clears both the internal listener map and the event emitter.
   *
   * @remarks
   * This method performs the following actions:
   * 1. Logs a message indicating the removal of listeners.
   * 2. Iterates through all stored listeners and removes them from the event emitter.
   * 3. Clears the internal listener map.
   * 4. Removes all listeners from the event emitter.
   *
   * @returns {void} This method doesn't return a value.
   */
  removeAllListeners(): void {
    console.log(`Removing all event listeners for playback ${this.id}`);
    this.listenersMap.forEach((listeners, event) => {
      listeners.forEach((listener) => {
        this.eventEmitter.off(
          event as string,
          listener as (...args: any[]) => void,
        );
      });
    });
    this.listenersMap.clear();
    this.eventEmitter.removeAllListeners();
  }
  /**
   * Checks if the playback instance has any listeners for a specific event.
   *
   * @param {string} event - Event type to check
   * @returns {boolean} True if there are listeners for the event
   */
  hasListeners(event: string): boolean {
    return this.eventEmitter.listenerCount(event) > 0;
  }
  /**
   * Gets the current playback data without making an API call.
   *
   * @returns {Playback | null} Current playback data or null if not available
   */
  getCurrentData(): Playback | null {
    return this.playbackData;
  }
}
/**
 * Manages playback instances and their related operations in the Asterisk REST Interface.
 * This class provides functionality to create, control, and manage playback instances,
 * as well as handle WebSocket events related to playbacks.
 */
export class Playbacks {
  private playbackInstances = new Map<string, PlaybackInstance>();
  private eventQueue = new Map<string, NodeJS.Timeout>();
  constructor(
    private baseClient: BaseClient,
    private client: AriClient,
  ) {}
  /**
   * Gets or creates a playback instance
   * @param {Object} [params] - Optional parameters for getting/creating a playback instance
   * @param {string} [params.id] - Optional ID of an existing playback
   * @returns {PlaybackInstance} The requested or new playback instance
   */
  Playback(params?: { id?: string }): PlaybackInstance {
    try {
      const id = params?.id;
      if (!id) {
        const instance = new PlaybackInstance(this.client, this.baseClient);
        this.playbackInstances.set(instance.id, instance);
        return instance;
      }
      if (!this.playbackInstances.has(id)) {
        const instance = new PlaybackInstance(this.client, this.baseClient, id);
        this.playbackInstances.set(id, instance);
        return instance;
      }
      return this.playbackInstances.get(id)!;
    } catch (error: unknown) {
      const message = getErrorMessage(error);
      console.warn(`Error creating/retrieving playback instance:`, message);
      throw new Error(`Failed to manage playback instance: ${message}`);
    }
  }
  /**
   * Cleans up resources associated with the Playbacks instance.
   * This method performs the following cleanup operations:
   * 1. Clears all pending timeouts in the event queue.
   * 2. Removes all playback instances.
   *
   * @remarks
   * This method should be called when the Playbacks instance is no longer needed
   * to ensure proper resource management and prevent memory leaks.
   *
   * @returns {void} This method doesn't return a value.
   */
  public cleanup(): void {
    // Limpar event queue
    this.eventQueue.forEach((timeout) => clearTimeout(timeout));
    this.eventQueue.clear();
    // Limpar todas as instâncias
    this.remove();
  }
  /**
   * Removes all playback instances and cleans up their resources.
   */
  public remove(): void {
    // Salvar os IDs antes de começar a limpeza
    const playbackIds = Array.from(this.playbackInstances.keys());
    for (const playbackId of playbackIds) {
      try {
        const instance = this.playbackInstances.get(playbackId);
        if (instance) {
          instance.cleanup(); // Usar o novo método cleanup
          this.playbackInstances.delete(playbackId);
          console.log(`Playback instance ${playbackId} removed and cleaned up`);
        }
      } catch (error) {
        console.error(`Error cleaning up playback ${playbackId}:`, error);
      }
    }
    // Garantir que o map está vazio
    this.playbackInstances.clear();
    console.log("All playback instances have been removed and cleaned up");
  }
  /**
   * Removes a playback instance and cleans up its resources
   * @param {string} playbackId - ID of the playback instance to remove
   * @throws {Error} If the playback instance doesn't exist
   */
  public removePlaybackInstance(playbackId: string): void {
    if (!playbackId) {
      throw new Error("Playback ID is required");
    }
    const instance = this.playbackInstances.get(playbackId);
    if (instance) {
      try {
        instance.cleanup();
        this.playbackInstances.delete(playbackId);
        console.log(`Playback instance ${playbackId} removed from memory`);
      } catch (error) {
        console.error(`Error removing playback instance ${playbackId}:`, error);
        throw error;
      }
    } else {
      console.warn(`Attempt to remove non-existent instance: ${playbackId}`);
    }
  }
  /**
   * Propagates WebSocket events to the corresponding playback instance
   * @param {WebSocketEvent} event - The WebSocket event to propagate
   */
  public propagateEventToPlayback(event: WebSocketEvent): void {
    if (!event || !("playback" in event) || !event.playback?.id) {
      console.warn("Invalid WebSocket event received");
      return;
    }
    const key = `${event.type}-${event.playback.id}`;
    const existing = this.eventQueue.get(key);
    if (existing) {
      clearTimeout(existing);
    }
    this.eventQueue.set(
      key,
      setTimeout(() => {
        const instance = this.playbackInstances.get(event.playback!.id!);
        if (instance) {
          instance.emitEvent(event);
        } else {
          console.warn(
            `No instance found for playback ${event.playback!.id}. Event ignored.`,
          );
        }
        this.eventQueue.delete(key);
      }, 100),
    );
  }
  /**
   * Retrieves details of a specific playback
   * @param {string} playbackId - ID of the playback to get details for
   * @returns {Promise<Playback>} Promise resolving to playback details
   * @throws {Error} If the playback ID is invalid or the request fails
   */
  async get(playbackId: string): Promise<Playback> {
    if (!playbackId) {
      throw new Error("Playback ID is required");
    }
    try {
      return await this.baseClient.get<Playback>(`/playbacks/${playbackId}`);
    } catch (error: unknown) {
      const message = getErrorMessage(error);
      console.warn(`Error getting playback details ${playbackId}:`, message);
      throw new Error(`Failed to get playback details: ${message}`);
    }
  }
  /**
   * Controls a specific playback instance
   * @param {string} playbackId - ID of the playback to control
   * @param {"pause" | "unpause" | "reverse" | "forward"} operation - Operation to perform
   * @throws {Error} If the playback ID is invalid or the operation fails
   */
  async control(
    playbackId: string,
    operation: "pause" | "unpause" | "reverse" | "forward",
  ): Promise<void> {
    if (!playbackId) {
      throw new Error("Playback ID is required");
    }
    try {
      const playback = this.Playback({ id: playbackId });
      await playback.control(operation);
    } catch (error: unknown) {
      const message = getErrorMessage(error);
      console.warn(`Error controlling playback ${playbackId}:`, message);
      throw new Error(`Failed to control playback: ${message}`);
    }
  }
  /**
   * Stops a specific playback instance
   * @param {string} playbackId - ID of the playback to stop
   * @throws {Error} If the playback ID is invalid or the stop operation fails
   */
  async stop(playbackId: string): Promise<void> {
    if (!playbackId) {
      throw new Error("Playback ID is required");
    }
    try {
      const playback = this.Playback({ id: playbackId });
      await playback.stop();
    } catch (error: unknown) {
      const message = getErrorMessage(error);
      console.warn(`Error stopping playback ${playbackId}:`, message);
      throw new Error(`Failed to stop playback: ${message}`);
    }
  }
  /**
   * Gets the count of active playback instances
   * @returns {number} Number of active playback instances
   */
  getInstanceCount(): number {
    return this.playbackInstances.size;
  }
  /**
   * Checks if a playback instance exists
   * @param {string} playbackId - ID of the playback to check
   * @returns {boolean} True if the playback instance exists
   */
  hasInstance(playbackId: string): boolean {
    return this.playbackInstances.has(playbackId);
  }
}
</file>

<file path="src/ari-client/resources/sounds.ts">
import type { BaseClient } from "../baseClient.js";
import type { Sound, SoundListRequest } from "../interfaces/sounds.types.js";
export class Sounds {
  constructor(private client: BaseClient) {}
  /**
   * Lists all available sounds.
   *
   * @param params - Optional parameters to filter the list of sounds.
   * @returns A promise that resolves to an array of Sound objects.
   * @throws {Error} If the API response is not an array.
   */
  async list(params?: SoundListRequest): Promise<Sound[]> {
    const query = params
      ? `?${new URLSearchParams(params as Record<string, string>).toString()}`
      : "";
    const sounds = await this.client.get<unknown>(`/sounds${query}`);
    if (!Array.isArray(sounds)) {
      throw new Error("Resposta da API /sounds não é um array.");
    }
    return sounds as Sound[];
  }
  /**
   * Retrieves details of a specific sound.
   *
   * @param soundId - The unique identifier of the sound.
   * @returns A promise that resolves to a Sound object containing the details of the specified sound.
   */
  async getDetails(soundId: string): Promise<Sound> {
    return this.client.get<Sound>(`/sounds/${soundId}`);
  }
}
</file>

<file path="src/ari-client/ariClient.ts">
import { BaseClient } from "./baseClient.js";
import type {
  AriClientConfig,
  WebSocketEvent,
  WebSocketEventType,
} from "./interfaces";
import type {
  TypedWebSocketEventListener,
  WebSocketEventListener,
} from "./interfaces/websocket.types";
import { Applications } from "./resources/applications.js";
import { Asterisk } from "./resources/asterisk";
import { type BridgeInstance, Bridges } from "./resources/bridges";
import { type ChannelInstance, Channels } from "./resources/channels.js";
import { Endpoints } from "./resources/endpoints";
import { type PlaybackInstance, Playbacks } from "./resources/playbacks";
import { Sounds } from "./resources/sounds";
import { WebSocketClient } from "./websocketClient.js";
/**
 * Main client class for interacting with the Asterisk REST Interface (ARI).
 * Provides access to various ARI resources and WebSocket event handling capabilities.
 *
 * @example
 * ```typescript
 * const client = new AriClient({
 *   host: 'localhost',
 *   port: 8088,
 *   username: 'user',
 *   password: 'secret'
 * });
 * ```
 */
export class AriClient {
  private readonly baseClient: BaseClient;
  private webSocketClient?: WebSocketClient;
  private eventListeners = new Map<string, WebSocketEventListener[]>();
  public readonly channels: Channels;
  public readonly endpoints: Endpoints;
  public readonly applications: Applications;
  public readonly playbacks: Playbacks;
  public readonly sounds: Sounds;
  public readonly asterisk: Asterisk;
  public readonly bridges: Bridges;
  /**
   * Creates a new instance of the ARI client.
   *
   * @param {AriClientConfig} config - Configuration options for the ARI client
   * @throws {Error} If required configuration parameters are missing
   */
  constructor(private readonly config: AriClientConfig) {
    if (!config.host || !config.port || !config.username || !config.password) {
      throw new Error("Missing required configuration parameters");
    }
    // Normalize host and create base URL
    const httpProtocol = config.secure ? "https" : "http";
    const normalizedHost = config.host.replace(/^https?:\/\//, "");
    const baseUrl = `${httpProtocol}://${normalizedHost}:${config.port}/ari`;
    // Initialize base client and resources
    this.baseClient = new BaseClient(baseUrl, config.username, config.password);
    // Initialize resource handlers
    this.channels = new Channels(this.baseClient, this);
    this.playbacks = new Playbacks(this.baseClient, this);
    this.bridges = new Bridges(this.baseClient, this);
    this.endpoints = new Endpoints(this.baseClient);
    this.applications = new Applications(this.baseClient);
    this.sounds = new Sounds(this.baseClient);
    this.asterisk = new Asterisk(this.baseClient);
    console.log(`ARI Client initialized with base URL: ${baseUrl}`);
  }
  public async cleanup(): Promise<void> {
    try {
      console.log("Starting ARI Client cleanup...");
      // Primeiro limpa o WebSocket para evitar novos eventos
      if (this.webSocketClient) {
        await this.closeWebSocket();
      }
      // Limpar todos os resources em paralelo
      await Promise.all([
        // Cleanup de channels
        (async () => {
          try {
            this.channels.cleanup();
          } catch (error) {
            console.error("Error cleaning up channels:", error);
          }
        })(),
        // Cleanup de playbacks
        (async () => {
          try {
            this.playbacks.cleanup();
          } catch (error) {
            console.error("Error cleaning up playbacks:", error);
          }
        })(),
        // Cleanup de bridges
        (async () => {
          try {
            this.bridges.cleanup();
          } catch (error) {
            console.error("Error cleaning up bridges:", error);
          }
        })(),
      ]);
      // Limpar listeners do cliente ARI
      this.eventListeners.forEach((listeners, event) => {
        listeners.forEach((listener) => {
          this.off(event as WebSocketEvent["type"], listener);
        });
      });
      this.eventListeners.clear();
      console.log("ARI Client cleanup completed successfully");
    } catch (error) {
      console.error("Error during ARI Client cleanup:", error);
      throw error;
    }
  }
  /**
   * Initializes a WebSocket connection for receiving events.
   *
   * @param {string[]} apps - List of application names to subscribe to
   * @param {WebSocketEventType[]} [subscribedEvents] - Optional list of specific event types to subscribe to
   * @returns {Promise<void>} Resolves when connection is established
   * @throws {Error} If connection fails
   */
  public async connectWebSocket(
    apps: string[],
    subscribedEvents?: WebSocketEventType[],
  ): Promise<void> {
    try {
      if (!apps.length) {
        throw new Error("At least one application name is required.");
      }
      // Se já existe uma conexão, apenas adicione novas aplicações sem fechar
      if (this.webSocketClient && this.webSocketClient.isConnected()) {
        console.log(
          "WebSocket already connected. Reconnecting with updated apps...",
        );
        await this.webSocketClient.reconnectWithApps(apps, subscribedEvents);
        return;
      }
      // Criar nova conexão (apenas se não existir uma ativa)
      this.webSocketClient = new WebSocketClient(
        this.baseClient,
        apps,
        subscribedEvents,
        this,
      );
      await this.webSocketClient.connect();
    } catch (error) {
      console.error("Failed to establish WebSocket connection:", error);
      throw error;
    }
  }
  /**
   * Adds applications to the existing WebSocket connection.
   *
   * @param {string[]} apps - Additional applications to subscribe to
   * @param {WebSocketEventType[]} [subscribedEvents] - Optional list of specific event types to subscribe to
   * @returns {Promise<void>} Resolves when applications are added successfully
   * @throws {Error} If no WebSocket connection exists or if the operation fails
   */
  public async addApplicationsToWebSocket(
    apps: string[],
    subscribedEvents?: WebSocketEventType[],
  ): Promise<void> {
    if (!this.webSocketClient || !this.webSocketClient.isConnected()) {
      throw new Error(
        "No active WebSocket connection. Create one first with connectWebSocket().",
      );
    }
    await this.webSocketClient.addApps(apps, subscribedEvents);
  }
  /**
   * Destroys the ARI Client instance, cleaning up all resources and removing circular references.
   * This method should be called when the ARI Client is no longer needed to ensure proper cleanup.
   *
   * @returns {Promise<void>} A promise that resolves when the destruction process is complete.
   * @throws {Error} If an error occurs during the destruction process.
   */
  public async destroy(): Promise<void> {
    try {
      console.log("Destroying ARI Client...");
      // Cleanup de todos os recursos
      await this.cleanup();
      // Limpar referências
      this.webSocketClient = undefined;
      // Remover todas as referências circulares
      (this.channels as any) = null;
      (this.playbacks as any) = null;
      (this.bridges as any) = null;
      (this.endpoints as any) = null;
      (this.applications as any) = null;
      (this.sounds as any) = null;
      (this.asterisk as any) = null;
      console.log("ARI Client destroyed successfully");
    } catch (error) {
      console.error("Error destroying ARI Client:", error);
      throw error;
    }
  }
  /**
   * Registers an event listener for WebSocket events.
   *
   * @param {T} event - The event type to listen for
   * @param {Function} listener - Callback function for handling the event
   * @throws {Error} If WebSocket is not connected
   */
  /**
   * Registers an event listener for WebSocket events.
   *
   * @param {T} event - The event type to listen for
   * @param {Function} listener - Callback function for handling the event
   * @throws {Error} If WebSocket is not connected
   */
  public on<T extends WebSocketEvent["type"]>(
    event: T,
    listener: TypedWebSocketEventListener<T>,
  ): void {
    if (!this.webSocketClient) {
      throw new Error("WebSocket is not connected");
    }
    // 🔹 Verifica se o listener já está registrado para evitar duplicação
    const existingListeners = this.eventListeners.get(event) || [];
    if (existingListeners.includes(listener as WebSocketEventListener)) {
      console.warn(`Listener already registered for event ${event}, reusing.`);
      return;
    }
    // Conectar o listener diretamente
    this.webSocketClient.on(event, listener);
    // Armazenar o listener para referência e limpeza futura
    existingListeners.push(listener as WebSocketEventListener);
    this.eventListeners.set(event, existingListeners);
    console.log(`Event listener successfully registered for ${event}`);
  }
  /**
   * Registers a one-time event listener for WebSocket events.
   *
   * @param {T} event - The event type to listen for
   * @param {Function} listener - Callback function for handling the event
   * @throws {Error} If WebSocket is not connected
   */
  public once<T extends WebSocketEvent["type"]>(
    event: T,
    listener: TypedWebSocketEventListener<T>,
  ): void {
    if (!this.webSocketClient) {
      throw new Error("WebSocket is not connected");
    }
    // 🔹 Check if an identical listener already exists to avoid duplication
    const existingListeners = this.eventListeners.get(event) || [];
    if (existingListeners.includes(listener as WebSocketEventListener)) {
      console.warn(
        `One-time listener already registered for event ${event}, reusing.`,
      );
      return;
    }
    const wrappedListener = (data: Extract<WebSocketEvent, { type: T }>) => {
      listener(data);
      this.off(event, wrappedListener);
    };
    this.webSocketClient.once(event, wrappedListener);
    this.eventListeners.set(event, [
      ...existingListeners,
      wrappedListener as WebSocketEventListener,
    ]);
    console.log(`One-time event listener registered for ${event}`);
  }
  /**
   * Removes an event listener for WebSocket events.
   *
   * @param {T} event - The event type to remove listener for
   * @param {Function} listener - The listener function to remove
   */
  public off<T extends WebSocketEvent["type"]>(
    event: T,
    listener: TypedWebSocketEventListener<T>,
  ): void {
    if (!this.webSocketClient) {
      console.warn("No WebSocket connection to remove listener from");
      return;
    }
    this.webSocketClient.off(event, listener);
    const existingListeners = this.eventListeners.get(event) || [];
    this.eventListeners.set(
      event,
      existingListeners.filter(
        (l) => l !== (listener as WebSocketEventListener),
      ),
    );
    console.log(`Event listener removed for ${event}`);
  }
  /**
   * Closes the WebSocket connection if one exists.
   */
  public closeWebSocket(): Promise<void> {
    return new Promise((resolve) => {
      if (!this.webSocketClient) {
        console.warn("No WebSocket connection to close");
        resolve();
        return;
      }
      console.log("Closing WebSocket connection and cleaning up listeners.");
      const closeTimeout = setTimeout(() => {
        if (this.webSocketClient) {
          this.webSocketClient.removeAllListeners();
          this.webSocketClient = undefined;
        }
        resolve();
      }, 5000);
      this.eventListeners.forEach((listeners, event) => {
        listeners.forEach((listener) => {
          this.webSocketClient?.off(
            event as WebSocketEvent["type"],
            listener as (...args: any[]) => void,
          );
        });
      });
      this.eventListeners.clear();
      this.webSocketClient.once("close", () => {
        clearTimeout(closeTimeout);
        this.webSocketClient = undefined;
        console.log("WebSocket connection closed");
        resolve();
      });
      this.webSocketClient
        .close()
        .then(() => {
          clearTimeout(closeTimeout);
          this.webSocketClient = undefined;
          resolve();
        })
        .catch((error) => {
          console.error("Error during WebSocket close:", error);
          clearTimeout(closeTimeout);
          this.webSocketClient = undefined;
          resolve();
        });
    });
  }
  /**
   * Creates or retrieves a Channel instance.
   *
   * @param {string} [channelId] - Optional ID of an existing channel
   * @returns {ChannelInstance} A new or existing channel instance
   */
  public Channel(channelId?: string): ChannelInstance {
    return this.channels.Channel({ id: channelId });
  }
  /**
   * Creates or retrieves a Playback instance.
   *
   * @param {string} [playbackId] - Optional ID of an existing playback
   * @param {string} [_app] - Optional application name (deprecated)
   * @returns {PlaybackInstance} A new or existing playback instance
   */
  public Playback(playbackId?: string, _app?: string): PlaybackInstance {
    return this.playbacks.Playback({ id: playbackId });
  }
  /**
   * Creates or retrieves a Bridge instance.
   *
   * This function allows you to create a new Bridge instance or retrieve an existing one
   * based on the provided bridge ID.
   *
   * @param {string} [bridgeId] - Optional ID of an existing bridge. If provided, retrieves the
   *                               existing bridge with this ID. If omitted, creates a new bridge.
   * @returns {BridgeInstance} A new or existing Bridge instance that can be used to interact
   *                           with the Asterisk bridge.
   */
  public Bridge(bridgeId?: string): BridgeInstance {
    return this.bridges.Bridge({ id: bridgeId });
  }
  /**
   * Gets the current WebSocket connection status.
   *
   * @returns {boolean} True if WebSocket is connected, false otherwise
   */
  public isWebSocketConnected(): boolean {
    return !!this.webSocketClient && this.webSocketClient.isConnected();
  }
}
</file>

<file path="src/ari-client/baseClient.ts">
import axios, {
  type AxiosInstance,
  type AxiosRequestConfig,
  isAxiosError,
} from "axios";
/**
 * Custom error class for HTTP-related errors
 */
class HTTPError extends Error {
  constructor(
    message: string,
    public readonly status?: number,
    public readonly method?: string,
    public readonly url?: string,
  ) {
    super(message);
    this.name = "HTTPError";
  }
}
/**
 * BaseClient handles HTTP communications with the ARI server.
 * Provides methods for making HTTP requests and manages authentication and error handling.
 */
export class BaseClient {
  private readonly client: AxiosInstance;
  /**
   * Creates a new BaseClient instance.
   *
   * @param {string} baseUrl - The base URL for the API
   * @param {string} username - Username for authentication
   * @param {string} password - Password for authentication
   * @param {number} [timeout=5000] - Request timeout in milliseconds
   * @throws {Error} If the base URL format is invalid
   */
  constructor(
    private readonly baseUrl: string,
    private readonly username: string,
    private readonly password: string,
    timeout = 5000,
  ) {
    if (!/^https?:\/\/.+/.test(baseUrl)) {
      throw new Error(
        "Invalid base URL. It must start with http:// or https://",
      );
    }
    this.client = axios.create({
      baseURL: baseUrl,
      auth: { username, password },
      timeout,
      headers: {
        "Content-Type": "application/json",
      },
    });
    this.addInterceptors();
  }
  /**
   * Gets the base URL of the client.
   */
  public getBaseUrl(): string {
    return this.baseUrl;
  }
  /**
   * Gets the configured credentials.
   */
  public getCredentials(): {
    baseUrl: string;
    username: string;
    password: string;
  } {
    return {
      baseUrl: this.baseUrl,
      username: this.username,
      password: this.password,
    };
  }
  /**
   * Adds request and response interceptors to the Axios instance.
   */
  private addInterceptors(): void {
    this.client.interceptors.request.use(
      (config) => {
        return config;
      },
      (error: unknown) => {
        const message = this.getErrorMessage(error);
        console.error("[Request Error]", message);
        return Promise.reject(new HTTPError(message));
      },
    );
    this.client.interceptors.response.use(
      (response) => {
        return response;
      },
      (error: unknown) => {
        if (isAxiosError(error)) {
          const status = error.response?.status ?? 0;
          const method = error.config?.method?.toUpperCase() ?? "UNKNOWN";
          const url = error.config?.url ?? "unknown-url";
          const message =
            error.response?.data?.message || error.message || "Unknown error";
          if (status === 404) {
            console.warn(`[404] Not Found: ${url}`);
          } else if (status >= 500) {
            console.error(`[${status}] Server Error: ${url}`);
          } else if (status > 0) {
            console.warn(`[${status}] ${method} ${url}: ${message}`);
          } else {
            console.error(`[Network] Request failed: ${message}`);
          }
          throw new HTTPError(message, status || undefined, method, url);
        }
        const message = this.getErrorMessage(error);
        console.error("[Unexpected Error]", message);
        throw new Error(message);
      },
    );
  }
  /**
   * Executes a GET request.
   *
   * @param path - API endpoint path
   * @param config - Optional Axios request configuration
   * @returns Promise with the response data
   */
  async get<T>(path: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response = await this.client.get<T>(path, config);
      return response.data;
    } catch (error: unknown) {
      throw this.handleRequestError(error);
    }
  }
  /**
   * Executes a POST request.
   *
   * @param path - API endpoint path
   * @param data - Request payload
   * @param config - Optional Axios request configuration
   * @returns Promise with the response data
   */
  async post<T, D = unknown>(
    path: string,
    data?: D,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    try {
      const response = await this.client.post<T>(path, data, config);
      return response.data;
    } catch (error: unknown) {
      throw this.handleRequestError(error);
    }
  }
  /**
   * Executes a PUT request.
   *
   * @param path - API endpoint path
   * @param data - Request payload
   * @param config - Optional Axios request configuration
   * @returns Promise with the response data
   */
  async put<T, D = unknown>(
    path: string,
    data: D,
    config?: AxiosRequestConfig,
  ): Promise<T> {
    try {
      const response = await this.client.put<T>(path, data, config);
      return response.data;
    } catch (error: unknown) {
      throw this.handleRequestError(error);
    }
  }
  /**
   * Executes a DELETE request.
   *
   * @param path - API endpoint path
   * @param config - Optional Axios request configuration
   * @returns Promise with the response data
   */
  async delete<T>(path: string, config?: AxiosRequestConfig): Promise<T> {
    try {
      const response = await this.client.delete<T>(path, config);
      return response.data;
    } catch (error: unknown) {
      throw this.handleRequestError(error);
    }
  }
  /**
   * Handles and formats error messages from various error types.
   */
  private getErrorMessage(error: unknown): string {
    if (isAxiosError(error)) {
      return error.response?.data?.message || error.message || "HTTP Error";
    }
    if (error instanceof Error) {
      return error.message;
    }
    return "An unknown error occurred";
  }
  /**
   * Handles errors from HTTP requests.
   */
  private handleRequestError(error: unknown): never {
    const message = this.getErrorMessage(error);
    if (isAxiosError(error)) {
      throw new HTTPError(
        message,
        error.response?.status,
        error.config?.method?.toUpperCase(),
        error.config?.url,
      );
    }
    throw new Error(message);
  }
  /**
   * Sets custom headers for the client instance.
   */
  setHeaders(headers: Record<string, string>): void {
    this.client.defaults.headers.common = {
      ...this.client.defaults.headers.common,
      ...headers,
    };
  }
  /**
   * Gets the current request timeout setting.
   */
  getTimeout(): number {
    return this.client.defaults.timeout || 5000;
  }
  /**
   * Updates the request timeout setting.
   */
  setTimeout(timeout: number): void {
    this.client.defaults.timeout = timeout;
  }
}
</file>

<file path="src/ari-client/utils.ts">
import type { Channel, WebSocketEvent } from "./interfaces";
export function toQueryParams<T>(options: T): string {
  return new URLSearchParams(
    Object.entries(options as Record<string, string>)
      .filter(([, value]) => value !== undefined)
      .map(([key, value]) => [key, value as string]),
  ).toString();
}
export function isPlaybackEvent(
  event: WebSocketEvent,
  playbackId?: string,
): event is Extract<WebSocketEvent, { playbackId: string }> {
  const hasPlayback = "playback" in event && event.playback?.id !== undefined;
  return hasPlayback && (!playbackId || event.playback?.id === playbackId);
}
/**
 * Verifica se um evento pertence a um canal e opcionalmente valida o ID do canal.
 * @param event O evento WebSocket a ser validado.
 * @param channelId Opcional. O ID do canal a ser validado.
 * @returns Verdadeiro se o evento é relacionado a um canal (e ao ID, se fornecido).
 */
export function isChannelEvent(
  event: WebSocketEvent,
  channelId?: string,
): event is Extract<WebSocketEvent, { channel: Channel }> {
  // Verifica se o evento tem a propriedade `channel`
  const hasChannel = "channel" in event && event.channel?.id !== undefined;
  return hasChannel && (!channelId || event.channel?.id === channelId);
}
export const channelEvents = [
  "ChannelCreated",
  "ChannelDestroyed",
  "ChannelEnteredBridge",
  "ChannelLeftBridge",
  "ChannelStateChange",
  "ChannelDtmfReceived",
  "ChannelDialplan",
  "ChannelCallerId",
  "ChannelUserevent",
  "ChannelHangupRequest",
  "ChannelVarset",
  "ChannelTalkingStarted",
  "ChannelTalkingFinished",
  "ChannelHold",
  "ChannelUnhold",
];
</file>

<file path="src/ari-client/websocketClient.ts">
import { EventEmitter } from "events";
import { type IBackOffOptions, backOff } from "exponential-backoff";
import WebSocket from "ws";
import type { AriClient } from "./ariClient";
import type { BaseClient } from "./baseClient.js";
import type { WebSocketEvent, WebSocketEventType } from "./interfaces";
const DEFAULT_MAX_RECONNECT_ATTEMPTS = 30;
const DEFAULT_STARTING_DELAY = 500;
const DEFAULT_MAX_DELAY = 10000;
/**
 * WebSocketClient handles real-time communication with the Asterisk server.
 * Extends EventEmitter to provide event-based communication patterns.
 */
export class WebSocketClient extends EventEmitter {
  private ws?: WebSocket;
  private isReconnecting = false;
  private isConnecting = false; // 🔹 Evita múltiplas conexões simultâneas
  private shouldReconnect = true; // 🔹 Nova flag para impedir reconexão se for um fechamento intencional
  private readonly maxReconnectAttempts = DEFAULT_MAX_RECONNECT_ATTEMPTS;
  private reconnectionAttempts = 0;
  private lastWsUrl: string = "";
  private eventQueue: Map<string, NodeJS.Timeout> = new Map();
  /**
   * Logs the current connection status of the WebSocket client at regular intervals.
   *
   * This method sets up an interval that logs various connection-related metrics every 60 seconds.
   * The logged information includes:
   * - The number of active connections (0 or 1)
   * - The current state of the WebSocket connection
   * - The number of reconnection attempts made
   * - The size of the event queue
   *
   * This can be useful for monitoring the health and status of the WebSocket connection over time.
   *
   * @private
   * @returns {void}
   */
  private logConnectionStatus(): void {
    setInterval(() => {
      console.log({
        connections: this.ws ? 1 : 0,
        state: this.getState(),
        reconnectAttempts: this.reconnectionAttempts,
        eventQueueSize: this.eventQueue.size,
      });
    }, 60000);
  }
  /**
   * Sets up a heartbeat mechanism for the WebSocket connection.
   *
   * This method creates an interval that sends a ping message every 30 seconds
   * to keep the connection alive. The heartbeat is automatically cleared when
   * the WebSocket connection is closed.
   *
   * @private
   * @returns {void}
   */
  private setupHeartbeat(): void {
    const interval = setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        this.ws.ping();
      }
    }, 30000);
    this.ws!.once("close", () => clearInterval(interval));
  }
  private readonly backOffOptions: IBackOffOptions = {
    numOfAttempts: DEFAULT_MAX_RECONNECT_ATTEMPTS,
    startingDelay: DEFAULT_STARTING_DELAY,
    maxDelay: DEFAULT_MAX_DELAY,
    timeMultiple: 2,
    jitter: "full",
    delayFirstAttempt: false,
    retry: (error: Error, attemptNumber: number) => {
      console.warn(
        `Connection attempt #${attemptNumber} failed:`,
        error.message || "Unknown error",
      );
      return attemptNumber < this.maxReconnectAttempts;
    },
  };
  /**
   * Creates a new WebSocketClient instance.
   *
   * This constructor initializes a WebSocketClient with the necessary dependencies and configuration.
   * It ensures that at least one application name is provided.
   *
   * @param baseClient - The BaseClient instance used for basic ARI operations and authentication.
   * @param apps - An array of application names to connect to via the WebSocket.
   * @param subscribedEvents - Optional. An array of WebSocketEventTypes to subscribe to. If not provided, all events will be subscribed.
   * @param ariClient - Optional. The AriClient instance, used for creating Channel and Playback instances when processing events.
   *
   * @throws {Error} Throws an error if the apps array is empty.
   */
  constructor(
    private readonly baseClient: BaseClient,
    private apps: string[],
    private subscribedEvents?: WebSocketEventType[],
    private readonly ariClient?: AriClient,
  ) {
    super();
    if (!apps.length) {
      throw new Error("At least one application name is required");
    }
  }
  /**
   * Establishes a WebSocket connection to the Asterisk server.
   *
   * This method constructs the WebSocket URL using the base URL, credentials,
   * application names, and subscribed events. It then initiates the connection
   * using the constructed URL.
   *
   * @returns A Promise that resolves when the WebSocket connection is successfully established.
   * @throws Will throw an error if the connection cannot be established.
   */
  public async connect(): Promise<void> {
    if (this.isConnecting || this.isConnected()) {
      console.warn(
        "WebSocket is already connecting or connected. Skipping new connection.",
      );
      return;
    }
    this.shouldReconnect = true; // 🔹 Permite reconexão caso o WebSocket caia inesperadamente
    this.isConnecting = true;
    const { baseUrl, username, password } = this.baseClient.getCredentials();
    const protocol = baseUrl.startsWith("https") ? "wss" : "ws";
    const normalizedHost = baseUrl
      .replace(/^https?:\/\//, "")
      .replace(/\/ari$/, "");
    const queryParams = new URLSearchParams();
    queryParams.append("app", this.apps.join(","));
    this.subscribedEvents?.forEach((event) =>
      queryParams.append("event", event),
    );
    this.lastWsUrl = `${protocol}://${encodeURIComponent(username)}:${encodeURIComponent(password)}@${normalizedHost}/ari/events?${queryParams.toString()}`;
    try {
      await this.initializeWebSocket(this.lastWsUrl);
    } finally {
      this.isConnecting = false;
    }
  }
  /**
   * Reconecta o WebSocket com uma lista atualizada de aplicações.
   *
   * @param {string[]} newApps - Lista de aplicações para reconectar
   * @param {WebSocketEventType[]} [subscribedEvents] - Tipos de eventos para se inscrever (opcional)
   * @returns {Promise<void>} Promise resolvida quando reconectado com sucesso
   */
  public async reconnectWithApps(
    newApps: string[],
    subscribedEvents?: WebSocketEventType[],
  ): Promise<void> {
    if (!newApps.length) {
      throw new Error("At least one application name is required");
    }
    // Mesclar aplicações existentes com novas
    const uniqueApps = Array.from(new Set([...this.apps, ...newApps]));
    // Se não há mudanças nas aplicações, não reconectar
    if (
      uniqueApps.length === this.apps.length &&
      uniqueApps.every((app) => this.apps.includes(app))
    ) {
      console.log(
        "No changes in applications list, maintaining current connection",
      );
      return;
    }
    console.log(
      `Reconnecting WebSocket with updated applications: ${uniqueApps.join(", ")}`,
    );
    // Armazenar os aplicativos atualizados
    this.apps = uniqueApps;
    // Atualizar eventos inscritos se fornecidos
    if (subscribedEvents) {
      this.subscribedEvents = subscribedEvents;
    }
    // Fechar conexão existente
    if (this.ws) {
      await new Promise<void>((resolve) => {
        this.once("disconnected", () => resolve());
        this.close();
      });
    }
    // Reconectar com apps atualizados
    await this.connect();
    console.log("WebSocket reconnected successfully with updated applications");
  }
  /**
   * Adiciona novas aplicações à conexão WebSocket existente.
   *
   * @param {string[]} newApps - Lista de novas aplicações para adicionar
   * @param {WebSocketEventType[]} [subscribedEvents] - Tipos de eventos para se inscrever (opcional)
   * @returns {Promise<void>} Promise resolvida quando as aplicações são adicionadas com sucesso
   */
  public async addApps(
    newApps: string[],
    subscribedEvents?: WebSocketEventType[],
  ): Promise<void> {
    if (!newApps.length) {
      throw new Error("At least one application name is required");
    }
    // Verificar se há novas aplicações que ainda não estão na lista
    const appsToAdd = newApps.filter((app) => !this.apps.includes(app));
    if (appsToAdd.length === 0) {
      console.log("All applications are already registered");
      return;
    }
    // Adicionar novas aplicações à lista atual
    await this.reconnectWithApps(appsToAdd, subscribedEvents);
  }
  /**
   * Initializes a WebSocket connection with exponential backoff retry mechanism.
   *
   * This method attempts to establish a WebSocket connection to the specified URL.
   * It sets up event listeners for the WebSocket's 'open', 'message', 'close', and 'error' events.
   * If the connection is successful, it emits a 'connected' event. If it's a reconnection,
   * it also emits a 'reconnected' event with the current apps and subscribed events.
   * In case of connection failure, it uses an exponential backoff strategy to retry.
   *
   * @param wsUrl - The WebSocket URL to connect to.
   * @returns A Promise that resolves when the connection is successfully established,
   *          or rejects if an error occurs during the connection process.
   * @throws Will throw an error if the WebSocket connection cannot be established
   *         after the maximum number of retry attempts.
   */
  private async initializeWebSocket(wsUrl: string): Promise<void> {
    return backOff(async () => {
      return new Promise<void>((resolve, reject) => {
        try {
          this.ws = new WebSocket(wsUrl);
          this.ws.once("open", () => {
            this.setupHeartbeat();
            if (this.isReconnecting) {
              this.emit("reconnected", {
                apps: this.apps,
                subscribedEvents: this.subscribedEvents,
              });
            }
            this.isReconnecting = false;
            this.reconnectionAttempts = 0;
            this.emit("connected");
            resolve();
          });
          this.ws.on("message", (data) => this.handleMessage(data.toString()));
          this.ws.once("close", (code) => {
            // 🔹 Usa `once` para evitar handlers duplicados
            console.warn(
              `WebSocket disconnected with code ${code}. Attempting to reconnect...`,
            );
            if (!this.isReconnecting) {
              this.reconnect(this.lastWsUrl);
            }
          });
          this.ws.once("error", (err: Error) => {
            // 🔹 Usa `once` para evitar acúmulo de eventos
            console.error("WebSocket error:", err.message);
            if (!this.isReconnecting) {
              this.reconnect(this.lastWsUrl);
            }
            reject(err);
          });
        } catch (error) {
          reject(error);
        }
      });
    }, this.backOffOptions);
  }
  private getEventKey(event: WebSocketEvent): string {
    // Cria uma chave única baseada no tipo de evento e IDs relevantes
    const ids = [];
    if ("channel" in event && event.channel?.id) ids.push(event.channel.id);
    if ("playback" in event && event.playback?.id) ids.push(event.playback.id);
    if ("bridge" in event && event.bridge?.id) ids.push(event.bridge.id);
    return `${event.type}-${ids.join("-")}`;
  }
  private processEvent(event: WebSocketEvent): void {
    if (
      this.subscribedEvents?.length &&
      !this.subscribedEvents.includes(event.type as WebSocketEventType)
    ) {
      return;
    }
    if ("channel" in event && event.channel?.id && this.ariClient) {
      const instanceChannel = this.ariClient.Channel(event.channel.id);
      instanceChannel.emitEvent(event);
      event.instanceChannel = instanceChannel;
    }
    if ("playback" in event && event.playback?.id && this.ariClient) {
      const instancePlayback = this.ariClient.Playback(event.playback.id);
      instancePlayback.emitEvent(event);
      event.instancePlayback = instancePlayback;
    }
    if ("bridge" in event && event.bridge?.id && this.ariClient) {
      const instanceBridge = this.ariClient.Bridge(event.bridge.id);
      instanceBridge.emitEvent(event);
      event.instanceBridge = instanceBridge;
    }
    this.emit(event.type, event);
  }
  /**
   * Handles incoming WebSocket messages by parsing and processing events.
   *
   * This method parses the raw message into a WebSocketEvent, filters it based on
   * subscribed events (if any), processes channel and playback events, and emits
   * the event to listeners. It also handles any errors that occur during processing.
   *
   * @param rawMessage - The raw message string received from the WebSocket connection.
   * @returns void This method doesn't return a value but emits events.
   *
   * @throws Will emit an 'error' event if the message cannot be parsed or processed.
   */
  private handleMessage(rawMessage: string): void {
    try {
      const event: WebSocketEvent = JSON.parse(rawMessage);
      // Debounce eventos similares
      const key = this.getEventKey(event);
      const existing = this.eventQueue.get(key);
      if (existing) {
        clearTimeout(existing);
      }
      this.eventQueue.set(
        key,
        setTimeout(() => {
          this.processEvent(event);
          this.eventQueue.delete(key);
        }, 100),
      );
    } catch (error) {
      console.error("Error processing WebSocket message:", error);
      this.emit("error", new Error("Failed to decode WebSocket message"));
    }
  }
  /**
   * Attempts to reconnect to the WebSocket server using an exponential backoff strategy.
   *
   * This method is called when the WebSocket connection is closed unexpectedly.
   * It increments the reconnection attempt counter, logs the attempt, and uses
   * the backOff utility to retry the connection with exponential delays between attempts.
   *
   * @param wsUrl - The WebSocket URL to reconnect to.
   * @returns void - This method doesn't return a value.
   *
   * @emits reconnectFailed - Emitted if all reconnection attempts fail.
   */
  private async reconnect(wsUrl: string): Promise<void> {
    if (!this.shouldReconnect) {
      console.warn(
        "Reconnection skipped because WebSocket was intentionally closed.",
      );
      return;
    }
    if (this.isReconnecting) {
      console.warn("Já há uma tentativa de reconexão em andamento.");
      return;
    }
    this.isReconnecting = true;
    this.reconnectionAttempts++;
    console.log(`Tentando reconexão #${this.reconnectionAttempts}...`);
    backOff(() => this.initializeWebSocket(wsUrl), this.backOffOptions)
      .catch((error) => {
        console.error(`Falha ao reconectar: ${error.message}`);
        this.emit("reconnectFailed", error);
      })
      .finally(() => {
        this.isReconnecting = false;
      });
  }
  /**
   * Closes the WebSocket connection if it exists.
   *
   * This method attempts to gracefully close the WebSocket connection
   * and sets the WebSocket instance to undefined. If an error occurs
   * during the closing process, it will be caught and logged.
   *
   * @throws {Error} Logs an error message if closing the WebSocket fails.
   */
  public async close(): Promise<void> {
    if (!this.ws) {
      console.warn("No WebSocket connection to close");
      return;
    }
    console.log("Closing WebSocket connection.");
    this.shouldReconnect = false;
    // Limpar event queue
    this.eventQueue.forEach((timeout) => clearTimeout(timeout));
    this.eventQueue.clear();
    const closeTimeout = setTimeout(() => {
      if (this.ws && this.ws.readyState !== WebSocket.CLOSED) {
        this.ws.terminate();
      }
    }, 5000);
    try {
      this.ws.removeAllListeners();
      await new Promise<void>((resolve) => {
        this.ws!.once("close", () => {
          clearTimeout(closeTimeout);
          resolve();
        });
        this.ws!.close();
      });
    } catch (error) {
      console.error("Error closing WebSocket:", error);
    } finally {
      this.ws = undefined;
      this.emit("disconnected");
    }
  }
  /**
   * Checks if the WebSocket connection is currently open and active.
   *
   * This method provides a way to determine the current state of the WebSocket connection.
   * It checks if the WebSocket's readyState property is equal to WebSocket.OPEN,
   * which indicates an active connection.
   *
   * @returns {boolean} True if the WebSocket connection is open and active, false otherwise.
   */
  public isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }
  /**
   * Retrieves the current state of the WebSocket connection.
   *
   * This method provides a way to check the current state of the WebSocket connection.
   * It returns a number corresponding to one of the WebSocket readyState values:
   * - 0 (CONNECTING): The connection is not yet open.
   * - 1 (OPEN): The connection is open and ready to communicate.
   * - 2 (CLOSING): The connection is in the process of closing.
   * - 3 (CLOSED): The connection is closed or couldn't be opened.
   *
   * If the WebSocket instance doesn't exist, it returns WebSocket.CLOSED (3).
   *
   * @returns {number} A number representing the current state of the WebSocket connection.
   */
  public getState(): number {
    return this.ws?.readyState ?? WebSocket.CLOSED;
  }
  /**
   * Cleans up the WebSocketClient instance, resetting its state and clearing resources.
   *
   * This method performs the following cleanup operations:
   * - Clears the event queue and cancels any pending timeouts.
   * - Stops any ongoing reconnection attempts.
   * - Clears the stored WebSocket URL.
   * - Resets the reconnection attempt counter.
   * - Removes all event listeners attached to this instance.
   *
   * This method is typically called when the WebSocketClient is no longer needed or
   * before reinitializing the client to ensure a clean slate.
   *
   * @returns {void} This method doesn't return a value.
   */
  public cleanup(): void {
    // Limpar event queue
    this.eventQueue.forEach((timeout) => clearTimeout(timeout));
    this.eventQueue.clear();
    // Parar tentativas de reconexão
    this.shouldReconnect = false;
    this.isReconnecting = false;
    // Limpar URL armazenada
    this.lastWsUrl = "";
    // Resetar contadores
    this.reconnectionAttempts = 0;
    // Remover todos os listeners
    this.removeAllListeners();
  }
}
</file>

<file path="src/index.ts">
/**
 * @file Main entry point for the Asterisk REST Interface (ARI) client package
 * @description This file exports all the necessary classes, types and interfaces for interacting with the ARI
 */
/**
 * Main client class for interacting with Asterisk REST Interface
 * @packageDocumentation
 */
export { AriClient } from "./ari-client/ariClient.js";
/**
 * Resource Classes
 * These classes provide direct access to ARI resources
 */
export {
  Channels,
  ChannelInstance,
} from "./ari-client/resources/channels.js";
export { Endpoints } from "./ari-client/resources/endpoints.js";
export { Applications } from "./ari-client/resources/applications.js";
export { Sounds } from "./ari-client/resources/sounds.js";
export {
  Playbacks,
  PlaybackInstance,
} from "./ari-client/resources/playbacks.js";
export { Asterisk } from "./ari-client/resources/asterisk.js";
export { Bridges, BridgeInstance } from "./ari-client/resources/bridges.js";
/**
 * Type Definitions
 * These types and interfaces define the shape of data structures used throughout the API
 */
// Configuration Types
export type {
  AriClientConfig,
  AriApplication,
} from "./ari-client/interfaces/index.js";
// Channel Related Types
export type {
  Channel,
  ChannelEvent,
  ChannelPlayback,
  ChannelVar,
  ChannelDialplan,
  OriginateRequest,
  RecordingOptions,
  SnoopOptions,
  ExternalMediaOptions,
  RTPStats,
} from "./ari-client/interfaces/index.js";
// Playback Related Types
export type {
  Playback,
  PlaybackEvent,
  PlaybackOptions,
  PlaybackControlRequest,
  PlayMediaRequest,
} from "./ari-client/interfaces/index.js";
// Bridge Related Types
export type {
  Bridge,
  BridgePlayback,
  CreateBridgeRequest,
  RemoveChannelRequest,
  AddChannelRequest,
} from "./ari-client/interfaces/index.js";
// Endpoint Related Types
export type {
  Endpoint,
  EndpointDetails,
} from "./ari-client/interfaces/index.js";
// Application Related Types
export type {
  Application,
  ApplicationDetails,
} from "./ari-client/interfaces/index.js";
// Sound Related Types
export type {
  Sound,
  SoundListRequest,
} from "./ari-client/interfaces/index.js";
// Asterisk Related Types
export type {
  AsteriskInfo,
  Module,
  Logging,
  Variable,
  AsteriskPing,
} from "./ari-client/interfaces/index.js";
// WebSocket Related Types
export type {
  WebSocketEvent,
  WebSocketEventType,
} from "./ari-client/interfaces/index.js";
</file>

<file path=".gitignore">
*.swp
node_modules/
doc/
.nyc_output/
coverage/
.idea/
*.log
</file>

<file path=".npmignore">
dev
doc
examples
test
Gruntfile.js
.jshint
*.log
</file>

<file path="biome.json">
{
  "$schema": "https://biomejs.dev/schemas/1.8.3/schema.json",
  "formatter": {
    "enabled": true,
    "formatWithErrors": false,
    "indentStyle": "space",
    "indentWidth": 2,
    "lineEnding": "lf",
    "lineWidth": 80
  },
  "organizeImports": {
    "enabled": true
  },
  "linter": {
    "ignore": ["dist/**/*", "node_modules/**/*", "build/**/*"],
    "include": ["src/**/*.ts"],
    "enabled": true,
    "rules": {
      "recommended": false,
      "complexity": {
        "noBannedTypes": "error",
        "noExtraBooleanCast": "error",
        "noMultipleSpacesInRegularExpressionLiterals": "error",
        "noUselessCatch": "error",
        "noUselessTypeConstraint": "error",
        "noWith": "error"
      },
      "correctness": {
        "noConstAssign": "error",
        "noConstantCondition": "error",
        "noEmptyCharacterClassInRegex": "error",
        "noEmptyPattern": "error",
        "noGlobalObjectCalls": "error",
        "noInvalidConstructorSuper": "error",
        "noInvalidNewBuiltin": "error",
        "noNonoctalDecimalEscape": "error",
        "noPrecisionLoss": "error",
        "noSelfAssign": "error",
        "noSetterReturn": "error",
        "noSwitchDeclarations": "error",
        "noUndeclaredVariables": "error",
        "noUnreachable": "error",
        "noUnreachableSuper": "error",
        "noUnsafeFinally": "error",
        "noUnsafeOptionalChaining": "error",
        "noUnusedLabels": "error",
        "noUnusedPrivateClassMembers": "error",
        "noUnusedVariables": "warn",
        "useArrayLiterals": "off",
        "useIsNan": "error",
        "useValidForDirection": "error",
        "useYield": "error"
      },
      "style": {
        "noNamespace": "warn",
        "useAsConstAssertion": "error",
        "useImportType": "warn"
      },
      "suspicious": {
        "noAsyncPromiseExecutor": "off",
        "noCatchAssign": "error",
        "noClassAssign": "error",
        "noCompareNegZero": "error",
        "noControlCharactersInRegex": "error",
        "noDebugger": "error",
        "noDuplicateCase": "error",
        "noDuplicateClassMembers": "error",
        "noDuplicateObjectKeys": "error",
        "noDuplicateParameters": "error",
        "noEmptyBlockStatements": "error",
        "noExtraNonNullAssertion": "error",
        "noFallthroughSwitchClause": "error",
        "noFunctionAssign": "error",
        "noGlobalAssign": "error",
        "noImportAssign": "error",
        "noMisleadingCharacterClass": "error",
        "noMisleadingInstantiator": "error",
        "noPrototypeBuiltins": "error",
        "noRedeclare": "error",
        "noUnsafeDeclarationMerging": "error",
        "noUnsafeNegation": "error",
        "useGetterReturn": "error",
        "useValidTypeof": "error"
      }
    }
  }
}
</file>

<file path="build.js">
import { execSync } from "node:child_process";
import * as esbuild from "esbuild";
import fs from "node:fs";
import path from "node:path";
// Caminhos principais
const entryFile = "src/index.ts"; // Entrada principal
const typesOutputDir = "dist/types"; // Saída dos tipos
const esmOutputFile = "dist/esm/index.js"; // Saída ESM
const cjsOutputFile = "dist/cjs/index.cjs"; // Saída CommonJS
try {
  // 1. Gera os arquivos de tipagem (.d.ts)
  console.log("Gerando arquivos de declaração (.d.ts)...");
  execSync(
    `tsc --emitDeclarationOnly --declaration --declarationDir ${typesOutputDir}`,
    { stdio: "inherit" },
  );
  // 2. Build para ESM
  console.log("Gerando build ESM...");
  esbuild.buildSync({
    entryPoints: [entryFile],
    bundle: true,
    minify: false, // Opcional: minify
    sourcemap: true,
    format: "esm",
    outfile: esmOutputFile,
    target: ["esnext"],
    platform: "node",
    external: [
      // Dependências marcadas como externas
      "util",
      "stream",
      "axios",
      "ws",
      "form-data",
    ],
  });
  // 3. Build para CommonJS
  console.log("Gerando build CommonJS...");
  esbuild.buildSync({
    entryPoints: [entryFile],
    bundle: true,
    minify: false, // Opcional: minify
    sourcemap: true,
    format: "cjs",
    outfile: cjsOutputFile,
    target: ["esnext"],
    platform: "node",
    external: ["util", "stream", "axios", "ws", "form-data"],
  });
  // 4. Certifique-se de que o arquivo package.json do CJS tem type=commonjs
  const cjsDir = path.dirname(cjsOutputFile);
  const cjsPackageJson = path.join(cjsDir, 'package.json');
  console.log("Criando package.json para compatibilidade CommonJS...");
  fs.writeFileSync(
    cjsPackageJson,
    JSON.stringify({ type: "commonjs" }, null, 2)
  );
  console.log("Build concluído com sucesso!");
} catch (error) {
  console.error("Erro durante o processo de build:", error);
  process.exit(1);
}
</file>

<file path="package.json">
{
  "name": "@ipcom/asterisk-ari",
  "version": "0.0.160",
  "type": "module",
  "description": "JavaScript client for Asterisk REST Interface.",
  "homepage": "https://github.com/fabiotheo/ipcom-asterisk-ari",
  "keywords": ["Asterisk", "ARI", "Node.js", "TypeScript"],
  "types": "./dist/types/index.d.ts",
  "main": "./dist/cjs/index.cjs",
  "module": "./dist/esm/index.js",
  "scripts": {
    "test": "echo \"Error: no test specified\" && exit 1",
    "build": "node build.js",
    "prepublishOnly": "npm run build",
    "start": "node dist/esm/index.js",
    "docs": "./node_modules/.bin/typedoc && touch docs/.nojekyll"
  },
  "repository": {
    "type": "git",
    "url": "git://fabiotheo/ipcom-asterisk-ari"
  },
  "bugs": {
    "url": "https://ipcom.com.br",
    "email": "<EMAIL>"
  },
  "license": "Apache-2.0",
  "author": "Fábio Fernandes Theodoro",
  "dependencies": {
    "@types/ws": "^8.5.13",
    "axios": "^1.7.8",
    "exponential-backoff": "^3.1.1",
    "uuid": "^11.0.3",
    "ws": "^8.18.0"
  },
  "engines": {
    "node": ">=20"
  },
  "exports": {
    ".": {
      "types": "./dist/types/index.d.ts",
      "require": "./dist/cjs/index.cjs",
      "import": "./dist/esm/index.js",
      "default": "./dist/esm/index.js"
    }
  },
  "files": ["dist"],
  "devDependencies": {
    "@biomejs/biome": "1.9.4",
    "@types/uuid": "^10.0.0",
    "esbuild": "^0.24.0",
    "typedoc": "^0.26.11",
    "typescript": "5.6"
  },
  "publishConfig": {
    "access": "public"
  }
}
</file>

<file path="README.md">
# @ipcom/asterisk-ari

A modern JavaScript/TypeScript library for interacting with the Asterisk REST Interface (ARI).

## Features

- Complete Asterisk ARI support
- Written in TypeScript with full type support
- WebSocket support for real-time events
- Automatic reconnection management
- Simplified channel and playback handling
- ESM and CommonJS support
- Complete type documentation

## Installation

```bash
npm install @ipcom/asterisk-ari
```

## Basic Usage

### Initial Setup

```typescript
import { AriClient } from '@ipcom/asterisk-ari';

const client = new AriClient({
    host: 'localhost',      // Asterisk host
    port: 8088,            // ARI port
    username: 'username',   // ARI username
    password: 'password',   // ARI password
    secure: false          // Use true for HTTPS/WSS
});
```

### WebSocket Connection

```typescript
// Connect to WebSocket to receive events
await client.connectWebSocket(['myApp']); // 'myApp' is your application name

// Listen for specific events
client.on('StasisStart', event => {
    console.log('New channel started:', event.channel.id);
});

client.on('StasisEnd', event => {
    console.log('Channel ended:', event.channel.id);
});

// Listen for DTMF events
client.on('ChannelDtmfReceived', event => {
    console.log('DTMF received:', event.digit);
});

// Close WebSocket connection
client.closeWebSocket();
```

## Event Instances

### Channel, Bridge and Playback Instances in Events

When working with WebSocket events, you get access to both the raw event data and convenient instance objects that allow direct interaction with the channel or playback:

```typescript
client.on('StasisStart', async event => {
    // event.channel contains the raw channel data
    console.log('New channel started:', event.channel.id);

    // event.instanceChannel provides a ready-to-use ChannelInstance
    const channelInstance = event.instanceChannel;

    // You can directly interact with the channel through the instance
    await channelInstance.answer();
    await channelInstance.play({ media: 'sound:welcome' });
});

client.on('BridgeCreated', async event => {
    // event.bridge contains the raw bridge data
    console.log('Bridge created:', event.bridge.id);

    // event.instanceBridge provides a ready-to-use BridgeInstance
    const bridgeInstance = event.instanceBridge;

    // Direct control through the instance
    await bridgeInstance.add({ channel: ['channel-id-1', 'channel-id-2'] });
});

// Similarly for playback events
client.on('PlaybackStarted', async event => {
  // event.playback contains the raw playback data
  console.log('Playback ID:', event.playback.id);

  // event.instancePlayback provides a ready-to-use PlaybackInstance
  const playbackInstance = event.instancePlayback;
  
  // Direct control through the instance
  await playbackInstance.control('pause');
});
```

This approach provides two key benefits:
1. No need to manually create instances using `client.Channel()` or `client.Playback()`
2. Direct access to control methods without additional setup

### Comparing Approaches

Traditional approach:
```typescript
client.on('StasisStart', async event => {
  // Need to create channel instance manually
  const channel = client.Channel(event.channel.id);
  await channel.answer();
});
```

Using instance from event:
```typescript
client.on('StasisStart', async event => {
  // Instance is already available
  await event.instanceChannel.answer();
});
```

This feature is particularly useful when handling multiple events and needing to perform actions on channels or playbacks immediately within event handlers.

### Channel Handling

```typescript
// Create a channel instance
const channel = client.Channel();

// Originate a call
await channel.originate({
  endpoint: 'PJSIP/1000',
  extension: '1001',
  context: 'default',
  priority: 1
});

// Answer a call
await channel.answer();

// Play audio
const playback = await channel.play({
  media: 'sound:welcome'
});

// Hangup the channel
await channel.hangup();
```

### Playback Handling

```typescript
// Create a playback instance
const playback = client.Playback();

// Monitor playback events
playback.on('PlaybackStarted', event => {
  console.log('Playback started:', event.playback.id);
});

playback.on('PlaybackFinished', event => {
  console.log('Playback finished:', event.playback.id);
});

// Control playback
await playback.control('pause');  // Pause
await playback.control('unpause'); // Resume
await playback.control('restart'); // Restart
await playback.stop();            // Stop
```

### Bridge Handling

```typescript
// Create a bridge instance
const bridge = client.Bridge();

// Create a new bridge with specific settings
await bridge.getDetails();

// Add channels to the bridge
await bridge.add({
  channel: ['channel-id-1', 'channel-id-2']
});

// Remove channels from the bridge
await bridge.remove({
  channel: ['channel-id-1']
});

// Play audio on the bridge
const playback = await bridge.playMedia({
  media: 'sound:announcement',
  lang: 'en'
});

// Stop playback on the bridge
await bridge.stopPlayback(playback.id);

// Set video source
await bridge.setVideoSource('video-channel-id');

// Clear video source
await bridge.clearVideoSource();
```

### Specific Channel Monitoring

```typescript
// Create an instance for a specific channel
const channel = client.Channel('channel-id');

// Monitor specific channel events
channel.on('ChannelStateChange', event => {
  console.log('Channel state changed:', event.channel.state);
});

channel.on('ChannelDtmfReceived', event => {
  console.log('DTMF received on channel:', event.digit);
});

// Get channel details
const details = await channel.getDetails();
console.log('Channel details:', details);

// Handle channel variables
await channel.getVariable('CALLERID');
await channel.setVariable('CUSTOM_VAR', 'value');
```

### Channel Playback Handling

```typescript
// Play audio on a specific channel
const channel = client.Channel('channel-id');
const playback = await channel.play({
  media: 'sound:welcome',
  lang: 'en'
});

// Monitor specific playback
playback.on('PlaybackStarted', event => {
  console.log('Playback started on channel');
});

// Control playback
await channel.stopPlayback(playback.id);
await channel.pausePlayback(playback.id);
await channel.resumePlayback(playback.id);
```

### Bridge Event Monitoring

```typescript
// Create an instance for a specific bridge
const bridge = client.Bridge('bridge-id');

// Monitor bridge events
bridge.on('BridgeCreated', event => {
  console.log('Bridge created:', event.bridge.id);
});

bridge.on('BridgeDestroyed', event => {
  console.log('Bridge destroyed:', event.bridge.id);
});

bridge.on('BridgeMerged', event => {
  console.log('Bridge merged:', event.bridge.id);
});

// Get bridge details
const details = await bridge.get();
console.log('Bridge details:', details);

// Monitor channel events in bridge
bridge.on('ChannelEnteredBridge', event => {
  console.log('Channel entered bridge:', event.channel.id);
});

bridge.on('ChannelLeftBridge', event => {
  console.log('Channel left bridge:', event.channel.id);
});
```

## Error Handling

```typescript
try {
  await client.connectWebSocket(['myApp']);
} catch (error) {
  console.error('Error connecting to WebSocket:', error);
}

// Using with async/await
try {
  const channel = client.Channel();
  await channel.originate({
    endpoint: 'PJSIP/1000'
  });
} catch (error) {
  console.error('Error originating call:', error);
}
```

## TypeScript Support

The library provides complete type definitions for all operations:

```typescript
import type {
    Channel,
    Bridge,
    ChannelEvent,
    BridgeEvent,
    WebSocketEvent
} from '@ipcom/asterisk-ari';

// Types will be available for use
const handleChannelEvent = (event: ChannelEvent) => {
  const channelId: string = event.channel.id;
};

// Types will be available for use
const handleBridgeEvent = (event: BridgeEvent) => {
    const bridgeId: string = event.bridge.id;
};
```

## Additional Features

The library provides access to many other ARI features:

- Endpoint handling
- Sound manipulation
- Application control
- Asterisk system information
- And much more...

## Advanced Examples

### Bridge Creation and Channel Management

```typescript
// Create and manage a bridge
const bridge = await client.bridges.createBridge({
    type: 'mixing',
    name: 'myBridge'
});

// Add channels to bridge
await client.bridges.addChannels(bridge.id, {
    channel: ['channel-id-1', 'channel-id-2']
});
```

### Recording Management

```typescript
// Start recording on a channel
const channel = client.Channel('channel-id');
await channel.record({
    name: 'recording-name',
    format: 'wav',
    maxDurationSeconds: 60,
    beep: true
});
```

### External Media

```typescript
// Create external media channel
const channel = await client.channels.createExternalMedia({
    app: 'myApp',
    external_host: 'media-server:8088',
    format: 'slin16'
});
```
