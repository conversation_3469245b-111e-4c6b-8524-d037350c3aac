import { pgEnum } from "drizzle-orm/pg-core";

const FUEL_VALUES = [
	"GASOLINE",
	"DIESEL",
	"ELECTRIC",
	"HYBRID",
	"LPG", // Liquefied Petroleum Gas
	"CNG", // Compressed Natural Gas
	"HYDROGEN",
	"OTHER",
] as const;

export const fuelEnum = pgEnum("fuel_type", FUEL_VALUES);

export type FuelType = (typeof FUEL_VALUES)[number];

export enum Fuel {
	GASOLINE = "GASOLINE",
	DIESEL = "DIESEL",
	ELECTRIC = "ELECTRIC",
	HYBRID = "HYBRID",
	LPG = "LPG",
	CNG = "CNG",
	HYDROGEN = "HYDROGEN",
	OTHER = "OTHER",
}
