// apps/main-api/src/pbx/services/ari-connector.service.ts
import { Injectable, Logger, type OnModuleInit, type OnModuleDestroy } from '@nestjs/common';
import type { HttpService } from '@nestjs/axios'; // Still needed for REST commands
import { firstValueFrom } from 'rxjs';
import type { PbxConfigService } from './pbx-config.service';
import type { PbxEventRouterService } from './pbx-event-router.service';
import { ManagedAriConnection } from './managed-ari-connection.js'; 
import type { AriConnectionDetails, AriOriginateParams } from '@repo/types';
import type { Channel as AriChannelResponse } from 'ari-client'; // Assuming 'ari-client' exports this

@Injectable()
export class AriConnectorService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(AriConnectorService.name);
  private managedConnections = new Map<string, ManagedAriConnection>(); // pbxInstanceId -> ManagedAriConnection

  constructor(
    private readonly pbxConfigService: PbxConfigService,
    private readonly eventRouter: PbxEventRouterService,
    private readonly httpService: HttpService,
  ) {}

  async onModuleInit() {
    this.logger.log('Initializing ARI Connector Service and connecting to configured ARI instances...');
    const ariConfigs = this.pbxConfigService.getAllAriConfigs();
    if (!ariConfigs || ariConfigs.length === 0) {
      this.logger.warn('No Asterisk ARI configurations found or enabled in the database.');
      return;
    }

    for (const pbxConfig of ariConfigs) {
      if (pbxConfig.isEnabled && pbxConfig.connectionDetails.type === 'ASTERISK_ARI') {
        this.createAndConnectInstance(pbxConfig.id, pbxConfig.connectionDetails);
      }
    }
  }

  private createAndConnectInstance(pbxInstanceId: string, connDetails: AriConnectionDetails) {
    if (this.managedConnections.has(pbxInstanceId)) {
      this.logger.warn(`Managed ARI connection for PBX ${pbxInstanceId} already exists.`);
      return;
    }
    const managedConnection = new ManagedAriConnection(
      pbxInstanceId,
      connDetails,
      this.eventRouter,
      this.logger, // Pass parent logger or create a specific one
    );
    this.managedConnections.set(pbxInstanceId, managedConnection);
    managedConnection.connect().catch(err => {
      this.logger.error(`Initial connection failed for ARI PBX ${pbxInstanceId} via ManagedAriConnection:`, err);
      // The ManagedAriConnection itself will handle retries.
    });
  }

  private getAriClientFromManagedConnection(pbxInstanceId: string): any { // Returns 'ari-client' instance
    const managedConn = this.managedConnections.get(pbxInstanceId);
    if (!managedConn || !managedConn.getIsConnected() || !managedConn.getAriClient()) {
      throw new Error(`ARI client for PBX ${pbxInstanceId} not available or not connected.`);
    }
    return managedConn.getAriClient();
  }

  // --- ARI REST Command Methods ---
  async originateCall(pbxInstanceId: string, params: AriOriginateParams): Promise<AriChannelResponse> {
    const ariClientInstance = this.getAriClientFromManagedConnection(pbxInstanceId);
    this.logger.log(`Sending Originate to PBX ${pbxInstanceId}: ${JSON.stringify(params)}`);
    // Assuming your 'ari-client' library's client instance has a method like this:
    return ariClientInstance.channels.originate(params);
  }

  async hangupChannel(pbxInstanceId: string, channelId: string): Promise<void> {
    const ariClientInstance = this.getAriClientFromManagedConnection(pbxInstanceId);
    this.logger.log(`Sending Hangup to PBX ${pbxInstanceId} for channel ${channelId}`);
    await ariClientInstance.channels.hangup({ channelId }); // Adjust to actual method signature
  }

  // Example using HttpService if direct REST calls are preferred for some actions
  // or if the 'ari-client' library doesn't expose a needed command conveniently.
  async getChannelDetailsViaHttp(pbxInstanceId: string, channelId: string): Promise<any> {
    const pbxConfig = this.pbxConfigService.getPbxConfig(pbxInstanceId);
    if (!pbxConfig || pbxConfig.connectionDetails.type !== 'ASTERISK_ARI') {
      throw new Error(`ARI PBX configuration for ${pbxInstanceId} not found.`);
    }
    const connDetails = pbxConfig.connectionDetails;
    const baseHttpUrl = connDetails.url.replace(/\/ari$/, '');
    const apiUrl = `${baseHttpUrl}/ari/channels/${channelId}`;

    try {
      const response = await firstValueFrom(
        this.httpService.get(apiUrl, {
          auth: { username: connDetails.username, password: connDetails.password },
        }),
      );
      return response.data;
    } catch (error: any) {
      this.logger.error(`ARI GET Channel Details failed for PBX ${pbxInstanceId}, channel ${channelId}:`, error.response?.data || error.message);
      throw error;
    }
  }

  async onModuleDestroy() {
    this.logger.log('Destroying AriConnectorService, closing all managed ARI connections...');
    for (const [pbxId, managedConn] of this.managedConnections) {
      try {
        await managedConn.close();
        this.logger.log(`Closed managed ARI connection for PBX ${pbxId}`);
      } catch (error) {
        this.logger.error(`Error closing managed ARI connection for PBX ${pbxId}:`, error);
      }
    }
    this.managedConnections.clear();
  }
}