import { registerAs } from "@nestjs/config";
import * as Rascal from "rascal";

export const rascalConfig = registerAs("rascal", () => {
	const config: Rascal.BrokerConfig = {
		vhosts: {
			"/": {
				// Connection details will now be sourced from environment variables
				// or use defaults if environment variables are not set.
				connection: {
					url: process.env.RABBITMQ_URL || "amqp://guest:guest@localhost:5672", // Use environment variable or a default
					options: {
						heartbeat: 10, // Reduced heartbeat interval for better compatibility
						connectionTimeout: 30000, // Increased connection timeout
					},
					socketOptions: {
						timeout: 30000, // Increased socket timeout
						// Using any to bypass type checking for socket options
						...({
							noDelay: true, // Disable <PERSON><PERSON>'s algorithm
							keepalive: true, // Enable TCP keepalive
							keepaliveDelay: 60000, // Keepalive delay in ms
						} as any),
					},
					retry: {
						min: 1000,
						max: 60000,
						factor: 2,
						strategy: "exponential",
						// Note: The RetryConfig type doesn't support 'attempts' property
					},
				},
				exchanges: {
					default: { type: "topic" },
					nestjs_example: { type: "topic" },
					calls: { type: "topic" },
					call_events_exchange: { type: "topic" },
					taxi_exchange: { type: "topic" },
					marketing_exchange: { type: "topic" },
					public_relations_exchange: { type: "topic" },
					dead_letter_exchange: { type: "topic" },
					drivers_exchange: { type: "topic" },
					application_events: { type: "topic" },
					telegram_exchange: { type: "topic" },
				},
				queues: {
					"call-created-queue": {
						options: {
							arguments: {
								"x-dead-letter-exchange": "dead_letter_exchange",
								"x-dead-letter-routing-key": "call.created.dead",
							},
						},
					},
					"call-created-dead-letter-queue": {
						options: {
							arguments: {
								"x-queue-type": "classic",
							},
						},
					},
					"test-tenant-queue": {
						options: {
							arguments: {
								"x-dead-letter-exchange": "dead_letter_exchange",
							},
						},
					},
					"driver-status-changed-queue": {
						options: {
							arguments: {
								"x-dead-letter-exchange": "dead_letter_exchange",
								"x-dead-letter-routing-key": "driver.status.changed.dead",
							},
						},
					},
					"driver-location-changed-queue": {
						options: {
							arguments: {
								"x-dead-letter-exchange": "dead_letter_exchange",
								"x-dead-letter-routing-key": "driver.location.changed.dead",
							},
						},
					},
					"notification-created-queue": {
						options: {
							arguments: {
								"x-dead-letter-exchange": "dead_letter_exchange",
								"x-dead-letter-routing-key": "notification.created.dead",
							},
						},
					},
					"taxi-company-created-queue": {
						options: {
							arguments: {
								"x-dead-letter-exchange": "dead_letter_exchange",
								"x-dead-letter-routing-key": "taxi.company.created.dead",
							},
						},
					},
					telegram_bot_queue: {
						options: {
							arguments: {
								"x-dead-letter-exchange": "dead_letter_exchange",
								"x-dead-letter-routing-key": "telegram.bot.message.dead",
							},
						},
					},
					"taxi-events-queue": {
						options: {
							arguments: {
								"x-dead-letter-exchange": "dead_letter_exchange",
								"x-dead-letter-routing-key": "taxi.events.dead",
							},
						},
					},
				},
				bindings: [
					"calls[call.created] -> call-created-queue",
					"dead_letter_exchange[call.created.dead] -> call-created-dead-letter-queue",
					"nestjs_example[test.tenant.#] -> test-tenant-queue",
					"drivers_exchange[driver.status.changed] -> driver-status-changed-queue",
					"drivers_exchange[driver.location.changed] -> driver-location-changed-queue",
					"application_events[notification.created] -> notification-created-queue",
					"taxi_exchange[taxi.company.created] -> taxi-company-created-queue",
					"telegram_exchange[telegram.bot.message] -> telegram_bot_queue",
					"taxi_exchange[taxi.events] -> taxi-events-queue",
				],
				publications: {
					"call-created": {
						exchange: "calls",
						routingKey: "call.created",
					},
					"test-tenant": {
						exchange: "nestjs_example",
						routingKey: "test.tenant.message",
					},
					"driver-status-changed": {
						exchange: "drivers_exchange",
						routingKey: "driver.status.changed",
					},
					"driver-location-changed": {
						exchange: "drivers_exchange",
						routingKey: "driver.location.changed",
					},
					"notification-created": {
						exchange: "application_events",
						routingKey: "notification.created",
					},
					"telegram-bot-message": {
						exchange: "telegram_exchange",
						routingKey: "telegram.bot.message",
					},
					"taxi-company-created": {
						exchange: "taxi_exchange",
						routingKey: "taxi.company.created",
					},
					"taxi-events": {
						exchange: "taxi_exchange",
						routingKey: "taxi.events",
					},
				},
				subscriptions: {
					"call-created-subscription": {
						queue: "call-created-queue",
					},
					"test-tenant-subscription": {
						queue: "test-tenant-queue",
					},
					"driver-status-changed-subscription": {
						queue: "driver-status-changed-queue",
					},
					"driver-location-changed-subscription": {
						queue: "driver-location-changed-queue",
					},
					"driver-location-updates": {
						queue: "driver-location-changed-queue",
						prefetch: 10,
						contentType: "application/json",
					},
					"notification-created-subscription": {
						queue: "notification-created-queue",
					},
					"taxi-company-created-subscription": {
						queue: "taxi-company-created-queue",
						prefetch: 10,
						contentType: "application/json",
					},
					telegram_bot_queue: {
						queue: "telegram_bot_queue",
						prefetch: 10,
						contentType: "application/json",
					},
					"taxi-events-subscription": {
						queue: "taxi-events-queue",
						prefetch: 10,
						contentType: "application/json",
					},
				},
			},
		},
	};

	// Merge with Rascal's default configuration
	return Rascal.withDefaultConfig(config);
});
