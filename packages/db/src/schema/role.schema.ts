import { relations } from "drizzle-orm";
import {
	boolean,
	jsonb,
	pgTable,
	uniqueIndex,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { tenants } from "./tenants.schema.js";
import { userBotRoles } from "./user-bot-roles.schema.js";

export const roles = pgTable(
	"roles",
	{
		id: uuid("id").defaultRandom().primaryKey(),
		tenantId: uuid("tenant_id")
			.references(() => tenants.id, {
				onDelete: "cascade",
			})
			.notNull(),
		name: varchar("name", { length: 50 }).notNull(), // e.g., 'admin', 'member'
		description: varchar("description", { length: 1024 }),
		permissions: jsonb("permissions").$type<string[]>(), // Assuming permissions are string arrays
		isDefault: boolean("is_default").notNull().default(false),
		isSystem: boolean("is_system").notNull().default(false),
	},
	(table) => [
		// Unique constraint for role name per tenant
		uniqueIndex("roles_tenant_id_name_idx").on(
			table.tenantId,
			table.name,
		),
	],
);

export const rolesRelations = relations(roles, ({ one, many }) => ({
	tenant: one(tenants, {
		fields: [roles.tenantId],
		references: [tenants.id],
	}),
	userBotRoles: many(userBotRoles),
}));

export type Role = typeof roles.$inferSelect;
export type NewRole = typeof roles.$inferInsert;
