import { pgEnum } from "drizzle-orm/pg-core";

// 1. Define enum values as the single source of truth
const CALL_DIRECTION_VALUES = ["INBOUND", "OUTBOUND"] as const;

// 2. Create the Drizzle ORM pgEnum for database schema definition
export const callDirectionEnum = pgEnum(
	"call_direction",
	CALL_DIRECTION_VALUES,
);

// 3. Define TypeScript union type for strict type safety
export type CallDirectionType = (typeof CALL_DIRECTION_VALUES)[number];

// 4. Define TypeScript string enum for application logic
export enum CallDirection {
	INBOUND = "INBOUND",
	OUTBOUND = "OUTBOUND",
}
