// apps/main-api/src/pbx/services/managed-ari-connection.ts
import { Logger } from '@nestjs/common';
import * as AriClientLib from 'ari-client'; // Assuming 'ari-client' is the library you use
import { EventEmitter } from 'node:events';
import type { PbxEventRouterService } from './pbx-event-router.service';
import type { AriConnectionDetails } from '@repo/types';

export class ManagedAriConnection extends EventEmitter {
  private ari: any; // Type from 'ari-client' library, e.g., AriClientLib.Client
  private isConnecting = false;
  private isConnected = false;
  private reconnectAttempts = 0;
  private readonly MAX_RECONNECT_ATTEMPTS = 10; // Make configurable if needed
  private readonly RECONNECT_DELAY_BASE = 5000; // ms
  private reconnectTimer: NodeJS.Timeout | null = null;
  private readonly logger: Logger;
  private readonly pbxInstanceId: string;
  private readonly connDetails: AriConnectionDetails;
  private readonly eventRouter: PbxEventRouterService;
  private shouldReconnect = true;

  constructor(
    pbxInstanceId: string,
    connDetails: AriConnectionDetails,
    eventRouter: PbxEventRouterService,
    _parentLogger?: Logger, // Marked as potentially unused
  ) {
    super();
    this.pbxInstanceId = pbxInstanceId;
    this.connDetails = connDetails;
    this.eventRouter = eventRouter;
    this.logger = new Logger(`${ManagedAriConnection.name}-${this.pbxInstanceId.substring(0, 8)}`);
  }  public async connect(): Promise<void> {
    if (this.isConnected || this.isConnecting) {
      this.logger.debug("Connection attempt already in progress or established.");
      return;
    }
    this.isConnecting = true;
    this.shouldReconnect = true; // Allow reconnections for this attempt cycle

    const { url, username, password, appName } = this.connDetails;
    this.logger.log(`Attempting to connect to ARI: ${url} for app: ${appName}`);

    try {
      // Close existing client if any (e.g., from a failed previous attempt)
      if (this.ari) {
        try { await this.ari.close(); } catch (e) { /* ignore */ }
        this.ari = null;
      }

      this.ari = await AriClientLib.connect(url, username, password);
      this.logger.log(`ARI HTTP client connected for PBX ${this.pbxInstanceId}.`);

      this.ari.on('error', (err: Error) => {
        this.logger.error(`ARI Client Library Error for PBX ${this.pbxInstanceId}: ${err.message}`, err.stack);
        this.isConnected = false;
        this.emit('error', err); // Emit error for AriConnectorService to potentially handle
        this.scheduleReconnect();
      });

      // StasisStart is critical for new calls entering your app
      this.ari.on('StasisStart', (event: any, channel: any) => {
        this.logger.verbose(`PBX ${this.pbxInstanceId}: StasisStart - Channel: ${channel.id}`);
        this.eventRouter.routeAriEvent(this.pbxInstanceId, 'StasisStart', { ...event, channel });
      });

      // StasisEnd indicates a channel has left your Stasis app
      this.ari.on('StasisEnd', (event: any, channel: any) => {
        this.logger.verbose(`PBX ${this.pbxInstanceId}: StasisEnd - Channel: ${channel.id}`);
        this.eventRouter.routeAriEvent(this.pbxInstanceId, 'StasisEnd', { ...event, channel });
      });

      // Other important events to listen to (adapt based on ari-client's event names)
      this.ari.on('ChannelHangupRequest', (event: any, channel: any) => {
        this.logger.verbose(`PBX ${this.pbxInstanceId}: ChannelHangupRequest - Channel: ${channel.id}`);
        this.eventRouter.routeAriEvent(this.pbxInstanceId, 'ChannelHangupRequest', { ...event, channel });
      });
      
      this.ari.on('ChannelDtmfReceived', (event: any, channel: any) => {
         this.logger.verbose(`PBX ${this.pbxInstanceId}: ChannelDtmfReceived - Channel: ${channel.id}, Digit: ${event.digit}`);
         this.eventRouter.routeAriEvent(this.pbxInstanceId, 'ChannelDtmfReceived', { ...event, channel });
      });

      this.ari.on('ChannelStateChange', (event: any, channel: any) => {
        this.logger.verbose(`PBX ${this.pbxInstanceId}: ChannelStateChange - Channel: ${channel.id}, State: ${channel.state}`);
        this.eventRouter.routeAriEvent(this.pbxInstanceId, 'ChannelStateChange', { ...event, channel });
      });
      
      // ... add listeners for Bridge events, Playback events, etc., as needed,
      // always calling this.eventRouter.routeAriEvent(...)

      // Start the Stasis application for eventing
      await this.ari.start(appName);
      this.logger.log(`Stasis application '${appName}' started for PBX ${this.pbxInstanceId}. Listening for events.`);
      
      this.isConnected = true;
      this.isConnecting = false;
      this.reconnectAttempts = 0;
      if (this.reconnectTimer) {
        clearTimeout(this.reconnectTimer);
        this.reconnectTimer = null;
      }
      this.emit('connected');

    } catch (err: any) {
      this.logger.error(`Failed to connect to ARI for PBX ${this.pbxInstanceId}: ${err.message}`, err.stack);
      this.isConnected = false;
      this.isConnecting = false;
      this.emit('disconnected', err);
      this.scheduleReconnect();
      // Do not re-throw here if scheduleReconnect handles it, or re-throw if connect() is expected to fail hard
    }
  }

  private scheduleReconnect(): void {
    if (!this.shouldReconnect || this.isConnecting) {
      this.logger.debug(`PBX ${this.pbxInstanceId}: Reconnect skipped (shouldReconnect: ${this.shouldReconnect}, isConnecting: ${this.isConnecting})`);
      return;
    }

    if (this.reconnectAttempts >= this.MAX_RECONNECT_ATTEMPTS) {
      this.logger.error(`PBX ${this.pbxInstanceId}: Max reconnection attempts reached. Giving up.`);
      this.emit('error', new Error('Max reconnection attempts reached'));
      return;
    }

    this.reconnectAttempts++;
    const delay = this.RECONNECT_DELAY_BASE * 2 ** Math.min(this.reconnectAttempts -1, 4); // Exponential backoff up to a point
    this.logger.log(`PBX ${this.pbxInstanceId}: Scheduling reconnection attempt ${this.reconnectAttempts} in ${delay}ms.`);

    if (this.reconnectTimer) clearTimeout(this.reconnectTimer);
    this.reconnectTimer = setTimeout(() => {
      this.connect().catch(e => this.logger.error(`PBX ${this.pbxInstanceId}: Error during scheduled reconnect: ${e.message}`));
    }, delay);
  }

  public async close(): Promise<void> {
    this.logger.log(`PBX ${this.pbxInstanceId}: Closing ARI connection intentionally.`);
    this.shouldReconnect = false;
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    if (this.ari) {
      try {
        // ari-client's close() might handle WebSocket and stop Stasis app.
        // If it doesn't stop the Stasis app, you might need: await this.ari.stop(this.connDetails.appName);
        await this.ari.close();
        this.logger.log(`PBX ${this.pbxInstanceId}: ARI client closed.`);
      } catch (error) {
        this.logger.error(`PBX ${this.pbxInstanceId}: Error closing ARI client:`, error);
      } finally {
        this.ari = null;
        this.isConnected = false;
        this.isConnecting = false;
      }
    }
  }

  public getAriClient(): any | null { // Return type from 'ari-client'
    return this.ari;
  }

  public getIsConnected(): boolean {
    return this.isConnected;
  }
}