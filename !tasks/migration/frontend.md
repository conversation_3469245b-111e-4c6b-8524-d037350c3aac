

1. **Database Schema - Gaps and New Entities:** What's defined in `packages/db/src/schema/` and what might be missing or needs refinement based on the old system's features.
2. **Frontend Pages/Views (Next.js `apps/dashboard`):** What pages/major UI sections are implied by the old system's features and how they might map to Next.js.
3. **Backend API/Service Layer (NestJS `apps/main-api`):** What NestJS modules, services, controllers, and gateways are needed, and whether they'd primarily use REST, WebSocket, or interact with RabbitMQ.

Let's break down the new monorepo structure from your JSON files first:

**Monorepo Structure Insights from JSON:**

* **`apps/main-api` (NestJS Backend):**
  * Uses NestJS (`nest-cli.json`, `package.json` dependencies like `@nestjs/core`).
  * Has Biome for formatting/linting.
  * Includes Drizzle ORM (`drizzle-orm` in dependencies).
  * Integrates with RabbitMQ (`@golevelup/nestjs-rabbitmq`, `rascal`).
  * Integrates with <PERSON>is (`@liaoliaots/nestjs-redis`, `ioredis`).
  * Uses GramIO for Telegram bot interactions (`gramio` and related packages). This seems to be a significant part of `main-api`.
  * Has modules for `config`, `database`, `gramiobot`, `health`, `rabbitmq`, `redis`, `scenes`, `services` (locks, tenant management).
  * `tsconfig.json` references `packages/db`.
* **`apps/mini-app` (SolidJS Frontend):**
  * A SolidJS application (vite, `solid-js`, `@solidjs/router`).
  * Uses `@telegram-apps/sdk-solid` and `@tonconnect/ui`, suggesting it's a Telegram Mini App, possibly with TON blockchain integration.
  * This is likely separate from the main operator dashboard.
* **`packages/db` (Database Layer):**
  * Manages Drizzle ORM schema (`src/schema/`), migrations (`scripts/migrate.ts`), and potentially seeding.
  * Contains a very extensive list of schema files, indicating a rich data model is already being defined (e.g., `area.schema.ts`, `bots.schema.ts`, `pbx-call.schema.ts` (likely the new `calls`), `operators.schema.ts`, `tenants.schema.ts`, etc.).
  * Includes enums for various statuses and types.
* **`packages/eslint-config`, `packages/typescript-config`:** Shared linting and TypeScript configurations.
* **Root `package.json`:** Manages workspaces, uses Turbo for monorepo tasks, Bun as package manager.

**Connecting Old Features to New Structure & Identifying Gaps/Plans:**

Based on this, let's outline the plan:

---

## Implementation Plan: Bridging Old Features to New Architecture

This plan details how features from the old system will be implemented in the new Next.js (dashboard) + NestJS (main-api) monorepo, identifying necessary database entities, frontend pages, and backend communication patterns.

### I. Database Schema (`packages/db/src/schema/`)

**Existing Relevant Schemas (from your new `packages/db` structure):**

* **`tenants.schema.ts`**: Foundation for multi-tenancy (if planned, or can be a single default tenant initially).
* **`users.schema.ts`**: General user model. Will need to differentiate or link to operators, drivers, passengers.
* **`operators.schema.ts`**: Specific details for operators (extends `userTenants`). This maps well to the old `operators` table.
  * **Action:** Ensure fields like `disabled` status are present.
* **`pbx-call.schema.ts`**: This is likely the **new `calls` table**.
  * **Action:** Cross-reference with the old `CALLS` data model from `docs/data-models.md`. Ensure all necessary fields are present: `id`, `timestamp`, `timeanswered`, `timeassigned`, `phone`, `location`, `destination`, `area`, `arrivaltime`, `source`, `extension`, `operator_ids` (might be a relation to `operators` or `userTenants`), `comment`, `gisdata` (JSONB), `vehicles` (TEXT[]), `metadata` (JSONB).
* **`scheduled_calls` (Needs a schema if not already present, or managed differently):** The old system used Redis. The new system could:
    1. **Use a dedicated `scheduled_calls.schema.ts` in PostgreSQL:** For persistence, with fields like `id`, `base_call_id` (FK to `pbx-call`), `scheduled_timestamp`, `arrivaltime_offset`, `repeat_pattern` (JSONB for `["HH:MM" or 0, ...]`), `status` ('pending', 'active', 'processed').
    2. **Manage in Redis with more structure:** Similar to the old way but with well-defined keys and potentially using Redis streams or sorted sets for activation.
  * **Decision Point:** PostgreSQL offers better querying and relational integrity. Redis is good for fast access and timers. A hybrid approach might be best (PostgreSQL for master record, Redis for active timers/queue).
  * **Action:** Define `scheduled_calls.schema.ts` or document Redis strategy.
* **`contacts` (Needs a schema or clear Redis strategy):** The old system relied heavily on Redis for contact caching (`contacts:<phone>` HASH, sorted sets for locations/destinations).
  * **Option 1 (DB-centric):** Create `contacts.schema.ts` (`phone` PK, `name`, `first_contact_ts`, `last_action_ts`, `preferred_lang`, `default_destination`) and `contact_history.schema.ts` (`contact_phone` FK, `type` ('location', 'destination'), `value`, `count`, `is_booked`). Redis would be a cache.
  * **Option 2 (Redis-centric, similar to old):** Formalize the Redis structure in documentation.
  * **Action:** Decide and define. The current `packages/db` doesn't show an explicit `contacts` table.
* **`vehicle.schema.ts`**: Defines vehicles.
  * **Action:** Ensure it can store `vehicle_number` (as used in `pbx-call.vehicles`) and any other relevant details (type, capacity).
* **`area.schema.ts`**: Defines geographical areas.
  * **Action:** Ensure it links to `tenants` and can be referenced by `pbx-call.area`.
* **`role.schema.ts` & `user-bot-roles.schema.ts` / `user-tenant.schema.ts`**: Foundation for RBAC.
  * **Action:** Define roles like 'OPERATOR', 'ADMIN'. `user-tenant` links users to tenants with a specific role.
* **`i18n_translation.schema.ts`**: For storing translations if a DB-backed i18n system is chosen over file-based.
* **`audit-log.schema.ts`**: Good for tracking changes.

**Missing/Needs Clarification (based on old system):**

* **`Call Statistics Aggregation Tables`**: While stats can be calculated on-the-fly or cached in Redis (as the old system did), for more complex reporting or historical analysis, pre-aggregated tables in PostgreSQL might be beneficial (e.g., `daily_operator_stats`, `daily_overall_stats`).
  * **Action:** Decide if on-the-fly + Redis caching is sufficient or if aggregated tables are needed. The current `stats.js` logic from the old backend suggests a Redis-first approach for caching daily/detailed stats.
* **`SMS Log/Message Table`**: The old system didn't store SMS content persistently. The new `message.schema.ts` and `chatbot-messages.schema.ts` seem geared towards bot interactions.
  * **Action:** Decide if operator-sent SMS (via PBX gateway) needs to be logged in the database. If so, `message.schema.ts` could be adapted or a new `sms_log.schema.ts` created.

### II. Frontend Pages/Views (Next.js `apps/dashboard`)

The operator dashboard will be the primary interface, replacing the old Angular app.

| Old Feature/View             | New Next.js Page/Component(s) (`apps/dashboard`)                                  | Core Data Entities Involved                                     | Primary Backend Interaction |
| :--------------------------- | :-------------------------------------------------------------------------------- | :-------------------------------------------------------------- | :-------------------------- |
| **Operator Login**           | `/login` (Page)                                                                   | `Operators`                                                     | REST (`/api/getOperators`), WebSocket (connect) |
| **Main Call List View**      | `/` or `/calls` (Page)                                                            | `PbxCall` (new `calls`), `Contacts`                             | WebSocket (`call:init`, `call:add`, `call:update`, `call:remove`, `call:locked`, `call:unlocked`, `call:metadata`) |
|                              |   - Call Row Component                                                            | `PbxCall`                                                       | (Display only, actions trigger WS events) |
|                              |   - Call Filters Component                                                        | (Client-side filtering or WS `call:list` with filters)          | WebSocket (if server-side)  |
|                              |   - Add New Call Modal                                                            | `PbxCall`                                                       | WebSocket (`call:add`)      |
|                              |   - Edit Call Form (inline or modal)                                              | `PbxCall`, `Vehicles`, `Areas`                                  | WebSocket (`call:update`)   |
| **Scheduled Calls View**     | `/scheduled` (Page)                                                               | `ScheduledCall` (new entity/Redis), `PbxCall` (base)            | WebSocket (`schedule:init`, `schedule:add`, `schedule:update`, `schedule:remove`) |
|                              |   - Scheduled Call Row Component                                                  | `ScheduledCall`                                                 | (Display only)              |
|                              |   - Schedule New/Edit Modal                                                       | `ScheduledCall`, `PbxCall`                                      | WebSocket (`schedule:add`, `schedule:update`) |
| **Search Calls / Results**   | `/search` (Modal or Page for form), `/search/results` (Page)                      | `PbxCall`, `Contacts`                                           | WebSocket (`call:list`)     |
| **Daily Statistics View**    | `/statistics/daily` (Page)                                                        | Aggregated Stats (from `PbxCall`)                               | WebSocket (`stats:daily`)   |
|                              |   - Charts & Tables for stats                                                     |                                                                 |                             |
|                              |   - Operator breakdown drilldown                                                  | Aggregated Stats (per operator)                                 | WebSocket (`stats:detailed`) |
| **Mapping Modal**            | Modal component (invoked from Call Row/Edit)                                      | `PbxCall` (for existing GIS data), External Geocoding           | REST (to external geocoding APIs via Next.js API route or direct client call), WebSocket (`call:update` with new GIS data) |
| **Send SMS Modal**           | Modal component (invoked from Call Row or Notifications)                          | `PbxCall` (for context), `Contacts` (for phone)                 | WebSocket (`message:new` type 'sms') |
| **Notifications Area**       | Global Toast/Notification Component (e.g., using `sonner` or `react-toastify`)    | `Message` (from WS), `PbxCall` (for context in Texy notifs)     | WebSocket (receives `message:new`, `server:error`) |
| **Header/Footer/Layout**     | Layout component (`_app.tsx` or `layout.tsx`)                                     | `Operator` (for display name)                                   | (Display only)              |
| **Language Switcher**        | Component in Header/User Menu                                                     | (Client-side state, `next-i18next`)                             | WebSocket (`client:settings` to inform backend) |
| **Operator Shift Stats**     | Component within Header or dedicated stats view                                   | Aggregated Stats (from `PbxCall`)                               | (Display only, data from `client:init` or specific WS event) |

**Actions for Frontend:**

* Create page structure in `apps/dashboard/app/` (or `pages/`).
* Develop reusable UI components (CallRow, Modals, Forms) using shadcn/ui.
* Implement Zustand stores for global state (e.g., operator info, WebSocket connection status) and feature states (e.g., call list, selected call for editing).
* Develop a robust WebSocket service to handle event subscriptions and emissions.
* Integrate `next-i18next` for localization.

### III. Backend API/Service Layer (NestJS `apps/main-api`)

| Feature Domain        | NestJS Module(s)                                     | Key Services                                                                | Controllers/Gateways                                     | Primary Communication Pattern(s) | Key Data Entities (DB/Redis) | Async Tasks (RabbitMQ) - Conceptual |
| :-------------------- | :--------------------------------------------------- | :-------------------------------------------------------------------------- | :------------------------------------------------------- | :------------------------------- | :--------------------------- | :---------------------------------- |
| **Authentication**    | `AuthModule`, `UsersModule`                          | `AuthService`, `UsersService`, `OperatorsService`                           | `AuthController` (REST for `/api/getOperators`), `AuthGateway` (WS for connection auth) | REST, WebSocket                  | `Operators`, `Users`, `Roles`, `UserTenant` | -                                   |
| **Call Management**   | `CallsModule` (`PbxCallsModule`), `ContactsModule`, `OrdersModule` (new) | `PbxCallsService`, `ContactsService`, `OrderService` (handles vehicle assignment logic, SMS for orders), `LockingService` | `CallsGateway` (WebSocket), `PbxEventsController` (REST for PBX events) | WebSocket, REST (from PBX)       | `PbxCall`, `Contacts` (Redis/DB), `Vehicles`, `Areas`, In-memory `lockedCalls`, `assignedCalls` (Redis/memory) | SMS sending for order status |
| **Scheduling**        | `SchedulingModule`                                   | `SchedulingService`                                                         | `SchedulingGateway` (WebSocket)                          | WebSocket                        | `ScheduledCall` (DB/Redis), `PbxCall` | Schedule activation (timer to job) |
| **Messaging**         | `MessagingModule`, `NotificationsModule`             | `MessagingService` (SMS sending), `NotificationService` (WS push)         | `MessagingGateway` (WebSocket for sending), `SmsWebhookController` (REST for incoming SMS) | WebSocket, REST (incoming SMS)   | `Message` (if logging SMS), `Contacts` | SMS sending (if offloaded)      |
| **Statistics**        | `StatisticsModule`                                   | `StatisticsService`                                                         | `StatisticsGateway` (WebSocket)                          | WebSocket                        | `PbxCall`, Stats Cache (Redis) | Daily stats aggregation (batch job) |
| **Mapping**           | `MappingModule` (optional, for config or proxy)      | `MapConfigService` (provides URLs)                                          | `GeocodingProxyController` (REST, optional if client calls directly) | REST (if proxied)                | -                            | -                                   |
| **Contacts**          | `ContactsModule`                                     | `ContactsService`                                                           | `ContactsGateway` (WebSocket)                            | WebSocket                        | `Contacts` (Redis/DB)        | Contact data sync (if from CRM) |
| **System/Core**       | `WebSocketModule`, `ConfigModule`, `DatabaseModule`, `RedisModule`, `RabbitMQModule` | `WebSocketManagerService`, `AppConfigService`, `DatabaseService`, `RedisService`, `RascalService` | `RootGateway` (for `client:init`, `ping`) | WebSocket                        | -                            | -                                   |
| **Telegram Bot**      | `GramioBotModule` (from new structure)               | `BotProcessingService`, `TenantBotFactoryService`, various command handlers | `BotController` (REST webhook)                           | REST (Telegram webhook)          | `Bots`, `Tenants`, `Roles`, `UserBotRoles`, `TelegramUsers`, `TelegramChats`, `TelegramMessages` | Bot message processing (if heavy) |

**Actions for Backend:**

* **Define DTOs:** For all WebSocket event payloads and REST request/response bodies.
* **Implement NestJS Modules, Services, Gateways, Controllers.**
* **WebSocket Gateway (`RootGateway` or feature-specific gateways):**
  * Handle `client:init` to send initial config.
  * Implement handlers for all client-to-server events (`@SubscribeMessage()`).
  * Broadcast server-to-client events.
  * Manage WebSocket connections and operator sessions (link connection to authenticated operator).
* **Services:** Encapsulate all business logic. Interact with Drizzle for DB operations, Redis for caching/state, and RabbitMQ for async tasks.
* **REST Controllers:**
  * `AuthController`: `/api/getOperators`.
  * `PbxEventsController`: New endpoints to replace legacy TCP listener for events like `/api/lineStatusUpd`, `/api/incomingSms`, `/api/vehicleAtDestination`.
  * `GeocodingProxyController` (optional): If you decide to proxy external geocoding calls through the backend.
* **Drizzle ORM Integration:** Use `packages/db` for all database interactions.
* **RabbitMQ Integration (`RascalService`):**
  * Define exchanges, queues, and bindings in `rascal.config.ts`.
  * Publish messages from services (e.g., `OrderService` publishes `order.vehicle.assigned` event).
  * Create consumers/handlers for async tasks (e.g., a `NotificationConsumer` that listens for events and sends SMS/Push).
* **Error Handling:** Implement global exception filters for both HTTP and WebSocket errors.
* **Logging:** Integrate `nestjs-pino` for structured logging.

### IV. Key Decisions & Further Elaboration Needed

1. **Scheduled Calls Storage:** PostgreSQL table (`scheduled_calls`) vs. primarily Redis.
    * *Recommendation:* Store master record in PostgreSQL for durability and querying. Use Redis or a proper job queue (BullMQ backed by Redis, integrated with RabbitMQ for signaling) for managing activation timers.
2. **Contact Data Storage:** DB-centric with Redis cache vs. Redis-primary.
    * *Recommendation:* Store primary contact data (phone, name) in a PostgreSQL `contacts` table. Use Redis for caching frequently accessed data like last locations/destinations (sorted sets) and overall stats (hash). This makes data more durable and queryable.
3. **SMS Logging:** Should operator-sent SMS be logged persistently?
    * *Recommendation:* Yes, create an `sms_logs` table for audit and history.
4. **Real-time Call State (beyond DB):**
    * `lockedCalls`: In-memory Map in `CallManagementService` (scoped to a single NestJS instance) or Redis (if multiple backend instances).
    * `unansweredCalls`, `liveCalls`, `assignedCalls`: Can be managed in Redis for persistence across restarts/scales, or derived from DB + recent events if performance allows. The old system used in-memory maps. Redis is generally better for a scalable backend.
5. **Telegram Bot Integration (`GramIO`):** The `main-api` seems heavily geared towards being a Telegram bot backend. Clarify how this integrates with the operator dashboard. Is the dashboard also for managing bot tenants/interactions, or is it purely for the call center operations? The current database schema suggests a multi-tenant bot platform.
6. **Authentication for External PBX/SMS Gateway Calls to Backend:** The old system had no auth. The new one should implement API key/secret or IP whitelisting for these inbound REST calls.

Excellent! That additional context is crucial and very exciting. Knowing the vision is a multi-tenant, cloud-based "Uber-like" system with chatbot integration and a move towards RabbitMQ for eventing significantly shapes the recommendations.

Let's refine the plan with this new direction in mind, focusing on decoupling, RabbitMQ integration, and how the dashboard fits in.

## Revised Implementation Plan: Cloud-Native, Event-Driven Dispatch System

This plan incorporates the vision of a multi-tenant, cloud-native system with chatbot integration, emphasizing RabbitMQ for event-driven architecture and service decoupling.

### I. Database Schema (`packages/db/src/schema/`) - Refinements for Cloud & Multi-Tenancy

Your existing new schema in `packages/db` already shows a strong foundation for multi-tenancy (`tenants.schema.ts`, `bots.schema.ts`, `user-tenant.schema.ts`, etc.).

**Key Considerations & Actions:**

1. **`pbx-call.schema.ts` (Core Call Log):**
    * **Tenant ID:** Ensure `tenantId` is a non-nullable foreign key. All calls must belong to a tenant.
    * **Source Granularity:** The `source` field should clearly distinguish between calls from:
        * Traditional PBX (e.g., `SOURCE_PBX_INCOMING`, `SOURCE_PBX_OUTGOING`)
        * Operator Manual Entry (e.g., `SOURCE_OPERATOR_MANUAL`)
        * Chatbot (e.g., `SOURCE_CHATBOT_TELEGRAM`, `SOURCE_CHATBOT_VIBER`)
        * Driver App (future) (e.g., `SOURCE_DRIVER_APP`)
        * Passenger App (future) (e.g., `SOURCE_PASSENGER_APP`)
    * **Linking to Chat/Bot Sessions:** If a call originates from or is related to a chatbot interaction, consider adding nullable foreign keys like `chatbot_session_id` or `chatbot_message_id`.
2. **`scheduled_calls.schema.ts` (PostgreSQL):**
    * **Tenant ID:** Mandatory.
    * **Activation Mechanism:** Instead of in-memory timers in NestJS, scheduled call activation should publish an event to RabbitMQ (e.g., `schedule.activation.due`) when `timestamp - arrivaltime_offset` is reached. A dedicated worker service will consume these events.
3. **`contacts.schema.ts` (PostgreSQL):**
    * **Tenant ID:** Mandatory. A contact (phone number) can exist across multiple tenants but their associated data (name, history within that tenant) should be tenant-specific. This might mean a `tenant_contacts` table or ensuring all queries on a global `contacts` table are tenant-scoped.
    * **Action:** Define `contacts.schema.ts` with `tenantId`.
4. **`sms_logs.schema.ts` (PostgreSQL):**
    * **Tenant ID:** Mandatory.
    * Fields: `id`, `tenant_id`, `direction` ('inbound', 'outbound'), `phone_from`, `phone_to`, `message_content`, `status` ('sent', 'failed', 'delivered' - if gateway provides), `gateway_ref_id`, `timestamp`, `related_call_id` (nullable FK to `pbx-call`).
5. **Chatbot Related Schemas (`chatbot-messages`, `chatbot-sessions`, etc.):**
    * These seem well-defined for multi-tenant bot interactions. Ensure `tenantId` is consistently used.
6. **`audit-log.schema.ts`:**
    * Crucial for a cloud system. Ensure `tenantId` is captured for tenant-specific actions and can be null for system-wide actions.

### II. Frontend Pages/Views (Next.js `apps/dashboard`) - Event-Driven Updates

The dashboard will now primarily react to events received via a WebSocket connection that is fed by RabbitMQ messages, rather than direct WebSocket commands for all actions.

| Old Feature/View (Mapped to New) | New Next.js Page/Component(s) (`apps/dashboard`) | Core Data Entities (for display) | Primary Backend Interaction (Initial Load / Actions) | Real-time Updates Via |
| :------------------------------- | :------------------------------------------------- | :------------------------------- | :------------------------------------------------- | :-------------------- |
| **Operator Login**               | `/login`                                           | `Operators`, `Tenants`           | REST (AuthN/AuthZ service)                         | N/A                   |
| **Main Call List View**          | `/dashboard/[tenantId]/calls`                      | `PbxCall`, `Contacts`            | REST (initial load from `CallQueryService`), **RabbitMQ** (for actions like assign vehicle, which publishes an event) | WebSocket (fed by RabbitMQ: `call.created`, `call.updated`, `call.status.changed`) |
|  - Add New Call Modal            | Modal within `/calls`                              | `PbxCall`                        | REST (to `CallCommandService` to publish `call.create.request` event to RabbitMQ) | (Success/failure via WS) |
|  - Edit Call Form                | Modal or inline in `/calls`                        | `PbxCall`, `Vehicles`, `Areas`   | REST (to `CallCommandService` to publish `call.update.request` event) | (Success/failure via WS) |
| **Scheduled Calls View**         | `/dashboard/[tenantId]/scheduled`                  | `ScheduledCall`                  | REST (initial load), **RabbitMQ** (for actions)    | WebSocket (fed by RabbitMQ: `schedule.created`, `schedule.updated`, `schedule.deleted`) |
|  - Schedule New/Edit Modal       | Modal within `/scheduled`                          | `ScheduledCall`                  | REST (to `SchedulingCommandService` to publish events) | (Success/failure via WS) |
| **Search Calls / Results**       | `/dashboard/[tenantId]/search`                     | `PbxCall`, `Contacts`            | REST (to `CallQueryService`)                       | N/A (static result set) |
| **Daily Statistics View**        | `/dashboard/[tenantId]/statistics/daily`           | Aggregated Stats                 | REST (to `StatisticsQueryService`)                 | N/A (or periodic refresh) |
| **Mapping Modal**                | Modal                                              | `PbxCall`, External Geocoding    | REST (external geocoding), REST (to `CallCommandService` for GIS update event) | (Success/failure via WS) |
| **Send SMS Modal**               | Modal                                              | `PbxCall`, `Contacts`            | REST (to `MessagingCommandService` to publish `sms.send.request` event) | (Success/failure via WS) |
| **Notifications Area**           | Global Toast Component                             | `NotificationEvent` (from WS)    | N/A                                                | WebSocket (fed by RabbitMQ: `notification.user.new`, `system.alert`) |
| **Tenant Management** (New)      | `/admin/tenants`                                   | `Tenants`, `Bots`                | REST (to `TenantAdminService`)                     | WebSocket (for updates if admin is viewing) |
| **Bot Configuration** (New)      | `/dashboard/[tenantId]/bots`                       | `Bots`, `ChatbotConfigs`         | REST (to `BotAdminService`)                        | WebSocket (for updates) |

**Key Frontend Changes:**

* **Primary Data Fetching:** Initial page loads will likely use REST APIs to query "Query Services" in the backend.
* **Actions/Commands:** User actions (creating a call, assigning a vehicle) will send commands via REST to "Command Services" in the backend. These services will validate and then *publish events to RabbitMQ*.
* **Real-time Updates:** The frontend will maintain a single WebSocket connection. A dedicated NestJS service (`WebSocketPushService` or similar) will subscribe to relevant RabbitMQ topics/exchanges and push formatted updates to the connected dashboard clients. This decouples the dashboard from direct WebSocket command handling for many operations.

### III. Backend API/Service Layer (NestJS `apps/main-api`) - Decoupled Services & RabbitMQ

This is where the most significant architectural shift happens.

**Core Principles for Backend Services:**

* **Command Query Responsibility Segregation (CQRS) - Lite:**
  * **Command Services:** Handle requests that change state (e.g., `CallCommandService`, `ScheduleCommandService`). They validate commands, publish events to RabbitMQ, and DO NOT return data directly.
  * **Query Services:** Handle requests that retrieve data (e.g., `CallQueryService`, `TenantQueryService`). They read from the database (potentially read-replicas) and return data.
  * **Event Handlers/Workers:** Services that subscribe to RabbitMQ events and perform actions (e.g., update database, send notifications, call external APIs).
* **Event-Driven Architecture:** Changes in one part of the system (e.g., a PBX event creating a call) publish an event. Other interested services consume these events.

**NestJS Modules and Services with RabbitMQ Integration:**

| Domain                | NestJS Module(s)                               | Command Services (Publish to RabbitMQ) | Query Services (Read from DB) | Event Handlers/Workers (Consume from RabbitMQ) | REST Controllers (for Commands/Queries) | WebSocket Gateway (for Pushing Updates) | Key Data Entities |
| :-------------------- | :--------------------------------------------- | :------------------------------------- | :---------------------------- | :------------------------------------------- | :-------------------------------------- | :-------------------------------------- | :---------------- |
| **Authentication**    | `AuthModule`, `UsersModule`                    | -                                      | `UserQueryService`            | -                                            | `AuthController` (login, get_operators) | -                                       | `Operators`, `Users`, `Tenants`, `Roles` |
| **Call Management**   | `CallsModule`, `ContactsModule`, `PbxIntegrationModule` | `CallCommandService` (create, update, assign_vehicle, lock, cancel) | `CallQueryService`, `ContactQueryService` | `CallEventHandlerService` (updates DB from `pbx.call.created`, `call.vehicle.assigned` etc.), `PbxEventHandlerService` (processes raw PBX events, publishes domain events like `pbx.call.incoming`) | `CallController` (for commands), `CallQueryController` (for queries), `PbxEventController` (for PBX webhook) | `DashboardPushGateway` | `PbxCall`, `Contacts`, `Vehicles`, `Areas` |
| **Scheduling**        | `SchedulingModule`                             | `ScheduleCommandService` (create, update, delete, activate_now) | `ScheduleQueryService`        | `ScheduleActivationWorker` (consumes `schedule.activation.due`, publishes `call.create.request` for active call) | `ScheduleController`, `ScheduleQueryController` | `DashboardPushGateway` | `ScheduledCall` |
| **Messaging (SMS)**   | `MessagingModule`                              | `SmsCommandService` (send_sms_request) | -                             | `SmsSendingWorker` (consumes `sms.send.request`, calls SMS Gateway, publishes `sms.sent` or `sms.failed`), `IncomingSmsHandler` (consumes `sms.gateway.incoming`, publishes `sms.received.internal`) | `SmsController` (for commands), `SmsWebhookController` (for gateway status/incoming) | `DashboardPushGateway` | `SmsLog` |
| **Notifications**     | `NotificationsModule`                          | -                                      | -                             | `NotificationDispatchWorker` (consumes various events like `call.missed`, `user.alert`, formats and publishes `websocket.push.notification`) | -                                       | `DashboardPushGateway` (consumes `websocket.push.notification`) | - |
| **Statistics**        | `StatisticsModule`                             | -                                      | `StatisticsQueryService`      | `StatisticsAggregationWorker` (optional, for pre-calculating, consumes `call.completed`, etc.) | `StatisticsQueryController`             | -                                       | `PbxCall`, Stats Cache (Redis) |
| **Contacts**          | `ContactsModule`                               | `ContactCommandService` (update_name)  | `ContactQueryService`         | `ContactEventHandlerService` (updates contact aggregates from call events) | `ContactController`, `ContactQueryController` | `DashboardPushGateway` | `Contacts` |
| **Chatbots**          | `GramioBotModule`, `ChatbotLogicModule`        | `ChatbotCommandService` (e.g., send_message_to_user) | `ChatbotQueryService`       | `TelegramEventHandlerService`, `ViberEventHandlerService` (consume platform events, publish internal `chatbot.message.received`), `ChatbotMessageProcessor` (consumes internal events, applies NLP/logic, publishes `chatbot.send.reply`) | `BotWebhookController` (for Telegram, Viber webhooks) | - | `Bots`, `ChatbotMessages`, `ChatbotSessions`, `Tenants` |
| **Tenant Admin**      | `TenantAdminModule`                            | `TenantAdminCommandService`            | `TenantAdminQueryService`     | -                                            | `TenantAdminController`                 | -                                       | `Tenants`, `Bots`, `Roles` |
| **WebSocket Push**    | `WebSocketPushModule`                          | -                                      | -                             | `WebSocketPushService` (subscribes to specific RabbitMQ topics like `dashboard.updates.<tenantId>`, pushes to relevant WS clients) | `DashboardPushGateway` (manages connections) | - |

**RabbitMQ Event Storming (Examples):**

* **PBX Incoming Call:**
    1. PBX -> `PbxEventController` (REST)
    2. `PbxEventController` -> `PbxEventHandlerService`
    3. `PbxEventHandlerService` publishes `pbx.call.incoming` to RabbitMQ.
    4. `CallEventHandlerService` consumes `pbx.call.incoming`, creates `PbxCall` record, publishes `call.created` to RabbitMQ.
    5. `WebSocketPushService` consumes `call.created`, pushes update to relevant dashboards.
* **Operator Assigns Vehicle:**
    1. Dashboard (Next.js) -> `CallController` (REST POST `/calls/{id}/assign-vehicle`)
    2. `CallController` -> `CallCommandService.requestVehicleAssignment(callId, vehicleId, operatorId)`
    3. `CallCommandService` validates, publishes `call.assign-vehicle.request` to RabbitMQ.
    4. `CallEventHandlerService` consumes `call.assign-vehicle.request`, updates `PbxCall` (sets vehicles, timeassigned), publishes `call.vehicle.assigned`.
    5. `WebSocketPushService` consumes `call.vehicle.assigned`, pushes update.
    6. `SmsSendingWorker` (optional) consumes `call.vehicle.assigned`, sends "Vehicle Assigned" SMS.
* **Scheduled Call Activation:**
    1. `ScheduleActivationWorker` (triggered by cron or persistent queue check) finds due schedules.
    2. Publishes `schedule.activate.request` to RabbitMQ.
    3. `SchedulingService` (as an event handler) consumes `schedule.activate.request`, updates `ScheduledCall` status, publishes `call.create.request` (with details from scheduled call) to RabbitMQ.
    4. `CallEventHandlerService` consumes `call.create.request` (as if it's a new call from source 'schedule'), creates `PbxCall`, publishes `call.created`.
    5. `WebSocketPushService` pushes update.

**Decoupling Strategy:**

* **Services are self-contained:** A service should not directly call methods of another service in a different domain if it can be avoided. Instead, it publishes an event, and the other service consumes it.
* **RabbitMQ as the Event Bus:** All significant state changes or commands that require cross-domain action go through RabbitMQ.
* **Database as Single Source of Truth:** Event handlers update the database. Query services read from the database.
* **Frontend (Dashboard) is an Event Consumer:** It primarily reacts to state changes pushed via WebSockets, which are themselves triggered by backend services consuming RabbitMQ events. Actions from the dashboard initiate commands that result in events.

### IV. Key Decisions & Further Elaboration Needed (Revisited)

1. **Scheduled Calls Activation:** Use a robust job queue like BullMQ (backed by Redis) which can be signaled/managed via RabbitMQ messages, or have a dedicated NestJS worker service that polls the `scheduled_calls` table for due items and publishes activation events. *The latter is simpler to start with.*
2. **Contact Data Aggregation:** `ContactEventHandlerService` will consume `call.completed`, `call.canceled` events to update aggregated stats (locations, destinations, served/canceled counts) in the `contacts` table or its related history tables.
3. **Real-time Call State (Locks, Active Lists):**
    * **`lockedCalls`**: Can be managed by a `LockingService` using Redis (e.g., `SETEX lock:call:<callId> <operatorId> NX EX <timeout>`). This makes it distributed.
    * **`unansweredCalls`, `liveCalls`, `assignedCalls`**: These are essentially views/projections of the `PbxCall` table based on status and time. Query services can derive these. For very high-frequency updates or presence, Redis sets/hashes can still be used, updated by event handlers.
4. **Telegram Bot (`GramIO`) Integration with Core System:**
    * When a bot interaction needs to create a dispatch call: The `ChatbotMessageProcessor` (or similar service within `GramioBotModule`) will publish an event like `dispatch.request.chatbot` to RabbitMQ.
    * A `CallEventHandlerService` (or a dedicated `ChatbotDispatchHandlerService`) consumes this, creates a `PbxCall` with source `SOURCE_CHATBOT_TELEGRAM`, and the usual call lifecycle events follow.
    * Updates about the dispatch call (e.g., vehicle assigned, call completed) can be published back to a RabbitMQ topic that the `GramioBotModule` subscribes to, allowing it to send updates back to the Telegram user.
5. **Authentication for External Systems (PBX, SMS Gateway):**
    * Implement API Key authentication for NestJS REST endpoints receiving events from these systems. Store API keys securely (e.g., hashed in DB, managed via Vault).
    * IP Whitelisting as an additional layer.

This revised plan aligns better with your vision of a scalable, event-driven, cloud-native system. The key is the shift towards RabbitMQ for inter-service communication and decoupling, with the dashboard becoming a reactive client to these backend events.
