import { relations, sql } from "drizzle-orm";
import {
	json,
	pgTable,
	timestamp,
	uniqueIndex,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { profileChangeEnum } from "../enums/profile-change.enum.js";
import { users } from "./users.schema.js";

export const userProfileHistory = pgTable(
	"user_profile_history",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		userId: uuid("user_id")
			.references(() => users.id)
			.notNull(),
		changedById: uuid("changed_by_id").references(() => users.id),
		changeType: profileChangeEnum("change_type").notNull(),
		oldValue: json("old_value").notNull(),
		newValue: json("new_value").notNull(),
		changedAt: timestamp("changed_at").notNull().defaultNow(),
		context: varchar("context", { length: 50 }).notNull(),
		deletedAt: timestamp("deleted_at"),
	},
	(table) => [
		uniqueIndex("user_profile_history_user_id_changed_at_idx").on(
			table.userId,
			table.changedAt,
		),
		uniqueIndex("user_profile_history_deleted_at_idx")
			.on(table.deletedAt)
			.where(sql`${table.deletedAt} is not null`),
	],
);

export const userProfileHistoryRelations = relations(
	userProfileHistory,
	({ one }) => ({
		user: one(users, {
			fields: [userProfileHistory.userId],
			references: [users.id],
		}),
		changedByUser: one(users, {
			fields: [userProfileHistory.changedById],
			references: [users.id],
		}),
	}),
);
