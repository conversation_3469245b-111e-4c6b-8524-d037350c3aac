import { config } from "dotenv"; // .js extension not typically needed for node_modules
import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { bots, roles, tenants, userBotRoles } from "../src/schema/index.js";
import { encryptBotToken } from "../src/utils/bot-token.js";

// Load environment variables
config();

// Database connection string
const connectionString = process.env.DATABASE_URL;
const encryptionKey = process.env.TOKEN_ENCRYPTION_KEY;

if (!connectionString) {
	console.error("DATABASE_URL environment variable is not set");
	process.exit(1);
}

if (!encryptionKey) {
	console.error(
		"TOKEN_ENCRYPTION_KEY environment variable is not set. Cannot encrypt bot tokens for seeding.",
	);
	process.exit(1);
}

// Create a postgres client
const sql = postgres(connectionString, { max: 1 });

// Create a drizzle instance
const db = drizzle(sql);

// Seed data
async function seed() {
	try {
		console.log("Seeding database...");

		// Create a tenant
		const [tenant] = await db
			.insert(tenants)
			.values({
				name: "Demo Tenant",
			})
			.returning();

		console.log("Created tenant:", tenant);

		// Create a bot
		const encryptedToken = encryptBotToken(
			"1234567890:ABCDefGhIJKlmNoPQRsTUVwxyZ",
			encryptionKey,
		);

		if (!encryptedToken) {
			console.error("Failed to encrypt bot token during seeding.");
			process.exit(1);
		}

		const [bot] = await db
			.insert(bots)
			.values({
				tenantId: tenant.id,
				token: encryptedToken,
				webhookPathSegment: "demo-bot",
				botUsername: "demo_bot",
				isEnabled: true,
				config: { welcomeMessage: "Hello from the demo bot!" },
			})
			.returning();

		console.log("Created bot:", bot);

		// Create roles
		const [adminRole] = await db
			.insert(roles)
			.values({
				tenantId: tenant.id,
				name: "admin",
				description: "Administrator role with full access",
				permissions: ["read", "write", "delete", "admin"],
				isDefault: false,
				isSystem: true,
			})
			.returning();

		const [memberRole] = await db
			.insert(roles)
			.values({
				tenantId: tenant.id,
				name: "member",
				description: "Regular member with basic access",
				permissions: ["read"],
				isDefault: true,
				isSystem: true,
			})
			.returning();

		console.log("Created roles:", adminRole, memberRole);

		// Create user bot roles
		const adminUserId = 123456789; // Example Telegram user ID
		const memberUserId = 987654321; // Example Telegram user ID

		await db.insert(userBotRoles).values({
			telegramUserId: adminUserId,
			botId: bot.id,
			roleId: adminRole.id,
		});

		await db.insert(userBotRoles).values({
			telegramUserId: memberUserId,
			botId: bot.id,
			roleId: memberRole.id,
		});

		console.log("Created user bot roles");

		console.log("Seeding completed successfully");
	} catch (error) {
		console.error("Error seeding database:", error);
	} finally {
		await sql.end();
		process.exit(0);
	}
}

seed();
