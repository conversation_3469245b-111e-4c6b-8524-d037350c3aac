import { pgEnum } from "drizzle-orm/pg-core";

const CALL_SOURCE_VALUES = [
	"PBX_INCOMING", // Call received from PBX
	"PBX_OUTGOING", // Call initiated via PBX by operator
	"<PERSON><PERSON><PERSON>", // Call manually entered by an operator
	"CHATBOT_TELEGRAM", // Call originating from Telegram bot
	"CHATBOT_VIBER", // Call originating from Viber bot (example)
	"CHATBOT_WHATSAPP", // Call originating from WhatsApp bot (example)
	"SCHEDULED_ACTIVATION", // Call activated from a schedule
	"APP_PASSENGER", // Call from passenger app
	"APP_DRIVER", // Call initiated by driver app
	"API", // Call created via an external API integration
	"DUPLICATE", // Call duplicated from an existing one (maps to old 'multiple')
] as const;

export const callSourceEnum = pgEnum("call_source", CALL_SOURCE_VALUES);

export type CallSourceType = (typeof CALL_SOURCE_VALUES)[number];

export enum CallSource {
	PBX_INCOMING = "PBX_INCOMING",
	PBX_OUTGOING = "PBX_OUTGOING",
	MANUAL = "MANUAL",
	CHATBOT_TELEGRAM = "CHATBOT_TELEGRAM",
	CHATBOT_VIBER = "CHATBOT_VIBER",
	CHATBOT_WHATSAPP = "CHATBOT_WHATSAPP",
	SCHEDULED_ACTIVATION = "SCHEDULED_ACTIVATION",
	APP_PASSENGER = "APP_PASSENGER",
	APP_DRIVER = "APP_DRIVER",
	API = "API",
	DUPLICATE = "DUPLICATE",
}
