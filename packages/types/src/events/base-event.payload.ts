   // @repo/types/src/events/base.event.ts
   export interface ActorContext {
     type: 'USER' | 'OPERATOR' | 'SYSTEM' | 'BOT' | 'PBX_GATEWAY' | 'EXTERNAL_API' | 'ANONYMOUS_USER'; // Added ANONYMOUS_USER
     id?: string;
     ipAddress?: string;
     userAgent?: string;
   }

   export interface BaseEventPayload<TData = any, TMetadata = Record<string, any> | undefined> {
     eventId: string;          // UUID for this specific event instance
     timestamp: string;        // ISO 8601 UTC
     version: string;          // Event schema version, e.g., "1.0", "1.1"
     sourceService: string;    // Name of the NestJS service that published this event
     tenantId: string | null;  // UUID of the tenant, or null for system-wide events
     correlationId?: string;    // ID to trace a request/flow across multiple events
     causationId?: string;      // ID of the event that caused this event (if applicable)
     actor?: ActorContext;      // Who or what initiated the action leading to this event
     data: TData;              // The specific payload for this event type
     metadata?: TMetadata;      // Optional additional metadata for the event
   }