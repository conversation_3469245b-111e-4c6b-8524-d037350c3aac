import {
	backButton,
	initData,
	init as initSDK,
	miniApp,
	setDebug,
	themeParams,
	viewport,
} from "@telegram-apps/sdk-solid";

/**
 * Initializes the application and configures its dependencies.
 */
export function init(debug: boolean): void {
	// Set @telegram-apps/sdk debug mode.
	setDebug(debug);

	// Initialize special event handlers for Telegram Desktop, Android, iOS, etc.
	// Also, configure the package.
	initSDK();

	// Mount all components used in the project.
	backButton.isSupported() && backButton.mount();
	miniApp.mount();
	themeParams.mount();
	initData.restore();
	void viewport.mount().catch((e) => {
		console.error("Something went wrong mounting the viewport", e);
	});

	// Define components-related CSS variables.
	viewport.bindCssVars();
	miniApp.bindCssVars();
	themeParams.bindCssVars();

	// Add Eruda if needed.
	debug &&
		import("eruda").then((lib) => lib.default.init()).catch(console.error);
}
