# gramio

This template autogenerated by [create-gramio](https://github.com/gramiojs/create-gramio)

### Stack

- Telegram Bot API framework - [GramIO](https://gramio.dev/)
- ORM - [Drizzle](https://orm.drizzle.team/) ([PostgreSQL](https://www.postgresql.org/))
- Linter - [Biome](https://biomejs.dev/)
- GramIO plugins - [Scenes](https://gramio.dev/plugins/official/scenes), [I18n](https://gramio.dev/plugins/official/i18n.html), [Auto-retry](https://gramio.dev/plugins/official/auto-retry.html), [Media-group](https://gramio.dev/plugins/official/media-group.html), [Media-cache](https://gramio.dev/plugins/official/media-cache.html), [Auto answer callback query](https://gramio.dev/plugins/official/auto-answer-callback-query), [Autoload](https://gramio.dev/plugins/official/autoload.html), [Session](https://gramio.dev/plugins/official/session.html), [Prompt](https://gramio.dev/plugins/official/prompt.html)
- Others tools - [Docker](https://www.docker.com/)

## Development

Start development services (DB, Redis etc):

```bash
docker compose -f docker-compose.dev.yml up
```

Start the bot:

```bash
bun dev
```

## Session Typing Best Practices in Gramio

### The Problem with Untyped Session Data

When using the [`@gramio/session`](https://gramio.dev/plugins/official/session.html) plugin in TypeScript, it's common to store arbitrary data in the session. However, **omitting explicit types for your session data leads to**:

- Loss of TypeScript's type safety
- No autocompletion in your editor
- Increased risk of runtime errors (undefined properties, wrong value types)
- Harder code maintenance as your bot grows

### Best-Practice Solution: Typed Session Data

The recommended best practice is to **define a TypeScript interface for your session structure and provide it to the plugin**. This:

- Enables full type-checking and autocompletion in your bot code
- Prevents bugs and keeps your session usage consistent
- Makes your intent clear to contributors and future you

**Steps:**

1. **Define a TypeScript interface** describing your session data.
2. **Pass this type to the session plugin** and to the context type, so TypeScript knows about your structure end-to-end.
3. Enjoy safe, autocompleted access everywhere you use session data!

#### See a Full Demo

For a full, self-contained, and commented demonstration—including recommended typing patterns—see [`apps/bot/gramio-session-type-guide.ts`](apps/bot/gramio-session-type-guide.ts) in this repository.

## Migrations

Push schema to Database:

```bash
bunx drizzle-kit push
```

Generate new migration:

```bash
bunx drizzle-kit generate
```

Apply migrations:

```bash
bunx drizzle-kit migrate
```

## Production

Run project in `production` mode:

```bash
docker compose up -d
```
