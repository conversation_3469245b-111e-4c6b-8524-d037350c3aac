import { join } from "node:path";
import type { I18nOptions } from "nestjs-i18n";
import {
	AcceptLanguageResolver,
	HeaderResolver,
	QueryResolver,
} from "nestjs-i18n";

export const i18nConfig: I18nOptions = {
	fallbackLanguage: "en",
	loaderOptions: {
		path: join(__dirname, "../"),
		watch: true,
	},
	resolvers: [
		{ use: QueryResolver, options: ["lang", "locale"] },
		AcceptLanguageResolver,
		new HeaderResolver(["x-lang"]),
	],
};
