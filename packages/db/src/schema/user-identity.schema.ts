import { relations } from "drizzle-orm";
import {
	json,
	pgTable,
	timestamp,
	uniqueIndex,
	uuid,
	varchar, // Moved varchar import
} from "drizzle-orm/pg-core"; // Group imports
import { userRoleEnum } from "../enums/user-role.enum.js"; // Assuming this was intended instead of providerEnum
import { users } from "./users.schema.js";

export const userIdentity = pgTable(
	"user_identity",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		userId: uuid("user_id")
			.references(() => users.id)
			.notNull(),
		provider: userRoleEnum("provider").notNull(), // Changed from providerEnum to userRoleEnum as per your file content
		externalId: varchar("external_id", { length: 255 }).notNull(),
		display: varchar("display", { length: 255 }).notNull(),
		avatarUrl: varchar("avatar_url", { length: 1024 }),
		metadata: json("metadata").notNull().default({}),
		linkedAt: timestamp("linked_at").notNull().defaultNow(),
		unlinkedAt: timestamp("unlinked_at"),
	},
	(table) => [
		uniqueIndex("user_identity_provider_external_id_idx").on(
			table.provider,
			table.externalId,
		),
		uniqueIndex("user_identity_user_id_idx").on(table.userId),
		uniqueIndex("user_identity_provider_idx").on(table.provider),
	],
);

export const userIdentityRelations = relations(userIdentity, ({ one }) => ({
	user: one(users, {
		fields: [userIdentity.userId],
		references: [users.id],
	}),
}));
