// packages/types/src/dtos/telephony.dtos.ts

export interface LineStatusUpdateQueryDto {
	num: string;
	ext: string;
	call_id: string; // External system's call ID
	event: "incoming" | "established" | "terminated" | "hold" | "resume" | string; // Allow other events
}

export interface GetContactNameQueryDto {
	number: string;
}

export interface GetContactNameResponseDto {
	name: string | null;
}

export interface VehicleAtDestinationQueryDto {
	driverNum: string; // Driver's phone number
}

// Response for VehicleAtDestination can be a simple status or more detailed
export interface VehicleAtDestinationResponseDto {
	status: "success" | "error";
	message?: string;
	actionTaken?: string; // e.g., "sms_sent_to_customer"
}