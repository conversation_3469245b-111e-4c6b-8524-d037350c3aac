# Explanation for Multi-Tenancy with Rascal

## Overview of Multi-Tenancy Approaches

There are several approaches to implementing multi-tenancy with Rascal. The following sections outline these approaches and their respective use cases.

### Tenant ID in Routing Key

For topic exchanges, including the `tenantId` in the routing key when publishing is a common approach. This can be achieved by appending the `tenantId` to the routing key (e.g., `call.created.tenant_abc`). Subscribers can then use wildcards to listen to specific or all tenant events. For example:

- `call.created.*` for a global listener
- `call.created.tenant_abc` for a tenant-specific listener

The publications section in Rascal config can utilize a function to dynamically generate routing keys.

### Tenant ID in Message Headers/Payload

Including the `tenantId` in the message payload or headers is crucial for processing messages within the correct tenant context. This approach is particularly useful when a single queue consumes messages for multiple tenants.

### Tenant-Specific Queues (Advanced)

For extreme isolation, dynamically asserting queues per tenant is possible (e.g., `call_events_tenant_abc_q`). While Rascal supports this approach, it adds complexity to managing queue bindings. In most cases, using routing keys + `tenantId` in payload is sufficient.

### Vhosts (Less Common for this type of multi-tenancy)

RabbitMQ vhosts provide complete isolation, including separate users, permissions, exchanges, and queues. This approach is usually overkill for application-level multi-tenancy unless tenants require entirely separate RabbitMQ environments.
