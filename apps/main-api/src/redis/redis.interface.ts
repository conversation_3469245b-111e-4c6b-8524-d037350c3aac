import type { Redis } from "ioredis";

export interface IRedisService {
	// Basic Key-Value Operations
	get(tenantId: string, key: string): Promise<string | null>;
	set(
		tenantId: string,
		key: string,
		value: string | number,
		ttl?: number,
	): Promise<void>;
	del(tenantId: string, key: string): Promise<void>;
	exists(tenantId: string, key: string | string[]): Promise<number>; // Changed to match ioredis 'exists' signature
	expire(tenantId: string, key: string, seconds: number): Promise<number>;
	ttl(tenantId: string, key: string): Promise<number>;
	keys(tenantId: string, pattern: string): Promise<string[]>;
	incr(tenantId: string, key: string): Promise<number>;
	decr(tenantId: string, key: string): Promise<number>;

	// Hash Operations
	hGet(tenantId: string, key: string, field: string): Promise<string | null>;
	hSet(tenantId: string, key: string, field: string, value: string | number): Promise<number>;
	hDel(tenantId: string, key: string, field: string | string[]): Promise<number>;
	hGetAll(tenantId: string, key: string): Promise<Record<string, string>>;
	hmSet(tenantId: string, key: string, data: Record<string, string | number>): Promise<"OK">;
	hExists(tenantId: string, key: string, field: string): Promise<number>; // Changed to match ioredis 'hexists' signature (returns 0 or 1)
	hIncrBy(tenantId: string, key: string, field: string, increment: number): Promise<number>;

	// Set Operations
	sAdd(tenantId: string, key: string, members: (string | number) | (string | number)[]): Promise<number>;
	sRem(tenantId: string, key: string, members: (string | number) | (string | number)[]): Promise<number>;
	sMembers(tenantId: string, key: string): Promise<string[]>;
	sIsMember(tenantId: string, key: string, member: string | number): Promise<number>; // Changed to match ioredis 'sismember' signature (returns 0 or 1)

	// Sorted Set Operations
	zAdd(tenantId: string, key: string, score: number, member: string | number): Promise<number>;
	zAddMultiple(tenantId: string, key: string, members: (string | number | { score: number; member: string | number })[]): Promise<number>;
	zRange(tenantId: string, key: string, start: number, stop: number, withScores?: "WITHSCORES"): Promise<string[]>;
	zRem(tenantId: string, key: string, members: (string | number) | (string | number)[]): Promise<number>;
	zScore(tenantId: string, key: string, member: string | number): Promise<string | null>;
	zIncrBy(tenantId: string, key: string, increment: number, member: string | number): Promise<string>;

	// Pub/Sub
	publish(channel: string, message: string): Promise<number>;
	subscribe(
		channel: string,
		callback: (channel: string, message: string) => unknown,
	): Promise<void>;
	unsubscribe(channel: string): Promise<void>;

	// Utility
	checkHealth(): Promise<{ status: string; error?: string }>;
	getCacheClient(): Redis; // Changed Redis.Redis to Redis
}
