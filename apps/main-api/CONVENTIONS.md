# CONVENTIONS - Comprehensive Guide

    Naming convention typically follows these guidelines:

## PascalCase

    Modules: Use PascalCase and end with Modu<PERSON>. Example: User Module.
    Services: Use PascalCase and end with Service. Example: User Service.
    Controllers: Use PascalCase and end with <PERSON>. Example: User Controller.
    DTOs (Data Transfer Objects): Use PascalCase and end with Dto. Example: CreateUser Dto.
    Interfaces: Use PascalCase and may start with an I. Example: IUser .
    Enums: Use PascalCase. Example: User Role.
    CamelCase is typically used in the following situations in NestJS:

## CamelCase

    Variables: For variable names, use camelCase. Example: userName.
    Function/Method Names: Method names should also be in camelCase. Example: getUser Info().
    Private Properties: Private class properties often use camelCase. Example: private userId.
    CamelCase helps differentiate these elements from classes and types, which follow PascalCase conventions.

## CAPS LOCK for Enum Values

- **Enum Values**: Use `CAPS_LOCK` (uppercase with underscores) for enum values. Example:

  ```typescript
  export enum UserRole {
    ADMIN = "ADMIN",
    USER = "USER",
    GUEST = "GUEST",
  }


## 🗄️ Schema Synchronization: DTOs, Types, and Drizzle ORM

To ensure strong alignment between the database schema, ORM models, and API contracts, follow these conventions:

### Source of Truth

- The **Drizzle ORM schemas** (in `src/core/drizzle/schema`) define the database structure.
- Use **Drizzle-inferred types** (`InferInsertModel`, `InferSelectModel`) as the **base types** for domain models and DTOs.

### Composing DTOs

- **Do NOT manually redefine** database fields in DTOs.
- Instead, **compose DTOs** using utility types:

```typescript
// Example: User Create DTO
import type { InferInsertModel } from 'drizzle-orm';
import { users } from '../core/drizzle/schema';

// Base insert type from Drizzle
export type UserInsert = InferInsertModel<typeof users>;

// Create DTO excludes auto-generated fields
export class CreateUserDto implements Omit<UserInsert, 'id' | 'createdAt' | 'updatedAt'> {
  @IsString()
  username: string;

  @IsString()
  email: string;

  @IsString()
  password: string;

  // ... other fields with validation decorators
}
```

- Use `Pick`, `Omit`, `Partial`, and intersections to tailor DTOs for create/update operations.
- Add **validation decorators** (`@IsString()`, `@IsEmail()`, etc.) on DTO properties.
- Add **Swagger** (`@ApiProperty`) and **GraphQL** (`@Field`) decorators as needed.

### Benefits

- **Reduces duplication** and manual sync effort.
- **Ensures DTOs stay aligned** with the database schema.
- **Improves maintainability** and reduces bugs.

### Guidelines

- Always **derive DTOs and domain types** from Drizzle-inferred types.
- **Document** DTOs with comments indicating their base Drizzle type.
- **Avoid redefining** the same shape manually.
- Allow DTOs to **diverge only when necessary** (e.g., hiding sensitive fields, adding computed fields).
- Consider **automating** DTO and validation schema generation in the future.

This synchronization strategy is **mandatory** for all new DTOs and types to ensure consistency across the codebase.

## � Key Technologies

1. **Backend Framework**: NestJS
2. **Frontend Framework**: React
3. **ORM**: Drizzle
4. **Database**: PostgreSQL
5. **Caching**: Redis
6. **Package Management**: bun
7. **Containerization**: Docker

## 🚀 Deployment Workflow

```mermaid
sequenceDiagram
    participant Dev as Developer
    participant Git as Git Repository
    participant CI as CI/CD
    participant Docker as Docker
    participant Cloud as Cloud Platform

    Dev->>Git: Push Code
    Git->>CI: Trigger Build
    CI->>CI: Run Tests
    CI->>Docker: Build Container
    Docker->>Cloud: Deploy Application
```

## 🔒 Security and Performance Considerations

- **JWT Authentication**
- **Rate Limiting**
- **Caching with Redis**
- **Environment-specific configurations**
- **Strict TypeScript type checking**
- **RBAC (Role-Based Access Control)**

## 🔄 User Role Management

### Database Enum as Source of Truth

- Always use the `UserRole` enum from `database/enums/user-role.enum.ts` as the single source of truth for user roles
- Do not create duplicate role enums in different parts of the application
- Import the enum directly:

```typescript
import { UserRole } from '../../../database/enums/user-role.enum';
```

## 🎭 Scene and Wizard ID Management

### Centralized Scene ID Generation

- Use the `SceneName` enum in `src/feature/bot/constants/scene-ids.ts` as the single source of truth for scene names
- Use the generated `SceneIds` and `WizardIds` objects for scene and wizard IDs:

```typescript
import { SceneIds, WizardIds } from '../../constants/scene-ids';

@Scene(SceneIds.LANGUAGE)
export class LanguageScene {
  // Scene implementation
}

@Wizard(WizardIds.PASSENGER_REGISTRATION)
export class PassengerRegistrationWizard {
  // Wizard implementation
}
```

- When adding a new scene or wizard:
  1. Add the scene name to the `SceneName` enum
  2. The scene and wizard IDs will be automatically generated
  3. Use the generated IDs in your scene or wizard decorators

### Session Validation with Database Roles

- When creating session DTOs, set the role using the database enum:

```typescript
export class CourierSessionDto extends BaseSessionDto {
  constructor(data?: Partial<CourierSessionDto>) {
    super();
    if (data) {
      Object.assign(this, data);
    }
    // Set default role from database enum
    this.role = UserRole.COURIER;
  }
}
```

- Use type guards to safely check session types:

```typescript
export function isCourierSession(session: any): session is CourierSessionDto {
  return session && session.role === UserRole.COURIER;
}
```

- When accessing role-specific properties, always use type guards:

```typescript
if (isCourierSession(ctx.session)) {
  ctx.session.isAvailable = true;
} else {
  // Handle non-courier session
  (ctx.session as any).isAvailable = true;
}
```

### Benefits of Centralized Role Management

- Ensures consistency across database, API, and UI
- Simplifies role-based access control
- Reduces errors from mismatched role values
- Makes it easier to add or modify roles

---

## 🚀 Getting Started

---

## 🚫 DON'Ts

### 1. Never Modify Core Configuration Files

- `tsconfig.json`
- `drizzle.config.ts`
- `package.json` (root)
- Dockerfile
- docker-compose.yml

### 2. Avoid Direct Dependency Modifications

- Use workspace dependencies
- Always add dependencies to specific packages
- Respect existing dependency management

---

## ❗ Important Configuration Guidelines

### 1. Environment Variables

```bash
# Always use .env.local for local development
# Use .env.test for testing
# Production environment in .env
```

### 2. Path Aliases

```typescript
// Good: Use defined path aliases
import { someUtil } from '@packages/utils'
import { CoreService } from '@core/services'

// Bad: Avoid relative imports outside immediate context
import { ... } from '../../../utils/something'
```

## 🔒 Security Recommendations

### 1. Secrets Management

```typescript
// Always use environment variables
const JWT_SECRET = process.env.JWT_SECRET
// Never hardcode secrets

// Use zod for runtime validation
const configSchema = z.object({
  DATABASE_URL: z.string().url(),
  JWT_SECRET: z.string().min(32)
})
```

### 2. Authentication

```typescript
// Use built-in NestJS authentication
@UseGuards(JwtAuthGuard)
class ProtectedController {
  // Implement role-based access control
}
```

### Role-Based Access Control - Nestjs-roles libary

To implement role-based access control, we need to define the roles and create a guard to check for these roles.

#### Step 1: Role Definition

Define roles using TypeScript enums:

```typescript
export enum Role {
  ADMIN = 'ADMIN',
  USER = 'USER',
  DRIVER = 'DRIVER',
  PASSENGER = 'PASSENGER',
  OPERATOR = 'OPERATOR',
  TAXI_OWNER = 'TAXI_OWNER',
  MANAGER = 'MANAGER',
  GUEST = 'GUEST',
}
```

#### Step 2: Guard Creation

The package provides a `createRolesGuard` function that creates a guard based on how to extract roles from the request context:

```typescript
function getRole(context: ExecutionContext) {
  const { session } = context.switchToHttp().getRequest();
  if (!session) return;
  return session.role; // Can return single role or array of roles
}

export const Roles = createRolesGuard<Role>(getRole);
```

#### Step 3: Global Guard Setup

Register the guard globally in the application:

```typescript
const app = await NestFactory.create(AppModule);
const reflector = app.get<Reflector>(Reflector);
app.useGlobalGuards(new Roles(reflector));
```

#### Step 4: Usage in Controllers

The guard can be used in two ways:

- At the controller level to set default access:

```typescript
@Controller('secrets')
@Roles.Params(true) // Allows access to any authenticated user
export class SecretsController {}
```

- At the route level to override controller-level settings:

```typescript
@Patch(':id')
@Roles.Params(Role.ADMIN) // Only allows admin access
async update() {}
```

#### Step 5: Role Checking Logic

The guard implements several access control rules:

- If you specify `false`, only non-authenticated users can access
- If you specify `true`, any authenticated user can access
- If you specify specific roles, only users with those roles can access
- For users with multiple roles, access is granted if they have any of the required roles

#### Step 6: Type Safety

The package ensures type safety by:

- Using TypeScript generics to ensure role types match enum
- Providing compile-time checking of role values
- Ensuring you can't accidentally use invalid role values

---

## 🏗️ Extending the Boilerplate

### 1. Adding New Features

```bash
# Generate NestJS resource
npx @nestjs/cli generate resource users

# Generate React component
# Use your preferred method or CLI

---

## ⚠️ Common Pitfalls

### 1. Performance Considerations

```typescript
// Use lazy loading
@Injectable()
class HeavyService {
  @Inject(lazy(() => DependentService))
  private dependentService: DependentService
}

// Optimize database queries
const results = await this.repository
  .createQueryBuilder()
  .select()
  .where()
  .limit(10)
  .getMany()
```

### 2. Error Handling

```typescript
// Use consistent error handling
@Catch()
class GlobalExceptionFilter implements ExceptionFilter {
  catch(exception: unknown, host: ArgumentsHost) {
    // Centralized error management
  }
}
```

---

## 🔍 Debugging and Monitoring

### 1. Logging

Configuration of the `pino-logger` in a NestJS application, using the `LoggerModule` to set up logging with customizable options. Here's a brief explanation of the implementation:

```typescript
// src/app.module.ts
LoggerModule.forRootAsync({
  inject: [ConfigService],
  useFactory: (configService: ConfigService) => ({
    pinoHttp: {
      level: configService.get<string>("LOG_LEVEL") || "debug", // Set log level from config or default to debug
      transport:
        configService.get("NODE_ENV") !== "production" // Check if not in production
          ? {
              target: "pino-pretty", // Use pretty print for logs in development
              options: {
                colorize: true, // Enable colorized output
                singleLine: true, // Output logs in a single line
                translateTime: "UTC:yyyy-mm-dd HH:MM:ss.l", // Format timestamp
                ignore: "pid,hostname", // Ignore certain fields in logs
              },
            }
          : undefined, // No transport in production
    },
  }),
}),
```

### Key Points

1. **Asynchronous Configuration**: The logger is configured asynchronously using `forRootAsync`, allowing for dependency injection (e.g., `ConfigService`).
2. **Log Level**: The logging level is dynamically set based on the application's configuration, defaulting to "debug" if not specified.
3. **Transport Options**: In non-production environments, the logger uses `pino-pretty` for more readable log output, with options for colorization and timestamp formatting.
4. **Production Settings**: In production, the transport is set to `undefined`, which means logs will not be prettified, optimizing performance.

### Logger Substitution

- The implementation allows for a custom logger to be injected into the NestJS application, replacing the default logger. This is done by calling `app.useLogger(app.get(Logger))` in the `main.ts` file.
- The `bufferLogs` option ensures that logs are buffered until the custom logger is ready, which is crucial for standalone applications.
- The `LoggerService` accept arguments without a second context argument. This change improves compatibility with `pino` logging methods and simplifies usage.

### 2. Tracing

```typescript
// Use OpenTelemetry or similar for distributed tracing
@Span()
async processOrder() {
  // Traceable method
}
```

---

## 💡 Recommended Development Flow

```mermaid
graph TD
    A[Start Development] --> B{Feature/Fix}
    B --> |Small Change| C[Create Branch]
    B --> |Major Feature| D[RFC/Design Discussion]
    C --> E[Implement]
    D --> E
    E --> F[Write Tests]
    F --> G[Run Linters]
    G --> H[Create Pull Request]
    H --> I{Code Review}
    I --> |Approved| J[Merge]
    I --> |Changes Needed| E
```

---

## 🎯 Best Practices Checklist

- [ ] Use TypeScript strict mode
- [ ] Write comprehensive tests
- [ ] Follow SOLID principles
- [ ] Use dependency injection
- [ ] Implement proper error handling
- [ ] Use environment-specific configurations
- [ ] Keep sensitive data out of version control

---

## 🔍 Debugging Workflow

```bash
# Verify workspace configuration
bun list

# Check individual package configurations
for pkg in packages/* apps/*; do
  echo "$pkg package.json:"
  cat "$pkg/package.json"
done

# Validate dependencies
bun install --verify
```

---

## 🚨 Common Pitfalls

- Inconsistent workspace package names
- Missing `package.json` in workspace packages
- Incorrect dependency references
- Not building workspace packages before Docker build

- Always use `bun install --frozen-lockfile` in Docker
- Copy only necessary files in build stages
- Use multi-stage builds to minimize image size
- Verify workspace dependencies before containerization
