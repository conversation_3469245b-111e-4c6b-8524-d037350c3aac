import { relations } from "drizzle-orm";
import {
	index,
	pgTable,
	text,
	timestamp,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { tenants } from "./tenants.schema.js";
import { vehicles } from "./vehicle.schema.js";

export const vehicleNotes = pgTable(
	"vehicle_note",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id")
			.references(() => tenants.id, { onDelete: "cascade" })
			.notNull(),
		vehicleId: uuid("vehicle_id")
			.references(() => vehicles.id, { onDelete: "cascade" })
			.notNull(),
		description: varchar("description", { length: 255 }), // Short title or summary
		note: text("note").notNull(),
		createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
		updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow().notNull().$onUpdate(() => new Date()),
	},
	(table) => [
		index("vehicle_note_tenant_id_idx").on(table.tenantId),
		index("vehicle_note_vehicle_id_idx").on(table.vehicleId),
		index("vehicle_note_created_at_idx").on(table.createdAt),
	],
);

export const vehicleNotesRelations = relations(vehicleNotes, ({ one }) => ({
	tenant: one(tenants, { fields: [vehicleNotes.tenantId], references: [tenants.id] }),
	vehicle: one(vehicles, { fields: [vehicleNotes.vehicleId], references: [vehicles.id] }),
}));

export type VehicleNote = typeof vehicleNotes.$inferSelect;
export type NewVehicleNote = typeof vehicleNotes.$inferInsert;