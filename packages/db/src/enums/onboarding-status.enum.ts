import { pgEnum } from "drizzle-orm/pg-core";

// 1. Define enum values as the single source of truth
const ONBOARDING_STATUS_VALUES = [
	"INCOMPLETE",
	"IN_PROGRESS",
	"COMPLETED",
] as const;

// 2. Create the Drizzle ORM pgEnum for database schema definition
export const onboardingStatusEnum = pgEnum(
	"onboarding_status",
	ONBOARDING_STATUS_VALUES,
);

// 3. Define TypeScript union type for strict type safety
export type OnboardingStatusType = (typeof ONBOARDING_STATUS_VALUES)[number];

// 4. Define TypeScript string enum for application logic
export enum OnboardingStatus {
	INCOMPLETE = "INCOMPLETE",
	IN_PROGRESS = "IN_PROGRESS",
	COMPLETED = "COMPLETED",
}
