import { pgEnum } from "drizzle-orm/pg-core";

// 1. Define enum values as the single source of truth
const PAYMENT_STATUS_VALUES = [
	"PENDING",
	"PROCESSING",
	"COMPLETED",
	"FAILED",
	"REFUNDED",
	"CANCELLED",
] as const;

// 2. Create the Drizzle ORM pgEnum for database schema definition
export const paymentStatusEnum = pgEnum(
	"payment_status",
	PAYMENT_STATUS_VALUES,
);

// 3. Define TypeScript union type for strict type safety
export type PaymentStatusType = (typeof PAYMENT_STATUS_VALUES)[number];

// 4. Define TypeScript string enum for application logic
export enum PaymentStatus {
	PENDING = "PENDING",
	PROCESSING = "PROCESSING",
	COMPLETED = "COMPLETED",
	FAILED = "FAILED",
	REFUNDED = "REFUNDED",
	CANCELLED = "CANCELLED",
}
