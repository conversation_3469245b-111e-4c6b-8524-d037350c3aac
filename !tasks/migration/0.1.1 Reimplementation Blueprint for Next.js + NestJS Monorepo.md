
# Reimplementation Blueprint for Next.js + NestJS Monorepo

---

## 1. Purpose and Scope

- **State clearly**: This document's goal is to serve as the foundational blueprint for rewriting the existing Traksi application (old JavaScript backend + Angular frontend) into a modern monorepo structure. The new architecture will consist of a Next.js frontend and a NestJS backend.
- **Define scope**: This document covers the core features identified in the existing application, their corresponding API contracts (both HTTP and WebSocket), data flow, event handling mechanisms, and the architectural principles guiding the rewrite. It aims to ensure feature parity while leveraging modern technologies for improved scalability, maintainability, and developer experience.

    **Overview of Key Aspects for the Rewrite:**

  - **API Contracts:**
    - **REST APIs (NestJS):** Will be defined using NestJS controllers (`@Get`, `@Post`, etc.) and DTOs for request/response validation. OpenAPI/Swagger documentation will be generated. Primarily for external integrations (like PBX/telephony events) and potentially some administrative functions.
    - **WebSocket API (NestJS Gateway):** The primary communication channel for real-time client-server interactions. Events will be strongly typed.
  - **Data Flow:**
    - **Inbound Requests:**
      - Next.js Frontend → NestJS WebSocket Gateway → NestJS Services → PostgreSQL (Drizzle ORM) / Redis / RabbitMQ (for async tasks).
      - External Systems (PBX, SMS Gateway) → NestJS REST API → NestJS Services → PostgreSQL / Redis / RabbitMQ / WebSocket Gateway (to broadcast updates).
    - **Outbound Data/Responses:**
      - PostgreSQL / Redis → NestJS Services → NestJS WebSocket Gateway → Next.js Frontend (real-time updates).
      - NestJS Services → NestJS Controllers → HTTP Response (for REST API calls).
  - **Cross-Service/Module Communication (Backend):**
    - RabbitMQ for asynchronous tasks and inter-module eventing (e.g., a call update triggering a notification, a scheduled call becoming active).
    - Redis for caching, session management (if applicable beyond WebSocket state), and potentially simple pub/sub for localized backend events.
  - **Event Handling:**
    - **WebSocket Events:** Defined and handled by NestJS WebSocket Gateway and associated services.
    - **RabbitMQ Events (Conceptual for new backend):** For decoupling services (e.g., `call.created`, `sms.sent`, `schedule.activated`). Handlers would subscribe to specific queues/exchanges.
    - **Domain Events (Conceptual for new backend):** Within services, domain events can trigger further actions or publish to RabbitMQ.
  - **Architecture Principles:**
    - **Modular Design (NestJS):** Clear separation into modules (e.g., `AuthModule`, `CallManagementModule`, `SchedulingModule`).
    - **Separation of Concerns:** Controllers/Gateways for network interface, Services for business logic, Repositories/Drizzle for data access.
    - **Type Safety:** End-to-end with TypeScript, Drizzle ORM, and defined DTOs/interfaces.
    - **Resilience:** Retry mechanisms for external API calls, robust error handling, potentially circuit breakers for critical integrations.
    - **Multi-Tenancy (if applicable for future):** Design with tenant isolation in mind for data and processing, even if not an immediate v1 requirement.
  - **Key Strengths of New Architecture:**
    - Improved maintainability and scalability with NestJS and Next.js.
    - Enhanced type safety.
    - Modern development practices and tooling.
    - Clearer API definitions with OpenAPI.
  - **Areas for Improvement (over old system):**
    - Formal API documentation (OpenAPI).
    - Distributed tracing for better observability.
    - More structured error handling and logging.
    - Testability at all levels (unit, integration, e2e).

---

## 2. High-Level Architectural Overview

- **System Diagram**:

    ```mermaid
    graph LR
        subgraph "User Interface"
            A[Next.js Frontend]
        end

        subgraph "Backend Services (NestJS)"
            B[API Gateway: REST & WebSocket]
            C[Auth Service]
            D[Call Management Service]
            E[Scheduling Service]
            F[Messaging Service]
            G[Statistics Service]
            H[Contact Service]
            I[Mapping Service Proxy optional]
            J[Notification Service Push]
        end

        subgraph "Data Stores"
            K[PostgreSQL DB Drizzle ORM]
            L[Redis Cache, Sessions, Queues]
        end

        subgraph "Message Queue"
            M[RabbitMQ Async Tasks, Events]
        end

        subgraph "External Services"
            N[Geocoding API Nominatim/Photon]
            O[PBX/Asterisk Telephony Events, Origination]
            P[SMS Gateway]
        end

        A -- HTTP/WebSocket --> B
        B <--> C
        B <--> D
        B <--> E
        B <--> F
        B <--> G
        B <--> H
        B <--> I
        B --> J

        C -- CRUD --> K
        D -- CRUD --> K
        D -- Cache --> L
        D -- Publishes/Consumes --> M
        E -- CRUD --> L 
        E -- Publishes/Consumes --> M
        F -- Integrates --> P
        F -- Publishes/Consumes --> M
        G -- Reads --> K
        G -- Cache --> L
        H -- CRUD --> K
        H -- Cache --> L
        I -- HTTP --> N
        J -- Pushes to --> A
        B -- HTTP/Telephony API --> O
        O -- HTTP/TCP --> B
        P -- SMS Status --> B

        D -- Uses --> H
        E -- Uses --> D

        %% Dockerized Components
        subgraph "Docker Environment"
            K
            L
            M
            NestApp[NestJS Backend App]
            NextApp[Next.js Frontend App]
        end
        NestApp --> K
        NestApp --> L
        NestApp --> M
        NextApp --> NestApp
    ```

- **Component Breakdown**:
  - **Frontend (Next.js)**:
    - **Routing**: Next.js file-system based routing (`app` or `pages` directory).
    - **State Management**: Zustand for global and feature-specific state. React Context for localized state.
    - **UI Components**: shadcn/ui, custom reusable React components.
    - **i18n**: `next-i18next` or similar for internationalization.
    - **API Communication**: Custom hooks/services using `fetch` or `axios` for REST; a dedicated WebSocket service for real-time.
  - **Backend (NestJS)**:
    - **Modules**: `AuthModule`, `UsersModule`, `CallsModule`, `SchedulingModule`, `MessagingModule`, `StatisticsModule`, `ContactsModule`, `MappingModule` (proxy/config), `WebSocketModule`, `DatabaseModule` (Drizzle), `CacheModule` (Redis), `QueueModule` (RabbitMQ).
    - **WebSocket Gateway**: Handles all real-time event-based communication with the Next.js frontend.
    - **REST API Controllers**: For external system integrations (PBX, SMS gateway status) and potentially admin functionalities.
    - **Services**: Contain business logic for each module.
    - **Data Persistence**: Drizzle ORM for PostgreSQL. Redis for caching and potentially short-lived state.
  - **Shared (`packages/` in monorepo)**:
    - `packages/types`: TypeScript interfaces for API payloads, WebSocket events, DTOs, database entities.
    - `packages/utils`: Common utility functions.
    - `packages/config`: Shared configuration schemas or constants.

---

## 3. Core Principles & Design Patterns

- **Component-driven UI** with shadcn/ui, promoting reusability and consistent design.
- **Type-safe communication**: Leverage TypeScript across the stack. Define clear interfaces/DTOs for all API (REST & WebSocket) request/response payloads and event data. Use Drizzle for type-safe database interactions.
- **Event-centric architecture** for WebSocket interactions: Frontend and backend communicate primarily through well-defined WebSocket events. Backend services may use an internal event bus (e.g., NestJS EventEmitter or RabbitMQ) for decoupling.
- **Separation of concerns (SoC)**:
  - **NestJS**: Controllers/Gateways (API layer), Services (business logic), Repositories/Drizzle Schemas (data access layer).
  - **Next.js**: UI Components (presentation), Hooks/Context/Zustand Stores (state and logic), API route handlers/server components (server-side logic within Next.js).
- **State management**:
  - **Next.js**: Zustand for global/complex cross-component state. React Context for simpler, localized state. Server Components for data fetching where appropriate.
  - **NestJS**: Primarily stateless services. Session-like data (e.g., operator associated with WebSocket connection) managed by the WebSocket gateway or a dedicated session service (potentially Redis-backed).
- **Modular Monorepo Structure**: Utilize Turbo or Nx for managing the monorepo, with clear separation between `apps/` (main-api, mini-app, dashboard) and `packages/` (db, eslint-config, typescript-config, shared-types, utils).

---

## 4. API & Event Contract Summary

(Refer to `docs/openapi.yaml` for HTTP and `docs/websocket-protocol.md` for WebSocket details)

- **REST API endpoints**:
  - `/api/getOperators` (GET): Fetches operator list for login.
  - `/api/getContactName` (GET): For external PBX caller ID.
  - `/api/vehicleAtDestination` (GET): External notification for vehicle arrival.
  - `/api/lineStatusUpd` (GET): External PBX line status updates.
  - `/api/updateTranslationDictionary` (GET): For reporting missing translations.
  - **Standards**: JSON request/response bodies, standard HTTP status codes.
  - **Auth**: Most external-facing endpoints are currently unauthenticated (relying on network security). Internal/admin APIs will use JWT.
- **WebSocket events**:
  - **Client → Server**: `call:add`, `call:update`, `call:remove`, `call:cancel`, `call:edit` (lock), `call:unlocked`, `call:new` (dial), `call:answer`, `call:list` (search), `schedule:add`, `schedule:update`, `schedule:remove`, `schedule:cancel`, `contact:info`, `contact:update`, `message:new` (send SMS), `stats:daily`, `stats:detailed`, `ping`, `client:settings`, `client:error`.
  - **Server → Clients**: `client:init`, `call:init`, `call:add`, `call:update`, `call:remove`, `call:locked`, `call:unlocked`, `call:edit` (ack), `call:metadata`, `call:list` (search results), `schedule:init`, `schedule:active`, `schedule:add`, `schedule:update`, `schedule:remove`, `contact:info`, `contact:update`, `message:new` (incoming/system), `message:sent`, `stats:daily`, `stats:detailed`, `ping`, `pong`, `client:reload`, `server:error`.
- **Data models**:
  - Core entities defined in `packages/db/src/schema/` using Drizzle ORM (e.g., `bots`, `tenants`, `users`, `roles`, `pbxCalls` (new name for old `calls`), `scheduledCalls` (new table or managed differently)).
  - DTOs (Data Transfer Objects) for NestJS controllers/gateways to validate request payloads.
  - TypeScript interfaces in `packages/types` for WebSocket event payloads and shared data structures.

---

## 5. Data Flow & Command Logic (based on old backend logic)

- **Feature-specific workflows**:
  - **Scheduled Calls**:
        1. **Creation (`schedule:add`)**:
            - Client sends schedule details.
            - NestJS `SchedulingService` validates.
            - If based on an existing call, updates it (e.g., `arrivaltime = -1`). If new, creates a placeholder `pbxCalls` record.
            - Stores schedule details in Redis (e.g., `schedule:<id>`).
            - Sets an in-memory timer (or uses a persistent job queue like BullMQ with RabbitMQ/Redis) for `timestamp - arrivaltime`.
            - Broadcasts `schedule:add`.
        2. **Update (`schedule:update`)**:
            - Client sends updated schedule. Requires call lock.
            - `SchedulingService` updates Redis entry and re-calculates/resets timer.
            - Broadcasts `schedule:update`. Unlocks call.
        3. **Removal (`schedule:remove`)**:
            - Client requests removal.
            - `SchedulingService` removes from Redis, clears timer. Updates base `pbxCalls` record (e.g., `arrivaltime = null`).
            - Broadcasts `schedule:remove`.
        4. **Activation (Timer expiry or `schedule:cancel`)**:
            - Timer fires or `schedule:cancel` received.
            - `SchedulingService` removes schedule from Redis.
            - Creates a new active call in `pbxCalls` table (source 'schedule', current timestamp, etc.) via `CallManagementService`.
            - `CallManagementService` broadcasts `call:add`.
  - **Live Calls**:
        1. **Incoming Call (PBX Event)**:
            - PBX sends event to `/api/lineStatusUpd` or TCP listener (legacy, to be replaced by direct NestJS integration).
            - `PbxIntegrationService` processes:
                - If `event=incoming` or `establishing`: Creates/updates `unansweredCalls` map (in-memory or Redis). Fetches contact details via `ContactService`. Broadcasts `call:add` (unanswered state).
        2. **Operator Answers (PBX Event or `call:answer`)**:
            - PBX reports line `established` or client sends `call:answer`.
            - `PbxIntegrationService` or `CallManagementService` updates call in `pbxCalls` table: sets `timeanswered`, assigns `operator_id`.
            - Removes from `unansweredCalls`. Updates `liveCalls` map.
            - Broadcasts `call:update`. If `call:answer`, may trigger `call:edit` for the answering operator.
        3. **Locking (`call:edit`)**:
            - Client requests lock.
            - `CallManagementService` checks `lockedCalls` map. If available, locks, broadcasts `call:locked` (others) and `call:edit` (self).
        4. **Updating (`call:update`)**:
            - Client sends updates (location, vehicles, etc.). Requires lock.
            - `CallManagementService` validates. Updates `pbxCalls` table.
            - If vehicles assigned/changed: interacts with `OrderService` (new concept for `orders.js` logic) to update Redis stats, `assignedCalls` map, potentially send SMS via `MessagingService`.
            - Broadcasts `call:update`. Unlocks call.
        5. **Call Termination (PBX Event)**:
            - PBX reports line `terminated`.
            - `PbxIntegrationService` updates call metadata (duration) in `pbxCalls`. Removes from `liveCalls`.
            - Broadcasts `call:metadata`.
  - **Messaging Flow (SMS/Notifications)**:
        1. **Send SMS (`message:new` type 'sms')**:
            - Client sends SMS details.
            - `MessagingService` calls `PbxIntegrationService.sendSms(phone, text)`.
            - `PbxIntegrationService` interacts with Asterisk/SMS Gateway (HTTP call).
            - On success: `MessagingService` sends `message:sent` (self) and system `message:new` (others).
            - On failure: `MessagingService` sends `server:error` (self).
        2. **Receive Incoming SMS/Notification (External -> Backend)**:
            - SMS Gateway/App pushes to a NestJS REST endpoint.
            - `MessagingService` or dedicated `NotificationController` processes.
            - Fetches contact info via `ContactService`.
            - Broadcasts `message:new` (type 'sms', 'texy', 'system') via WebSocket.
  - **Statistics Calculation Cycle**:
        1. **Request (`stats:daily` or `stats:detailed`)**:
            - Client requests stats for a date/range.
            - `StatisticsService` receives request.
        2. **Data Retrieval**:
            - Checks Redis cache first (e.g., `stats:YYYY:MM:DD`, `stats:YYYY:MM:DD:<status>:op`).
            - If not cached or for current day: Queries `pbxCalls` table (PostgreSQL) using Drizzle, applying filters for different statuses (answered, served, by operator, by shift based on `etc/stats.json` logic).
        3. **Caching**: Stores aggregated results in Redis for past days.
        4. **Response**: Sends aggregated data back to the client.
- **Real-time updates** via WebSocket event handling: Most state changes (call updates, new messages, lock status) are broadcast immediately to all relevant clients.

---

## 6. Security & Authentication

- **Session Management & Authentication**:
  - **Operator Login**:
        1. Client fetches operator list (`/api/getOperators`).
        2. User selects operator. Client initiates WebSocket connection with `operator_id` in URL.
        3. NestJS `AuthGuard` (custom for WebSockets) validates `operator_id`.
        4. If valid, a JWT token is generated by `AuthService` containing `operator_id`, roles, and expiry.
        5. This JWT is sent back to the client via an initial WebSocket message (e.g., within `client:init` or a dedicated `auth:success` event).
        6. Client stores JWT in `localStorage` or secure cookie.
  - **Subsequent WebSocket Communication**:
    - Client sends JWT with each WebSocket message (e.g., in a meta field or as part of initial handshake for subsequent connections if socket drops).
    - NestJS WebSocket Gateway uses a guard to validate JWT on each incoming message.
  - **HTTP API Authentication**:
    - Protected NestJS REST endpoints will require a `Bearer <JWT>` in the `Authorization` header.
    - NestJS `AuthGuard` (for HTTP) validates the JWT.
- **WebSocket Session Validation**:
  - JWT validation on connection and optionally on each message.
  - Server maintains a mapping of active WebSocket connections to authenticated operator IDs (potentially in Redis for scalability).
  - Handle token expiry and refresh mechanisms if long-lived sessions are needed.
- **Role/Permission Enforcement**:
  - JWT token will contain operator roles (e.g., 'admin', 'operator').
  - NestJS Guards (`RolesGuard`) will be used on controllers and gateway handlers to check for required roles.
  - Example: `@UseGuards(AuthGuard, RolesGuard) @Roles('admin') @SubscribeMessage('admin:action')`.
  - Business logic in services will also perform permission checks where necessary.

---

## 7. Technology & Libraries

- **Frontend (Next.js)**:
  - React (latest stable) for UI.
  - Next.js (`app` router or `pages` router).
  - State Management: Zustand.
  - UI Components: shadcn/ui.
  - Styling: Tailwind CSS (comes with shadcn/ui).
  - Forms: React Hook Form with Zod for validation.
  - i18n: `next-i18next` or `react-i18next`.
  - WebSocket Client: Native `WebSocket` API or a library like `socket.io-client` (if NestJS gateway uses Socket.IO adapter).
  - Data Fetching: SWR or React Query for REST APIs if any are consumed directly by frontend.
- **Backend (NestJS)**:
  - NestJS (latest stable).
  - WebSocket: `@nestjs/websockets` (using `ws` adapter by default, or `socket.io`).
  - Database ORM: Drizzle ORM with `pg` (node-postgres).
  - Caching: `@nestjs/cache-manager` with `cache-manager-redis-store`.
  - Message Queue: `@nestjs/microservices` with RabbitMQ transport, or a library like `nestjs-rascal`.
  - Authentication: `@nestjs/jwt`, `passport`, `passport-jwt`.
  - Validation: `class-validator`, `class-transformer`.
  - Configuration: `@nestjs/config`.
  - Logging: Pino (`nestjs-pino`).
- **Shared (`packages/`)**:
  - `@monorepo/types`: TypeScript interfaces, DTOs, enums.
  - `@monorepo/db`: Drizzle ORM schema, migrations, client instance.
  - `@monorepo/utils`: Common utility functions.
  - `@monorepo/config`: Shared configuration schemas.
- **External APIs/Integrations**:
  - Geocoding: HTTP client like `axios` or NestJS `HttpModule`.
  - PBX (Asterisk): Direct TCP/AMI integration (if replacing legacy listener) or via a new REST API on the PBX.
  - SMS Gateway: HTTP client.
- **Build/Tooling**:
  - Turbo (or Nx) for monorepo management.
  - Biome for linting/formatting.
  - Bun as package manager and runtime.
  - Docker & Docker Compose for development and deployment environments.

---

## 8. Development & Deployment Guidelines

- **Monorepo Structure**:
  - `apps/main-api`: NestJS backend.
  - `apps/dashboard`: Next.js frontend (assuming this is the main operator UI).
  - `apps/mini-app`: Another potential frontend.
  - `packages/db`: Drizzle ORM schema, migrations, DB client.
  - `packages/eslint-config`: Shared ESLint configurations.
  - `packages/typescript-config`: Shared TypeScript configurations (`tsconfig.base.json`, etc.).
  - `packages/types`: Shared TypeScript interfaces and DTOs.
  - `packages/utils`: Shared utility functions.
- **Build Commands**:
  - Root `package.json`: `turbo run build`, `turbo run dev`, etc.
  - App-specific `package.json`: `nest build` (main-api), `next build` (dashboard).
- **Testing Procedures**:
  - Unit tests (Jest/Vitest) for services, utils, components.
  - Integration tests for NestJS modules, API endpoints.
  - E2E tests (Playwright/Cypress) for critical user flows.
- **CI/CD Pipeline Overview**:
    1. Code push to Git repository (e.g., GitHub, GitLab).
    2. Webhook triggers CI pipeline (e.g., GitHub Actions, Jenkins).
    3. Linting and formatting checks (Biome).
    4. Run unit and integration tests.
    5. Build Docker images for `main-api` and `dashboard`.
    6. Push images to a container registry (e.g., Docker Hub, ECR, GCR).
    7. Deploy to staging environment (manual or automatic).
    8. Run E2E tests on staging.
    9. Deploy to production environment (manual or automatic after approval).
    10. Database migrations run as part of deployment (e.g., using Drizzle Kit `migrate` command).
- **Environment Configurations**:
  - Use `.env` files for local development (`.env.local`, `.env.development`).
  - NestJS: `@nestjs/config` to load environment variables. Validate using a schema (e.g., with Zod or Joi).
  - Next.js: Environment variables prefixed with `NEXT_PUBLIC_` for browser access, others for server-side.
  - Docker Compose for local development environment setup (Postgres, Redis, RabbitMQ).
  - Production environments will use secrets management tools provided by the cloud platform or deployment orchestrator.

---
