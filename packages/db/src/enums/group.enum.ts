import { pgEnum } from "drizzle-orm/pg-core";

// 1. Define enum values as the single source of truth
const GROUP_VALUES = [
	"FRANCHISE",
	"AGGREGATOR",
	"BRAND",
	"CORPORATE",
] as const;

// 2. Create the Drizzle ORM pgEnum for database schema definition
export const groupTypeEnum = pgEnum("group_type", GROUP_VALUES);

// 3. Define TypeScript union type for strict type safety
export type GroupTypeType = (typeof GROUP_VALUES)[number];

// 4. Define TypeScript string enum for application logic
export enum Group {
	FRANCHISE = "FRANCHISE",
	AGGREGATOR = "AGGREGATOR",
	BRAND = "BRAND",
	CORPORATE = "CORPORATE",
}
