#!/bin/bash

# Clean the monorepo
bunx turbo run clean

# Clean all node_modules directories
find . -name "node_modules" -type d -prune -exec rm -rf '{}' + || echo "Failed to remove some node_modules directories."

# Clean all dist directories
find . -name "dist" -type d -prune -exec rm -rf '{}' + || echo "Failed to remove some dist directories."

# Remove specific lock files
LOCK_FILES=("bun.lock" "pnpm-lock.yaml" "package-lock.json" "yarn.lock")
for lock_file in "${LOCK_FILES[@]}"; do
    find . -name "$lock_file" -type f -delete || echo "Failed to delete $lock_file"
done

# Remove any build artifacts (optional)
find . -name "*.log" -type f -delete || echo "Failed to delete log files."
find . -name "*.tmp" -type f -delete || echo "Failed to delete temporary files."
find . -name "*.tsbuildinfo" -type f -delete || echo "Failed to delete temporary .tsbuildinfo files."

# Remove previous repomix output files if they exist
OUTPUT_FILES=("repomix-*" "repomix-db.txt")
for output_file in "${OUTPUT_FILES[@]}"; do
    rm -f "$output_file"
done

# Generate new repomix output files

repomix --include "apps/mini-app/**/*.{ts,tsx,js,jsx,json,css,scss,html,yaml,yml}" -o repomix-mini-app.xml
repomix --include "apps/dashboard/**/*.{ts,tsx,js,jsx,json,css,scss,html,yaml,yml}" -o repomix-dashboard.xml
repomix --include "packages/db/**/*.{ts,tsx,js,jsx,json,css,scss,html,yaml,yml},turbo.json,tsconfig.json,package.json" -o repomix-db.xml
repomix --include "./**/*.ts" -o repomix-all-ts.xml
repomix --include "./**/*.json" --ignore "packages/db/drizzle/meta/**/*.json,apps/dashboard/public/registry/*.json" -o repomix-all-json.xml

echo "Clean-up complete."


# Install dependencies using Bun package manager
bun install

# Wait for 5 seconds to ensure all installations are settled
sleep 5


# Commit changes with a message
git add bun.lock
git commit -m "Bun packages and update lock file"

echo "Clean-up script complete."