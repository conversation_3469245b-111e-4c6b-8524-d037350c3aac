import { pgEnum } from "drizzle-orm/pg-core";

// 1. Define enum values as the single source of truth
const REMINDER_URGENCY_VALUES = [
	"NOT_URGENT",
	"URGENT",
	"VERY_URGENT",
	"PAST_DUE",
] as const;

// 2. Create the Drizzle ORM pgEnum for database schema definition
export const reminderUrgencyEnum = pgEnum(
	"reminder_urgency",
	REMINDER_URGENCY_VALUES,
);

// 3. Define TypeScript union type for strict type safety
export type ReminderUrgencyType = (typeof REMINDER_URGENCY_VALUES)[number];

// 4. Define TypeScript string enum for application logic
export enum ReminderUrgency {
	NOT_URGENT = "NOT_URGENT",
	URGENT = "URGENT",
	VERY_URGENT = "VERY_URGENT",
	PAST_DUE = "PAST_DUE",
}
