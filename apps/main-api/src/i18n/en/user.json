{"service": {"user_created": "User created with email {{email}} and ID {{id}}", "user_exists": "User with email {{email}} already exists", "find_all": "Fetching all users", "user_found": "User found with ID {{id}}", "user_not_found": "User not found with ID {{id}}", "update_error": "Failed to update user with ID {{id}}", "user_updated": "User updated with ID {{id}}", "user_removed": "User removed with ID {{id}}", "remove_failed": "Failed to remove user with ID {{id}}"}, "validation": {"NOT_EMPTY": "Field cannot be empty", "INVALID_EMAIL": "Invalid email address", "MIN_LENGTH": "Field must be at least {{value}} characters long", "EMAIL_EXISTS": "Email address already registered"}}