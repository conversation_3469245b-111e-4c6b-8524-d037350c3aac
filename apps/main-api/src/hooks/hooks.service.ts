import type { AppBot } from "@/types/bot-context.js";
// src/hooks/hooks.service.ts
import { Injectable, Logger } from "@nestjs/common";

@Injectable()
export class HooksService {
	private readonly logger = new Logger(HooksService.name);

	// NOTE: This hook is currently redundant with BotProcessingService.derive
	// Comment out to prevent conflicts with the main derive function
	// registerI18nHook(botInstance: AppBot): AppBot {
	// 	return botInstance.derive(
	// 		["message", "callback_query", "inline_query", "chosen_inline_result"],
	// 		async (ctx: ContextBeforeAppDerive) => {
	// 			// Assuming ctx.bot is the current bot instance
	// 			const tenantBotApiClient = ctx.bot.api;
	// 			return {
	// 				t: i18n.buildT(ctx.from?.languageCode),
	// 				tenantBotApiClient: tenantBotApiClient,
	// 			};
	// 		},
	// 	) as AppBot;
	// }

	applyCoreHooks(botInstance: AppBot): AppBot {
		botInstance.onStart(({ info }) => {
			this.logger.log(`✨ Bot ${info.username} initialized with hooks`);
		});
		return botInstance;
	}
}
