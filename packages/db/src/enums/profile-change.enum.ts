import { pgEnum } from "drizzle-orm/pg-core";

const PROFILE_CHANGE_VALUES = [
	"ONBOARDING",
	"PHONE_CHANGE",
	"ADMIN_UPDATE",
] as const;

export const profileChangeEnum = pgEnum(
	"profile_change_type",
	PROFILE_CHANGE_VALUES,
);

export type ProfileChangeType = (typeof PROFILE_CHANGE_VALUES)[number];

export enum ProfileChange {
	ONBOARDING = "ONBOARDING",
	PHONE_CHANGE = "PHONE_CHANGE",
	ADMIN_UPDATE = "ADMIN_UPDATE",
}
