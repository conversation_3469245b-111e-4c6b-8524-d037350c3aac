import { pgEnum } from "drizzle-orm/pg-core";

const USER_ROLE_VALUES = [
	"PASSENGER",
	"DRIVER",
	"OPERATOR",
	"MANAGER",
	"ADMIN",
	"SYSTEM_SUPPORT",
] as const;

export const userRoleEnum = pgEnum("user_role", USER_ROLE_VALUES);

export type UserRolesType = (typeof USER_ROLE_VALUES)[number];

export enum UserRole {
	PASSENGER = "PASSENGER",
	DRIVER = "DRIVER",
	OPERATOR = "OPERATOR",
	MANAGER = "MANAGER",
	ADMIN = "ADMIN",
	SYSTEM_SUPPORT = "SYSTEM_SUPPORT",
}
