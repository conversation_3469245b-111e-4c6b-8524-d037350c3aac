Okay, excellent! Now that we have a solid "Base for Reimplementation" blueprint and an understanding of the new monorepo structure (even if it's just JSON config files for now), we can bridge the gap between the *old system's features* and the *new system's implementation plan*.

I will analyze the provided JSON files (which represent the new monorepo's configuration and structure) and combine that with our previous analysis of the old system. Then, I'll outline:

1. **Database Schema - Gaps and New Entities:** What's defined in `packages/db/src/schema/` and what might be missing or needs refinement based on the old system's features.
2. **Frontend Pages/Views (Next.js `apps/dashboard`):** What pages/major UI sections are implied by the old system's features and how they might map to Next.js.
3. **Backend API/Service Layer (NestJS `apps/main-api`):** What NestJS modules, services, controllers, and gateways are needed, and whether they'd primarily use REST, WebSocket, or interact with RabbitMQ.

Let's break down the new monorepo structure from your JSON files first:

**Monorepo Structure Insights from JSON:**

* **`apps/main-api` (NestJS Backend):**
  * Uses NestJS (`nest-cli.json`, `package.json` dependencies like `@nestjs/core`).
  * Has Biome for formatting/linting.
  * Includes Drizzle ORM (`drizzle-orm` in dependencies).
  * Integrates with RabbitMQ (`@golevelup/nestjs-rabbitmq`, `rascal`).
  * Integrates with Redis (`@liaoliaots/nestjs-redis`, `ioredis`).
  * Uses GramIO for Telegram bot interactions (`gramio` and related packages). This seems to be a significant part of `main-api`.
  * Has modules for `config`, `database`, `gramiobot`, `health`, `rabbitmq`, `redis`, `scenes`, `services` (locks, tenant management).
  * `tsconfig.json` references `packages/db`.
* **`apps/mini-app` (SolidJS Frontend):**
  * A SolidJS application (vite, `solid-js`, `@solidjs/router`).
  * Uses `@telegram-apps/sdk-solid` and `@tonconnect/ui`, suggesting it's a Telegram Mini App, possibly with TON blockchain integration.
  * This is likely separate from the main operator dashboard.
* **`packages/db` (Database Layer):**
  * Manages Drizzle ORM schema (`src/schema/`), migrations (`scripts/migrate.ts`), and potentially seeding.
  * Contains a very extensive list of schema files, indicating a rich data model is already being defined (e.g., `area.schema.ts`, `bots.schema.ts`, `pbx-call.schema.ts` (likely the new `calls`), `operators.schema.ts`, `tenants.schema.ts`, etc.).
  * Includes enums for various statuses and types.
* **`packages/eslint-config`, `packages/typescript-config`:** Shared linting and TypeScript configurations.
* **Root `package.json`:** Manages workspaces, uses Turbo for monorepo tasks, Bun as package manager.

**Connecting Old Features to New Structure & Identifying Gaps/Plans:**

Based on this, let's outline the plan:

---

## Implementation Plan: Bridging Old Features to New Architecture

This plan details how features from the old system will be implemented in the new Next.js (dashboard) + NestJS (main-api) monorepo, identifying necessary database entities, frontend pages, and backend communication patterns.

### I. Database Schema (`packages/db/src/schema/`)

**Existing Relevant Schemas (from your new `packages/db` structure):**

* **`tenants.schema.ts`**: Foundation for multi-tenancy (if planned, or can be a single default tenant initially).
* **`users.schema.ts`**: General user model. Will need to differentiate or link to operators, drivers, passengers.
* **`operators.schema.ts`**: Specific details for operators (extends `userTenants`). This maps well to the old `operators` table.
  * **Action:** Ensure fields like `disabled` status are present.
* **`pbx-call.schema.ts`**: This is likely the **new `calls` table**.
  * **Action:** Cross-reference with the old `CALLS` data model from `docs/data-models.md`. Ensure all necessary fields are present: `id`, `timestamp`, `timeanswered`, `timeassigned`, `phone`, `location`, `destination`, `area`, `arrivaltime`, `source`, `extension`, `operator_ids` (might be a relation to `operators` or `userTenants`), `comment`, `gisdata` (JSONB), `vehicles` (TEXT[]), `metadata` (JSONB).
* **`scheduled_calls` (Needs a schema if not already present, or managed differently):** The old system used Redis. The new system could:
    1. **Use a dedicated `scheduled_calls.schema.ts` in PostgreSQL:** For persistence, with fields like `id`, `base_call_id` (FK to `pbx-call`), `scheduled_timestamp`, `arrivaltime_offset`, `repeat_pattern` (JSONB for `["HH:MM" or 0, ...]`), `status` ('pending', 'active', 'processed').
    2. **Manage in Redis with more structure:** Similar to the old way but with well-defined keys and potentially using Redis streams or sorted sets for activation.
  * **Decision Point:** PostgreSQL offers better querying and relational integrity. Redis is good for fast access and timers. A hybrid approach might be best (PostgreSQL for master record, Redis for active timers/queue).
  * **Action:** Define `scheduled_calls.schema.ts` or document Redis strategy.
* **`contacts` (Needs a schema or clear Redis strategy):** The old system relied heavily on Redis for contact caching (`contacts:<phone>` HASH, sorted sets for locations/destinations).
  * **Option 1 (DB-centric):** Create `contacts.schema.ts` (`phone` PK, `name`, `first_contact_ts`, `last_action_ts`, `preferred_lang`, `default_destination`) and `contact_history.schema.ts` (`contact_phone` FK, `type` ('location', 'destination'), `value`, `count`, `is_booked`). Redis would be a cache.
  * **Option 2 (Redis-centric, similar to old):** Formalize the Redis structure in documentation.
  * **Action:** Decide and define. The current `packages/db` doesn't show an explicit `contacts` table.
* **`vehicle.schema.ts`**: Defines vehicles.
  * **Action:** Ensure it can store `vehicle_number` (as used in `pbx-call.vehicles`) and any other relevant details (type, capacity).
* **`area.schema.ts`**: Defines geographical areas.
  * **Action:** Ensure it links to `tenants` and can be referenced by `pbx-call.area`.
* **`role.schema.ts` & `user-bot-roles.schema.ts` / `user-tenant.schema.ts`**: Foundation for RBAC.
  * **Action:** Define roles like 'OPERATOR', 'ADMIN'. `user-tenant` links users to tenants with a specific role.
* **`i18n_translation.schema.ts`**: For storing translations if a DB-backed i18n system is chosen over file-based.
* **`audit-log.schema.ts`**: Good for tracking changes.

**Missing/Needs Clarification (based on old system):**

* **`Call Statistics Aggregation Tables`**: While stats can be calculated on-the-fly or cached in Redis (as the old system did), for more complex reporting or historical analysis, pre-aggregated tables in PostgreSQL might be beneficial (e.g., `daily_operator_stats`, `daily_overall_stats`).
  * **Action:** Decide if on-the-fly + Redis caching is sufficient or if aggregated tables are needed. The current `stats.js` logic from the old backend suggests a Redis-first approach for caching daily/detailed stats.
* **`SMS Log/Message Table`**: The old system didn't store SMS content persistently. The new `message.schema.ts` and `chatbot-messages.schema.ts` seem geared towards bot interactions.
  * **Action:** Decide if operator-sent SMS (via PBX gateway) needs to be logged in the database. If so, `message.schema.ts` could be adapted or a new `sms_log.schema.ts` created.

### II. Frontend Pages/Views (Next.js `apps/dashboard`)

The operator dashboard will be the primary interface, replacing the old Angular app.

| Old Feature/View             | New Next.js Page/Component(s) (`apps/dashboard`)                                  | Core Data Entities Involved                                     | Primary Backend Interaction |
| :--------------------------- | :-------------------------------------------------------------------------------- | :-------------------------------------------------------------- | :-------------------------- |
| **Operator Login**           | `/login` (Page)                                                                   | `Operators`                                                     | REST (`/api/getOperators`), WebSocket (connect) |
| **Main Call List View**      | `/` or `/calls` (Page)                                                            | `PbxCall` (new `calls`), `Contacts`                             | WebSocket (`call:init`, `call:add`, `call:update`, `call:remove`, `call:locked`, `call:unlocked`, `call:metadata`) |
|                              |   - Call Row Component                                                            | `PbxCall`                                                       | (Display only, actions trigger WS events) |
|                              |   - Call Filters Component                                                        | (Client-side filtering or WS `call:list` with filters)          | WebSocket (if server-side)  |
|                              |   - Add New Call Modal                                                            | `PbxCall`                                                       | WebSocket (`call:add`)      |
|                              |   - Edit Call Form (inline or modal)                                              | `PbxCall`, `Vehicles`, `Areas`                                  | WebSocket (`call:update`)   |
| **Scheduled Calls View**     | `/scheduled` (Page)                                                               | `ScheduledCall` (new entity/Redis), `PbxCall` (base)            | WebSocket (`schedule:init`, `schedule:add`, `schedule:update`, `schedule:remove`) |
|                              |   - Scheduled Call Row Component                                                  | `ScheduledCall`                                                 | (Display only)              |
|                              |   - Schedule New/Edit Modal                                                       | `ScheduledCall`, `PbxCall`                                      | WebSocket (`schedule:add`, `schedule:update`) |
| **Search Calls / Results**   | `/search` (Modal or Page for form), `/search/results` (Page)                      | `PbxCall`, `Contacts`                                           | WebSocket (`call:list`)     |
| **Daily Statistics View**    | `/statistics/daily` (Page)                                                        | Aggregated Stats (from `PbxCall`)                               | WebSocket (`stats:daily`)   |
|                              |   - Charts & Tables for stats                                                     |                                                                 |                             |
|                              |   - Operator breakdown drilldown                                                  | Aggregated Stats (per operator)                                 | WebSocket (`stats:detailed`) |
| **Mapping Modal**            | Modal component (invoked from Call Row/Edit)                                      | `PbxCall` (for existing GIS data), External Geocoding           | REST (to external geocoding APIs via Next.js API route or direct client call), WebSocket (`call:update` with new GIS data) |
| **Send SMS Modal**           | Modal component (invoked from Call Row or Notifications)                          | `PbxCall` (for context), `Contacts` (for phone)                 | WebSocket (`message:new` type 'sms') |
| **Notifications Area**       | Global Toast/Notification Component (e.g., using `sonner` or `react-toastify`)    | `Message` (from WS), `PbxCall` (for context in Texy notifs)     | WebSocket (receives `message:new`, `server:error`) |
| **Header/Footer/Layout**     | Layout component (`_app.tsx` or `layout.tsx`)                                     | `Operator` (for display name)                                   | (Display only)              |
| **Language Switcher**        | Component in Header/User Menu                                                     | (Client-side state, `next-i18next`)                             | WebSocket (`client:settings` to inform backend) |
| **Operator Shift Stats**     | Component within Header or dedicated stats view                                   | Aggregated Stats (from `PbxCall`)                               | (Display only, data from `client:init` or specific WS event) |

**Actions for Frontend:**

* Create page structure in `apps/dashboard/app/` (or `pages/`).
* Develop reusable UI components (CallRow, Modals, Forms) using shadcn/ui.
* Implement Zustand stores for global state (e.g., operator info, WebSocket connection status) and feature states (e.g., call list, selected call for editing).
* Develop a robust WebSocket service to handle event subscriptions and emissions.
* Integrate `next-i18next` for localization.

### III. Backend API/Service Layer (NestJS `apps/main-api`)

| Feature Domain        | NestJS Module(s)                                     | Key Services                                                                | Controllers/Gateways                                     | Primary Communication Pattern(s) | Key Data Entities (DB/Redis) | Async Tasks (RabbitMQ) - Conceptual |
| :-------------------- | :--------------------------------------------------- | :-------------------------------------------------------------------------- | :------------------------------------------------------- | :------------------------------- | :--------------------------- | :---------------------------------- |
| **Authentication**    | `AuthModule`, `UsersModule`                          | `AuthService`, `UsersService`, `OperatorsService`                           | `AuthController` (REST for `/api/getOperators`), `AuthGateway` (WS for connection auth) | REST, WebSocket                  | `Operators`, `Users`, `Roles`, `UserTenant` | -                                   |
| **Call Management**   | `CallsModule` (`PbxCallsModule`), `ContactsModule`, `OrdersModule` (new) | `PbxCallsService`, `ContactsService`, `OrderService` (handles vehicle assignment logic, SMS for orders), `LockingService` | `CallsGateway` (WebSocket), `PbxEventsController` (REST for PBX events) | WebSocket, REST (from PBX)       | `PbxCall`, `Contacts` (Redis/DB), `Vehicles`, `Areas`, In-memory `lockedCalls`, `assignedCalls` (Redis/memory) | SMS sending for order status |
| **Scheduling**        | `SchedulingModule`                                   | `SchedulingService`                                                         | `SchedulingGateway` (WebSocket)                          | WebSocket                        | `ScheduledCall` (DB/Redis), `PbxCall` | Schedule activation (timer to job) |
| **Messaging**         | `MessagingModule`, `NotificationsModule`             | `MessagingService` (SMS sending), `NotificationService` (WS push)         | `MessagingGateway` (WebSocket for sending), `SmsWebhookController` (REST for incoming SMS) | WebSocket, REST (incoming SMS)   | `Message` (if logging SMS), `Contacts` | SMS sending (if offloaded)      |
| **Statistics**        | `StatisticsModule`                                   | `StatisticsService`                                                         | `StatisticsGateway` (WebSocket)                          | WebSocket                        | `PbxCall`, Stats Cache (Redis) | Daily stats aggregation (batch job) |
| **Mapping**           | `MappingModule` (optional, for config or proxy)      | `MapConfigService` (provides URLs)                                          | `GeocodingProxyController` (REST, optional if client calls directly) | REST (if proxied)                | -                            | -                                   |
| **Contacts**          | `ContactsModule`                                     | `ContactsService`                                                           | `ContactsGateway` (WebSocket)                            | WebSocket                        | `Contacts` (Redis/DB)        | Contact data sync (if from CRM) |
| **System/Core**       | `WebSocketModule`, `ConfigModule`, `DatabaseModule`, `RedisModule`, `RabbitMQModule` | `WebSocketManagerService`, `AppConfigService`, `DatabaseService`, `RedisService`, `RascalService` | `RootGateway` (for `client:init`, `ping`) | WebSocket                        | -                            | -                                   |
| **Telegram Bot**      | `GramioBotModule` (from new structure)               | `BotProcessingService`, `TenantBotFactoryService`, various command handlers | `BotController` (REST webhook)                           | REST (Telegram webhook)          | `Bots`, `Tenants`, `Roles`, `UserBotRoles`, `TelegramUsers`, `TelegramChats`, `TelegramMessages` | Bot message processing (if heavy) |

**Actions for Backend:**

* **Define DTOs:** For all WebSocket event payloads and REST request/response bodies.
* **Implement NestJS Modules, Services, Gateways, Controllers.**
* **WebSocket Gateway (`RootGateway` or feature-specific gateways):**
  * Handle `client:init` to send initial config.
  * Implement handlers for all client-to-server events (`@SubscribeMessage()`).
  * Broadcast server-to-client events.
  * Manage WebSocket connections and operator sessions (link connection to authenticated operator).
* **Services:** Encapsulate all business logic. Interact with Drizzle for DB operations, Redis for caching/state, and RabbitMQ for async tasks.
* **REST Controllers:**
  * `AuthController`: `/api/getOperators`.
  * `PbxEventsController`: New endpoints to replace legacy TCP listener for events like `/api/lineStatusUpd`, `/api/incomingSms`, `/api/vehicleAtDestination`.
  * `GeocodingProxyController` (optional): If you decide to proxy external geocoding calls through the backend.
* **Drizzle ORM Integration:** Use `packages/db` for all database interactions.
* **RabbitMQ Integration (`RascalService`):**
  * Define exchanges, queues, and bindings in `rascal.config.ts`.
  * Publish messages from services (e.g., `OrderService` publishes `order.vehicle.assigned` event).
  * Create consumers/handlers for async tasks (e.g., a `NotificationConsumer` that listens for events and sends SMS/Push).
* **Error Handling:** Implement global exception filters for both HTTP and WebSocket errors.
* **Logging:** Integrate `nestjs-pino` for structured logging.

### IV. Key Decisions & Further Elaboration Needed

1. **Scheduled Calls Storage:** PostgreSQL table (`scheduled_calls`) vs. primarily Redis.
    * *Recommendation:* Store master record in PostgreSQL for durability and querying. Use Redis or a proper job queue (BullMQ backed by Redis, integrated with RabbitMQ for signaling) for managing activation timers.
2. **Contact Data Storage:** DB-centric with Redis cache vs. Redis-primary.
    * *Recommendation:* Store primary contact data (phone, name) in a PostgreSQL `contacts` table. Use Redis for caching frequently accessed data like last locations/destinations (sorted sets) and overall stats (hash). This makes data more durable and queryable.
3. **SMS Logging:** Should operator-sent SMS be logged persistently?
    * *Recommendation:* Yes, create an `sms_logs` table for audit and history.
4. **Real-time Call State (beyond DB):**
    * `lockedCalls`: In-memory Map in `CallManagementService` (scoped to a single NestJS instance) or Redis (if multiple backend instances).
    * `unansweredCalls`, `liveCalls`, `assignedCalls`: Can be managed in Redis for persistence across restarts/scales, or derived from DB + recent events if performance allows. The old system used in-memory maps. Redis is generally better for a scalable backend.
5. **Telegram Bot Integration (`GramIO`):** The `main-api` seems heavily geared towards being a Telegram bot backend. Clarify how this integrates with the operator dashboard. Is the dashboard also for managing bot tenants/interactions, or is it purely for the call center operations? The current database schema suggests a multi-tenant bot platform.
6. **Authentication for External PBX/SMS Gateway Calls to Backend:** The old system had no auth. The new one should implement API key/secret or IP whitelisting for these inbound REST calls.

This detailed plan should provide a strong foundation for your development sprints. You can break this down further into specific tasks for your team.
