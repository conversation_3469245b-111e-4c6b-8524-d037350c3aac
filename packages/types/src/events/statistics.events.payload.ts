// packages/types/src/events/statistics.events.payload.ts
import type { BaseEventPayload } from "./base-event.payload.js";

export interface DateRange {
	from: string; // YYYY-MM-DD
	to: string; // YYYY-MM-DD
}

export interface DailyStatsRequestedPayload extends BaseEventPayload {
	data: DateRange;
}

export interface DailyStatEntry {
	answered: number;
	unanswered: number;
	served: number;
	unserved: number;
	canceled: number;
	manual: number;
	servedByVehicle?: Record<string, number>; // vehicleId -> count
}

export interface DailyStatsProvidedPayload extends BaseEventPayload {
	data: Record<string, DailyStatEntry>; // Key is YYYY-MM-DD
}

export interface DetailedStatsRequestedPayload extends BaseEventPayload {
	data: {
		date: string; // YYYY-MM-DD
	};
}

export interface OperatorDailyStats {
	answered: number;
	// ... other per-operator stats
}

export interface DetailedStatsProvidedPayload extends BaseEventPayload {
	data: Record<string, Record<string, OperatorDailyStats>>; // Date -> OperatorID -> Stats
}