// apps/main-api/src/pbx/pbx.module.ts
import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { ConfigModule as NestConfigModule } from '@nestjs/config';
import { PbxConfigService } from './services/pbx-config.service';
import { AriConnectorService } from './services/ari-connector.service'; // This now handles ARI connections
import { FreePbxGraphqlService } from './services/freepbx-graphql.service';
import { PbxEventRouterService } from './services/pbx-event-router.service';
import { PbxWebhookController } from './controllers/pbx-webhook.controller';
import { PbxRawEventHandler } from './handlers/pbx-raw-event.handler';
import { DatabaseModule } from '@/services/database/database.module';
import { RascalModule } from '@/rabbitmq/rascal.module';
// AriWebSocketManagerService is no longer needed if AriConnectorService manages ManagedAriConnection instances

@Module({
  imports: [
    HttpModule,
    NestConfigModule,
    DatabaseModule,
    RascalModule,
  ],
  controllers: [PbxWebhookController],
  providers: [
    PbxConfigService,
    AriConnectorService, // This service now manages multiple ManagedAriConnection instances
    FreePbxGraphqlService,
    PbxEventRouterService,
    PbxRawEventHandler,
  ],
  exports: [
    AriConnectorService, // For sending commands
    FreePbxGraphqlService, // For querying GraphQL
    PbxConfigService,
  ],
})
export class PbxModule {}