import { Injectable, Logger, type OnModuleInit } from "@nestjs/common";
import type { DiscoveryService, Reflector } from "@nestjs/core";
import type { AckOrNack } from "rascal";
import {
	RABBIT_HANDLER,
	type RabbitHandlerConfig,
} from "./handler.decorator.js";
import type { RascalService } from "./rascal.service.js";

@Injectable()
export class RabbitMQLoader implements OnModuleInit {
	private readonly logger = new Logger(RabbitMQLoader.name);

	constructor(
		private readonly discoveryService: DiscoveryService,
		private readonly reflector: Reflector,
		private readonly rascalService: RascalService, // Corrected: RascalService was already here
	) {}

	async onModuleInit() {
		const providers = this.discoveryService.getProviders();
		let handlerCount = 0;

		for (const provider of providers) {
			const instance = provider.instance;
			if (!instance || typeof instance !== "object") continue;

			for (const methodName of Object.getOwnPropertyNames(
				Object.getPrototypeOf(instance),
			)) {
				const method = instance[methodName];
				if (typeof method !== "function" || methodName === "constructor")
					continue;

				const config = this.reflector.get<RabbitHandlerConfig>(
					RABBIT_HANDLER,
					method,
				);
				if (!config) continue;

				this.logger.log(
					`Registering RabbitMQ handler ${provider.name}.${methodName} for subscription: ${config.subscription}`,
				);

				try {
					await this.rascalService.subscribe(
						config.subscription,
						{
							message: async (
								message: any, // Use any for Message type from amqplib
								content: any,
								ackOrNack: AckOrNack,
							) => {
								try {
									await method.call(instance, message, content, ackOrNack);
								} catch (error: unknown) {
									const errorMessage =
										error instanceof Error ? error.stack : String(error);
									this.logger.error(
										`Error in handler ${provider.name}.${methodName} for subscription ${config.subscription}`,
										errorMessage,
									);
									const errorToNack =
										error instanceof Error ? error : new Error(String(error));
									ackOrNack(errorToNack, [{ strategy: "nack", requeue: true }]);
								}
							},
							error: (error: Error) => {
								if (error instanceof Error) {
									this.logger.error(
										`Subscription error for ${config.subscription} in ${provider.name}.${methodName}`,
										error.stack,
									);
								} else {
									this.logger.error(
										`Subscription error for ${config.subscription} in ${provider.name}.${methodName}`,
										error,
									);
								}
							},
							cancelled: (err: Error | undefined) => {
								if (err) {
									this.logger.warn(
										`Subscription ${config.subscription} in ${provider.name}.${methodName} was cancelled: ${err.message}`,
										err.stack,
									);
								} else {
									this.logger.warn(
										`Subscription ${config.subscription} in ${provider.name}.${methodName} was cancelled`,
									);
								}
							},
						},
						config.options,
					);
					this.logger.log(
						`Successfully setup subscription ${config.subscription} for ${provider.name}.${methodName}`,
					);
					handlerCount++;
				} catch (error: unknown) {
					const errorMessage =
						error instanceof Error ? error.stack : String(error);
					this.logger.error(
						`Failed to register handler ${provider.name}.${methodName} for ${config.subscription}`,
						errorMessage,
					);
				}
			}
		}
		this.logger.log(`Registered ${handlerCount} RabbitMQ handlers`);
	}
}
