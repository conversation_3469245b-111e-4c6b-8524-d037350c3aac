// packages/types/src/pbx/config.types.ts

/**
 * Details required to connect to an Asterisk ARI (Asterisk REST Interface).
 */
export interface AriConnectionDetails {
  /** Discriminator to identify the connection type. */
  type: 'ASTERISK_ARI';
  /** Base URL for the ARI HTTP REST API (e.g., "http://localhost:8088" or "https://pbx.example.com:8089").
   *  The WebSocket URL for events will be derived from this (e.g., "ws://localhost:8088/ari/events").
   */
  url: string;
  /** Username for ARI authentication. */
  username: string; // Made mandatory as ARI usually requires it
  /** Password for ARI authentication. */
  password: string; // Made mandatory
  /** The Stasis application name registered in Asterisk that this client will connect to for events. */
  appName: string;
}

/**
 * Details required to connect to a FreePBX GraphQL API.
 */
export interface FreePbxGraphqlConnectionDetails {
  /** Discriminator to identify the connection type. */
  type: 'FREEPBX_GRAPHQL';
  /** Full URL to the FreePBX GraphQL endpoint (e.g., "https://freepbx.example.com/graphql"). */
  apiUrl: string;

  // Authentication Method 1: OAuth2 Client Credentials (Common for modern APIs)
  /** Optional: Client ID for OAuth2 client credentials grant. */
  clientId?: string;
  /** Optional: Client Secret for OAuth2 client credentials grant. */
  clientSecret?: string;
  /** Optional: URL to fetch the OAuth2 access token. Required if using clientId/clientSecret. */
  tokenUrl?: string;

  // Authentication Method 2: Static API Key (Simpler, if supported)
  /** Optional: A static API key if the FreePBX GraphQL API uses API key authentication. */
  apiKey?: string;
  /** Optional: The name of the header to send the apiKey in (e.g., 'X-API-KEY', 'Authorization').
   *  If 'Authorization', apiKey should often be prefixed with 'Bearer '.
   */
  apiKeyHeaderName?: string;
  apiKeyPrefix?: string; // e.g., "Bearer " if apiKey goes into Authorization header

  // Authentication Method 3: Basic Auth (Less common for GraphQL but possible)
  basicAuthUsername?: string;
  basicAuthPassword?: string;
}

/**
 * Union type representing connection details for any supported PBX type.
 * This will be the type for the `connectionDetails` field in the `pbx_instances` table.
 */
export type PbxConnectionDetails = AriConnectionDetails | FreePbxGraphqlConnectionDetails;


/**
 * Defines the types of criteria a routing rule can use to identify a tenant
 * from an incoming PBX event.
 */
export type TenantRoutingRuleType =
  | 'DID'                   // Match by Dialed Number Identification Service (DNIS/DID)
  | 'CHANNEL_VAR_MATCH'     // Match by an Asterisk channel variable's name and/or value
  | 'QUEUE_NAME'            // Match by the name of the Asterisk queue the call entered
  | 'EXTENSION_PREFIX'      // Match by a prefix of the dialed extension
  | 'CHANNEL_NAME_PREFIX'   // Match by a prefix of the Asterisk channel name (e.g., "PJSIP/tenantA-")
  | 'CALLER_ID_PREFIX';     // Match by a prefix of the inbound Caller ID Number

/**
 * Represents a single rule for routing calls from a multi-tenant PBX
 * to a specific tenant within your system.
 */
export interface TenantRoutingRule {
  type: TenantRoutingRuleType;
  tenantId: string; // UUID of the tenant (from your `tenants` table)
  pattern?: string;  // For DID, QUEUE_NAME, EXTENSION_PREFIX, CHANNEL_NAME_PREFIX, CALLER_ID_PREFIX
  variableName?: string; // For CHANNEL_VAR_MATCH
  variableValue?: string; // Optional: For CHANNEL_VAR_MATCH, to match a specific value
  priority?: number; // Lower numbers processed first, defaults to 0
}

/**
 * Interface representing the fully typed configuration for a PBX instance,
 * combining database fields with typed JSONB content.
 * This is what PbxConfigService would provide.
 */
export interface PbxInstanceConfigData {
  id: string; // UUID from pbx_instances table
  name: string;
  type: 'ASTERISK_ARI' | 'FREEPBX_GRAPHQL'; // Matches pbxTypeEnum
  isEnabled: boolean;
  connectionDetails: PbxConnectionDetails; // Typed JSONB content
  tenantAssociationType: 'SINGLE_TENANT' | 'MULTI_TENANT_RULE_BASED'; // Matches pbxTenantAssociationTypeEnum
  defaultTenantId?: string | null;
  tenantRoutingRules?: TenantRoutingRule[] | null; // Typed JSONB content
  // Add other fields from your pbx_instances Drizzle schema if needed by services
  createdAt: Date;
  updatedAt: Date;
}

// --- Parameters for ARI Commands (examples) ---
export interface AriOriginateParams {
  endpoint: string;
  extension?: string;
  context?: string;
  priority?: number;
  label?: string;
  app?: string;
  appArgs?: string;
  callerId?: string;
  timeout?: number;
  variables?: Record<string, string>;
  channelId?: string;        // Optional: Client-provided ID for the new channel
  otherChannelId?: string;   // Optional: For originating a second channel and bridging
  originator?: string;       // Optional: Channel ID of the channel that is originating this call
  formats?: string;          // Optional: Comma-separated list of allowed formats
}