import { relations } from "drizzle-orm";
import {
	date,
	index,
	integer,
	numeric,
	pgTable,
	text,
	timestamp,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { tenants } from "./tenants.schema.js";
import { vehicles } from "./vehicle.schema.js";

export const vehicleRepairs = pgTable(
	"vehicle_repair",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id")
			.references(() => tenants.id, { onDelete: "cascade" })
			.notNull(),
		vehicleId: uuid("vehicle_id")
			.references(() => vehicles.id, { onDelete: "cascade" })
			.notNull(),
		date: date("date").notNull(),
		odometer: integer("odometer"),
		description: text("description").notNull(),
		cost: numeric("cost", { precision: 10, scale: 2 }),
		notes: text("notes"),
		repairShop: varchar("repair_shop", { length: 255 }), // Optional: Name of the repair shop
		createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
	},
	(table) => [
		index("vehicle_repair_tenant_id_idx").on(table.tenantId),
		index("vehicle_repair_vehicle_id_idx").on(table.vehicleId),
		index("vehicle_repair_date_idx").on(table.date),
	],
);

export const vehicleRepairsRelations = relations(vehicleRepairs, ({ one }) => ({
	tenant: one(tenants, { fields: [vehicleRepairs.tenantId], references: [tenants.id] }),
	vehicle: one(vehicles, { fields: [vehicleRepairs.vehicleId], references: [vehicles.id] }),
}));

export type VehicleRepair = typeof vehicleRepairs.$inferSelect;
export type NewVehicleRepair = typeof vehicleRepairs.$inferInsert;