import { relations, sql } from "drizzle-orm";
import {
	index,
	integer,
	pgTable,
	timestamp,
	uuid,
} from "drizzle-orm/pg-core";
import { tenants } from "./tenants.schema.js";
import { users } from "./users.schema.js"; // Assuming operators are users
// import { shifts } from "./shifts.schema.js"; // Assuming a shifts schema might exist

export const operatorPerformanceStats = pgTable(
	"operator_performance_stats",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id")
			.references(() => tenants.id, { onDelete: "cascade" })
			.notNull(),
		operatorUserId: uuid("operator_user_id")
			.references(() => users.id, { onDelete: "cascade" })
			.notNull(),
		date: timestamp("date", { withTimezone: true, mode: "date" }).notNull(), // Date for aggregation
		// shiftId: uuid("shift_id").references(() => shifts.id, { onDelete: "set null" }), // Optional FK to a shifts table
		shiftId: uuid("shift_id"), // Using uuid without FK for now if shifts schema doesn't exist yet

		// Performance metrics
		answeredCalls: integer("answered_calls").default(0).notNull(),
		missedCalls: integer("missed_calls").default(0).notNull(),
		outboundCalls: integer("outbound_calls").default(0).notNull(),
		totalCallDurationSeconds: integer("total_call_duration_seconds").default(0).notNull(), // Duration in seconds
		avgHandleTimeSeconds: integer("avg_handle_time_seconds").default(0).notNull(), // Average handle time in seconds
		ridesDispatched: integer("rides_dispatched").default(0).notNull(),
		smsSent: integer("sms_sent").default(0).notNull(),
		// Add other relevant metrics as needed (e.g., total login time, idle time)

		createdAt: timestamp("created_at", { withTimezone: true })
			.defaultNow()
			.notNull(),
		updatedAt: timestamp("updated_at", { withTimezone: true })
			.defaultNow()
			.notNull()
			.$onUpdate(() => new Date()),
	},
	(table) => [
		index("operator_performance_stats_tenant_id_idx").on(table.tenantId),
		index("operator_performance_stats_operator_user_id_idx").on(
			table.operatorUserId,
		),
		index("operator_performance_stats_date_idx").on(table.date),
		index("operator_performance_stats_shift_id_idx").on(table.shiftId).where(sql`${table.shiftId} IS NOT NULL`),
		// Composite index for common queries by tenant, operator, and date
		index("operator_performance_stats_tenant_operator_date_idx").on(
			table.tenantId,
			table.operatorUserId,
			table.date,
		),
	],
);

export const operatorPerformanceStatsRelations = relations(operatorPerformanceStats, ({ one }) => ({
	tenant: one(tenants, { fields: [operatorPerformanceStats.tenantId], references: [tenants.id] }),
	operatorUser: one(users, { fields: [operatorPerformanceStats.operatorUserId], references: [users.id], relationName: "operatorPerformanceStats" }),
	// shift: one(shifts, { fields: [operatorPerformanceStats.shiftId], references: [shifts.id] }), // Uncomment if shifts schema exists
}));

export type OperatorPerformanceStat = typeof operatorPerformanceStats.$inferSelect;
export type NewOperatorPerformanceStat = typeof operatorPerformanceStats.$inferInsert;