import { pgEnum } from "drizzle-orm/pg-core";

// 1. Define enum values as the single source of truth
const BOT_STATUS_VALUES = ["RUNNING", "STOPPED", "ERROR"] as const;

// 2. Create the Drizzle ORM pgEnum for database schema definition
export const botStatusEnum = pgEnum("bot_status", BOT_STATUS_VALUES);

// 3. Define TypeScript union type for strict type safety
export type BotStatusType = (typeof BOT_STATUS_VALUES)[number];

// 4. Define TypeScript string enum for application logic
export enum BotStatus {
	RUNNING = "RUNNING",
	STOPPED = "STOPPED",
	ERROR = "ERROR",
}
