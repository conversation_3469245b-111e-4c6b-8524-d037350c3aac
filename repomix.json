{"$schema": "https://repomix.com/schemas/latest/schema.json", "input": {"maxFileSize": 50000000}, "output": {"filePath": "repomix-bot.xml", "style": "xml", "parsableStyle": false, "compress": false, "headerText": "Custom header information for the packed file.", "fileSummary": true, "directoryStructure": true, "files": true, "removeComments": true, "removeEmptyLines": false, "topFilesLength": 5, "showLineNumbers": false, "copyToClipboard": false, "includeEmptyDirectories": false, "git": {"sortByChanges": true, "sortByChangesMaxCommits": 100, "includeDiffs": false}}, "include": ["packages/typescript-config/**/*", "tsconfig.json", "turbo.json"], "ignore": {"useGitignore": true, "useDefaultPatterns": true, "customPatterns": ["additional-folder", "**/*.log"]}, "security": {"enableSecurityCheck": true}, "tokenCount": {"encoding": "o200k_base"}}