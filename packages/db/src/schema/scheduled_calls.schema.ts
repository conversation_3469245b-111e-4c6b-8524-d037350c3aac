import { relations } from "drizzle-orm";
import {
	boolean,
	index,
	jsonb,
	pgTable,
	integer,
	timestamp,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { tenants } from "./tenants.schema.js";
import { users } from "./users.schema.js"; // Assuming operators are users with a specific role

import { scheduledCallStatusEnum } from "../enums/scheduled-call-status.enum.js";

export const scheduledCalls = pgTable(
	"scheduled_calls",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id")
			.references(() => tenants.id, { onDelete: "cascade" })
			.notNull(),
		createdByOperatorId: uuid("created_by_operator_id").references(
			() => users.id,
			{ onDelete: "set null" },
		), // Link to the user who created the schedule
		customerUserId: uuid("customer_user_id").references(() => users.id, {
			onDelete: "set null",
		}), // Link to the customer user, if they exist
		phoneNumber: varchar("phone_number", { length: 50 }), // Customer phone number if not a registered user

		baseCallDetails: jsonb("base_call_details").$type<Record<string, any>>(), // Snapshot of call details (location, destination, comment, etc.)

		scheduledTimestamp: timestamp("scheduled_timestamp", {
			withTimezone: true,
		}), // For one-time schedules
		arrivalTimeOffsetMinutes: integer("arrival_time_offset_minutes"), // e.g., notify 10 mins before
		repeatPattern: jsonb("repeat_pattern").$type<Record<string, any>>(), // e.g., {"type": "weekly", "days": [1, 3, 5], "time": "08:00"}

		nextActivationTimestamp: timestamp("next_activation_timestamp", {
			withTimezone: true,
		}), // Calculated next time to activate
		lastActivatedTimestamp: timestamp("last_activated_timestamp", {
			withTimezone: true,
		}), // Timestamp of the last activation

		isActive: boolean("is_active").default(true).notNull(), // Whether the schedule is currently active
		status: scheduledCallStatusEnum("status").default("PENDING").notNull(), // e.g., 'PENDING', 'ACTIVE_RECURRING', 'DISABLED'

		createdAt: timestamp("created_at", { withTimezone: true })
			.defaultNow()
			.notNull(),
		updatedAt: timestamp("updated_at", { withTimezone: true })
			.defaultNow()
			.notNull()
			.$onUpdate(() => new Date()),
	},
	(table) => [
		index("scheduled_calls_tenant_id_idx").on(table.tenantId),
		index("scheduled_calls_created_by_operator_id_idx").on(
			table.createdByOperatorId,
		),
		index("scheduled_calls_customer_user_id_idx").on(table.customerUserId),
		index("scheduled_calls_phone_number_idx").on(table.phoneNumber),
		index("scheduled_calls_next_activation_timestamp_idx").on(
			table.nextActivationTimestamp,
		), // Crucial for polling
		index("scheduled_calls_is_active_idx").on(table.isActive),
		index("scheduled_calls_status_idx").on(table.status),
	],
);

export const scheduledCallsRelations = relations(scheduledCalls, ({ one }) => ({
	tenant: one(tenants, { fields: [scheduledCalls.tenantId], references: [tenants.id] }),
	createdByOperator: one(users, { fields: [scheduledCalls.createdByOperatorId], references: [users.id], relationName: "createdScheduledCalls" }),
	customerUser: one(users, { fields: [scheduledCalls.customerUserId], references: [users.id], relationName: "customerScheduledCalls" }),
}));

export type ScheduledCall = typeof scheduledCalls.$inferSelect;
export type NewScheduledCall = typeof scheduledCalls.$inferInsert;