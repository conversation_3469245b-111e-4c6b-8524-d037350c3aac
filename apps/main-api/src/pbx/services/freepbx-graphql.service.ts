import type { HttpService } from "@nestjs/axios";
// apps/main-api/src/pbx/services/freepbx-graphql.service.ts
import {
	Injectable,
	Logger,
	type OnModuleDestroy,
	type OnModuleInit,
} from "@nestjs/common";
import type { FreePbxCdr, FreePbxGraphqlConnectionDetails } from "@repo/types";
import axios from "axios";
import type { AxiosInstance } from "axios"; // Import AxiosInstance
import { firstValueFrom } from "rxjs"; // Still needed for HttpService
import type { PbxEventRouterService } from "./pbx-event-router.service";
import type { PbxConfigService } from "./pbx-config.service";

@Injectable()
export class FreePbxGraphqlService implements OnModuleInit, OnModuleDestroy {
	private readonly logger = new Logger(FreePbxGraphqlService.name);
	private gqlClients = new Map<string, AxiosInstance>(); // pbxInstanceId -> AxiosInstance
	// ... (activePollers, circuit breaker logic can be added per client if needed)

	constructor(
		private readonly pbxConfigService: PbxConfigService,
		private readonly eventRouter: PbxEventRouterService,
		private readonly httpService: HttpService,
	) {}

	async onModuleInit() {
		this.logger.log("Initializing FreePBX GraphQL Service...");
		const gqlConfigs = this.pbxConfigService.getAllFreePbxConfigs();
		if (!gqlConfigs || gqlConfigs.length === 0) {
			this.logger.warn("No FreePBX GraphQL configurations found.");
			return;
		}
		for (const pbxConfig of gqlConfigs) {
			if (
				pbxConfig.isEnabled &&
				pbxConfig.connectionDetails.type === "FREEPBX_GRAPHQL"
			) {
				await this.setupGraphQLClient(
					pbxConfig.id,
					pbxConfig.connectionDetails,
				);
			}
		}
	}

	private async getAccessToken(
		connDetails: FreePbxGraphqlConnectionDetails,
	): Promise<string | null> {
		// Prefer API Key if present
		if (connDetails.apiKey) {
			return connDetails.apiKey; // This would likely be used in a different header, e.g., 'X-API-KEY'
		}
		if (
			!connDetails.tokenUrl ||
			!connDetails.clientId ||
			!connDetails.clientSecret
		) {
			this.logger.warn(
				`OAuth client credentials or API Key missing for FreePBX GraphQL: ${connDetails.apiUrl}`,
			);
			return null;
		}
		// ... (OAuth2 client_credentials grant flow as in your FreePbxService example)
		try {
			const params = new URLSearchParams({
				grant_type: "client_credentials",
				client_id: connDetails.clientId,
				client_secret: connDetails.clientSecret,
				scope: "gql rest", // Adjust scope as needed
			});
			const response = await firstValueFrom(
				this.httpService.post(connDetails.tokenUrl, params.toString(), {
					headers: { "Content-Type": "application/x-www-form-urlencoded" },
				}),
			);
			const accessToken = response.data.access_token;
			if (
				!accessToken ||
				typeof accessToken !== "string" /* add JWT regex check if needed */
			) {
				throw new Error(
					"Invalid access token structure received from FreePBX.",
				);
			}
			return accessToken;
		} catch (error: any) {
			this.logger.error(
				`Failed to get access token for FreePBX ${connDetails.apiUrl}:`,
				error.response?.data || error.message,
			);
			return null;
		}
	}

	private async setupGraphQLClient(
		pbxInstanceId: string,
		connDetails: FreePbxGraphqlConnectionDetails,
	) {
		this.logger.log(
			`Setting up GraphQL client for PBX ID: ${pbxInstanceId} at ${connDetails.apiUrl}`,
		);
		const token = await this.getAccessToken(connDetails);

		if (!token) {
			this.logger.error(
				`Could not authenticate with FreePBX GraphQL for ${pbxInstanceId}. Client not created.`,
			);
			return;
		}

		const headers: Record<string, string> = {
			"Content-Type": "application/json",
		};
		if (connDetails.apiKey) {
			// If using direct API Key
			headers["X-API-KEY"] = token; // Or whatever header FreePBX expects
		} else {
			// If using Bearer token from OAuth
			headers.Authorization = `Bearer ${token}`;
		}

		const apiClient = axios.create({
			// Use the imported axios directly for create
			baseURL: connDetails.apiUrl,
			headers,
			timeout: 10000,
		});
		this.gqlClients.set(pbxInstanceId, apiClient);
		this.logger.log(`GraphQL client for PBX ${pbxInstanceId} initialized.`);
	}

	private getClientOrThrow(pbxInstanceId: string): AxiosInstance {
		const client = this.gqlClients.get(pbxInstanceId);
		if (!client) {
			// Consider attempting a re-setup like in your original FreePbxService
			this.logger.error(
				`GraphQL client for PBX ${pbxInstanceId} not found. Attempt re-setup or check logs.`,
			);
			throw new Error(`GraphQL client for PBX ${pbxInstanceId} not available.`);
		}
		return client;
	}

	async fetchCdrs(
		pbxInstanceId: string,
		filterOptions: {
			startDate: string;
			endDate: string;
			extension?: string;
			callerId?: string;
			disposition?: string;
			limit?: number;
		},
	): Promise<FreePbxCdr[]> {
		const apiClient = this.getClientOrThrow(pbxInstanceId);
		const query = `query GetCdrs($startDate: String!, $endDate: String!) {
        fetchAllCdrs(filter: { calldateRange: { start: $startDate, end: $endDate } }, orderBy: date_DESC) {
          cdrs { id uniqueid calldate src dst duration disposition did }
        }
      }`; // Simplified for example
		const variables = {
			startDate: filterOptions.startDate,
			endDate: filterOptions.endDate,
		};

		try {
			const response = await apiClient.post("", { query, variables });
			const cdrs = response.data?.data?.fetchAllCdrs?.cdrs;
			if (cdrs && Array.isArray(cdrs)) {
				for (const cdr of cdrs) {
					// Pass pbxInstanceId, a type hint, and the CDR data
					await this.eventRouter.routeFreePbxEvent(
						pbxInstanceId,
						"CDR_RECEIVED",
						cdr,
					);
				}
				return cdrs;
			}
			return [];
		} catch (error: any) {
			this.logger.error(
				`Failed to fetch CDRs from FreePBX ${pbxInstanceId}:`,
				error.response?.data || error.message,
			);
			throw error;
		}
	}

	// ... other GraphQL methods ...

	async onModuleDestroy() {
		this.logger.log("Destroying FreePbxGraphqlService...");
		this.gqlClients.clear();
		// Clear any pollers if you implement them
	}
}
