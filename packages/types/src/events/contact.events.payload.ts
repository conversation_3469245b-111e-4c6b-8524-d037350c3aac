// packages/types/src/events/contact.events.payload.ts
import type { BaseEventPayload } from "./base-event.payload.js";

export interface ContactInfo {
	name?: string;
	served?: number;
	canceled?: number;
	locations?: string[]; // Common locations
	destinations?: string[]; // Common destinations
	firstContactTimestamp?: string; // ISO 8601
	preferredLanguage?: string;
}

export interface ContactInfoRequestedPayload extends BaseEventPayload {
	data: {
		phoneNumber: string;
	};
}

export interface ContactInfoProvidedPayload extends BaseEventPayload {
	data: {
		phoneNumber: string;
		contactInfo: ContactInfo | null;
	};
}

export interface ContactNameUpdateRequestedPayload extends BaseEventPayload {
	data: {
		phoneNumber: string;
		name: string;
	};
}

export interface ContactNameUpdatedPayload extends BaseEventPayload {
	data: {
		phoneNumber: string;
		name: string;
	};
}