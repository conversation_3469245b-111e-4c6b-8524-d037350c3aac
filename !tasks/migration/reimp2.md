This is fantastic! Seeing the actual Drizzle schema files from `packages/db` gives us a very concrete "source of truth" for the data model in the new system. This is much better than inferring from the old system alone.

Given this, let's refine the "Base for Reimplementation" document, specifically focusing on how the old system's data and features map to *your new, already defined Drizzle schema*. We'll also highlight any potential gaps or areas where the old system's logic might need careful translation to the new schema.

**Key Observations from Your New DB Schema:**

1. **Multi-Tenancy is Core:** `tenants.schema.ts` is central, and many tables ( `bots`, `roles`, `pbx-call`, `operators`, `vehicle`, `area`, etc.) have a `tenantId`. This is a major architectural decision already made.
2. **User Model:** `users.schema.ts` is a generic user table. `operators.schema.ts` links to `user_tenant` which links `users` to `tenants` with a role. This is a good, flexible structure.
3. **Call Logging:** `pbx-call.schema.ts` is the new equivalent of the old `calls` table. It includes `tenantId`, links to `users` (for passenger/customer), `operators`, and `rides`.
4. **Chatbot Focus:** A significant number of schemas are dedicated to chatbot functionality (`chatbot-configs`, `chatbot-events`, `chatbot-instances`, `chatbot-messages`, `chatbot-providers`, `chatbot-sessions`, `chatbot-users`). This confirms the new feature direction.
5. **Ride Management:** `rides.schema.ts`, `ride-order.schema.ts`, `ride-event.schema.ts`, `ride-rating.schema.ts`, `dispatch-assignment.schema.ts`, `driver-vehicle.schema.ts` point to the "Uber-like" system for managing rides, drivers, and passengers. This is a major expansion from the old system.
6. **Telegram Integration:** Specific tables for Telegram (`telegram_chats`, `telegram_messages`, `telegram_users`, `telegram_chat_members`, `teleram_user_settings`) indicate deep integration with this platform, likely as a primary channel for chatbots and user interaction.
7. **Enums:** A comprehensive set of enums (`packages/db/src/enums/`) provides strong typing for status fields, types, etc.
8. **No Explicit `scheduled_calls` or `contacts` Table (yet):** Similar to our previous discussion, these specific tables from the old system's direct functionality aren't explicitly present.
    * Scheduled calls might be a specific type/status within `pbx-call.schema.ts` or `ride-order.schema.ts` (e.g., `rideOrder.scheduledTime` and a status like 'SCHEDULED_PENDING').
    * Contact-specific data (like preferred name, aggregated history) might be intended to live within `users.schema.ts` (`display_name`) and be aggregated on-the-fly or cached in Redis, rather than a separate `contacts` table. The old system's Redis-heavy approach for contact history (locations/destinations) might still be planned.

---

## Updated "Base for Reimplementation" Documentation (Incorporating New DB Schema)

```markdown
# Reimplementation Blueprint for Next.js + NestJS Monorepo (Cloud Dispatch System)

---

## 1. Purpose and Scope

-   **State clearly**: This document's goal is to serve as the foundational blueprint for rewriting the existing Traksi application (old JavaScript backend + Angular frontend) into a modern, multi-tenant, cloud-native monorepo structure. The new system will feature a Next.js frontend (operator dashboard, potentially passenger/driver apps in future) and a NestJS backend, with significant new capabilities including multi-platform chatbot integration (starting with Telegram) and advanced ride-hailing/dispatch functionalities.
-   **Define scope**: This document covers:
    *   Mapping core features from the old system to the new architecture.
    *   Defining API contracts (REST, RabbitMQ events, WebSocket pushes).
    *   Leveraging the new Drizzle ORM database schema as the source of truth for data.
    *   Outlining data flow and event handling, emphasizing a decoupled, event-driven approach using RabbitMQ.
    *   Architectural principles for the new cloud-based, "Uber-like" dispatch system.

    **Overview of Key Aspects for the Rewrite:**

    *   **API Contracts:**
        *   **REST APIs (NestJS):** For tenant administration, bot configuration, external integrations (PBX, SMS Gateways, payment processors), and initial data loads for the dashboard. Documented with OpenAPI.
        *   **RabbitMQ Events:** The primary mechanism for inter-service communication, asynchronous task processing, and signaling state changes.
        *   **WebSocket API (NestJS Gateway):** Primarily for pushing real-time updates (originating from RabbitMQ events) to connected dashboard clients. Direct client-to-server commands via WebSocket will be minimized in favor of REST calls that trigger RabbitMQ events.
    *   **Data Flow & Event Handling:**
        *   User actions on the dashboard trigger REST API calls to NestJS "Command Services".
        *   Command Services validate requests and publish domain events to RabbitMQ.
        *   NestJS "Event Handler Services" (workers) consume these events, update the PostgreSQL database (via Drizzle ORM from `@monorepo/db`), and may publish further events.
        *   A dedicated `WebSocketPushService` consumes specific RabbitMQ events and pushes formatted updates to relevant Next.js dashboard clients.
        *   PBX events and external system notifications are received via REST webhooks, processed, and also result in RabbitMQ events.
    *   **Architecture Principles:**
        *   **Multi-Tenancy:** System designed from the ground up to support multiple tenants, each with their own configurations, users, bots, and data (scoped by `tenantId` in database tables).
        *   **Event-Driven & Decoupled Services:** Using RabbitMQ to decouple services, allowing for independent scaling and development.
        *   **CQRS-Lite:** Separation of command (state-changing) and query (data-retrieval) paths.
        *   **Cloud-Native:** Designed for deployment in a cloud environment, leveraging Docker, and managed services where appropriate.
    *   **New Features (Uber-like & Chatbots):**
        *   Comprehensive ride management (ordering, dispatch, tracking, rating).
        *   Driver and vehicle management.
        *   Multi-platform chatbot integration (Telegram first, then Viber, WhatsApp, etc.) for user interaction, booking, and support.

---

## 2. High-Level Architectural Overview

-   **System Diagram**:
    ```mermaid
    graph TD
        subgraph "User Interfaces"
            A[Next.js Operator Dashboard]
            A1[Telegram/Viber/etc. Users via Chatbots]
            A2[Future: Passenger App]
            A3[Future: Driver App]
        end

        subgraph "Backend Services (NestJS - main-api)"
            B[API Gateway: REST (Commands/Queries) & WebSockets (Push only)]
            C[Auth Service]
            D[Call/Ride Command Service]
            D1[Call/Ride Query Service]
            E[Scheduling Command Service]
            E1[Scheduling Query Service]
            F[Messaging Command Service (SMS, Chatbot Replies)]
            G[Statistics Query Service]
            H[Contact/User Query Service]
            I[Tenant Admin Service]
            J[Bot Config Service]
            P[PBX Integration Service]
            WS[WebSocket Push Service]
        end

        subgraph "Data Stores"
            K[PostgreSQL DB (@monorepo/db - Drizzle ORM)]
            L[Redis (Cache, Session State, Rate Limiting, Chatbot State)]
        end

        subgraph "Message Queue & Event Bus"
            M[RabbitMQ]
        end

        subgraph "External Systems & Gateways"
            N[Geocoding API]
            O[PBX/Telephony Gateway]
            P_GW[SMS Gateway]
            Q[Payment Gateway]
            R[Chat Platforms (Telegram API, Viber API etc.)]
        end

        A -- REST API (Commands/Queries) --> B
        A -- WebSocket (Receives Pushes) <-- WS

        A1 -- Platform API --> R
        R -- Webhook --> B  // BotWebhookController

        B <--> C
        B --> D & D1
        B --> E & E1
        B --> F
        B --> G
        B --> H
        B --> I
        B --> J
        B --> P

        D -- Publishes Events --> M
        E -- Publishes Events --> M
        F -- Publishes Events --> M
        P -- Publishes Events (e.g., pbx.event.received) --> M

        subgraph "Event Consumers/Workers (NestJS)"
            W_Call[Call Event Handler]
            W_Sched[Schedule Event Handler]
            W_Msg[Messaging Worker (SMS, Chatbot Out)]
            W_Notif[Notification Dispatcher]
            W_Stats[Statistics Aggregator (optional)]
            W_Chatbot[Chatbot Logic Processor]
        end

        M -- Consumed by --> W_Call
        M -- Consumed by --> W_Sched
        M -- Consumed by --> W_Msg
        M -- Consumed by --> W_Notif
        M -- Consumed by --> W_Stats
        M -- Consumed by --> W_Chatbot
        M -- Consumed by --> WS // WebSocketPushService subscribes to relevant topics

        W_Call -- CRUD --> K
        W_Sched -- CRUD --> K & L
        W_Msg -- Integrates --> P_GW & R
        W_Chatbot -- CRUD --> K (chatbot tables) & L (session state)
        D1 & E1 & G & H & I & J -- Read --> K & L

        O -- HTTP/Telephony API --> P
        P_GW -- SMS Status --> B

        %% Dockerized Components
        subgraph "Docker Environment"
            K
            L
            M
            NestApp[NestJS Backend App (main-api)]
            NextDashboard[Next.js Dashboard App]
        end
        NestApp --> K
        NestApp --> L
        NestApp --> M
        NextDashboard --> NestApp
    ```

-   **Component Breakdown**:
    -   **Frontend (`apps/dashboard` - Next.js)**:
        -   Routing, State (Zustand, React Context), UI (shadcn/ui), i18n (`next-i18next`).
        -   **API Interaction**: Primarily REST for commands/queries. WebSocket connection for receiving real-time updates pushed by `WebSocketPushService`.
    -   **Backend (`apps/main-api` - NestJS)**:
        -   **Modules**: As identified (Auth, Users, Calls/Rides, Scheduling, Messaging, Statistics, Contacts, Tenants, Bots, PbxIntegration, WebSocketPush, RabbitMQConfig, DrizzleDatabase, RedisCache).
        -   **REST Controllers**: Handle command submissions and query requests from the dashboard and external systems.
        -   **Command Services**: Validate input, publish domain events to RabbitMQ.
        -   **Query Services**: Fetch data from PostgreSQL/Redis for REST controllers.
        -   **Event Handlers (Workers)**: Subscribe to RabbitMQ queues, process events, update database, publish further events.
        -   **`WebSocketPushService`**: Subscribes to specific RabbitMQ topics and forwards relevant data to connected dashboard clients via WebSockets.
        -   **Drizzle ORM**: Used in services/handlers for database interaction, leveraging schemas from `@monorepo/db`.
    -   **Shared (`packages/`)**:
        -   `@monorepo/db`: Drizzle schemas, enums, migration scripts, DB client. **This is the source of truth for data structures.**
        -   `@monorepo/types`: TypeScript interfaces for RabbitMQ event payloads, REST DTOs, WebSocket push message structures.
        -   `@monorepo/utils`: Common utilities.

---

## 3. Core Principles & Design Patterns

-   **Component-driven UI** with shadcn/ui.
-   **Type-safe communication**: Detailed TypeScript interfaces (in `@monorepo/types`) for RabbitMQ events, REST DTOs, and WebSocket push messages, derived from or consistent with Drizzle schemas.
-   **Event-driven architecture** via RabbitMQ for backend inter-service communication and asynchronous processing.
-   **CQRS-Lite Pattern**: Separation of command-handling services (which publish events) and query-handling services (which read data).
-   **Separation of concerns**: NestJS modules, services, controllers. Next.js components, hooks, stores.
-   **State management (Next.js)**: Zustand for global/complex state; React Context for local.
-   **Multi-Tenancy**: Data isolation enforced at the database level (`tenantId`) and respected by all services.

---

## 4. API & Event Contract Summary

-   **REST API endpoints (`apps/main-api`)**:
    -   Defined in `docs/openapi.yaml`. Primarily for:
        -   Dashboard sending commands (e.g., POST `/tenants/{tenantId}/calls/create-request`) which then publish RabbitMQ events.
        -   Dashboard fetching initial data or paginated lists (e.g., GET `/tenants/{tenantId}/calls`).
        -   External system webhooks (e.g., POST `/webhooks/pbx/call-event`, POST `/webhooks/telegram/{botToken}`).
    -   Authentication: JWT for dashboard users. API Keys for external systems.
-   **RabbitMQ Events (Internal Backend Bus)**:
    -   *Naming Convention*: `domain.entity.action` (e.g., `call.created`, `pbxevent.received`, `schedule.activation.due`, `sms.send.request`).
    -   *Payloads*: Defined in `@monorepo/types`, consistent with Drizzle schemas.
    -   *Exchanges/Queues*: To be defined (e.g., a topic exchange per domain, durable queues for workers).
-   **WebSocket Push Events (NestJS `WebSocketPushService` → Next.js Dashboard)**:
    -   *Naming Convention*: `ui.update.<entity>` or `ui.notification.<type>` (e.g., `ui.update.callList`, `ui.update.callDetails`, `ui.notification.info`).
    -   *Payloads*: Formatted specifically for dashboard consumption, derived from internal RabbitMQ events.
    -   (Refer to `docs/websocket-protocol.md` for the *old* system's events; the new system will simplify client-bound WS events to primarily be data pushes).
-   **Data models (`@monorepo/db/src/schema/`)**:
    -   The Drizzle schemas are the source of truth.
    -   DTOs in NestJS will validate incoming REST payloads against these structures.
    -   Interfaces in `@monorepo/types` will mirror these for event payloads.

---

## 5. Data Flow & Command Logic (New Event-Driven Approach)

-   **Feature-specific workflows**:
    -   **Scheduled Call Creation**:
        1.  Dashboard POSTs to `/tenants/{tenantId}/schedules/create-request` with schedule details.
        2.  `ScheduleCommandService` validates, publishes `schedule.create.requested` to RabbitMQ.
        3.  `ScheduleEventHandlerService` consumes, creates `ScheduledCall` record in DB (or updates `pbx-call` with schedule info), sets up a persistent job/message in a delayed RabbitMQ exchange (or dedicated job queue like BullMQ) for activation. Publishes `schedule.created`.
        4.  `WebSocketPushService` consumes `schedule.created`, pushes update to dashboard.
    -   **Live Call Answered (PBX Event)**:
        1.  PBX POSTs to `/webhooks/pbx/call-event` (type: `answered`).
        2.  `PbxEventController` -> `PbxIntegrationService` publishes `pbx.call.answered` to RabbitMQ.
        3.  `CallEventHandlerService` consumes, updates `PbxCall` record (sets `timeanswered`, `operator_id`), publishes `call.answered`.
        4.  `WebSocketPushService` consumes `call.answered`, pushes update.
    -   **Operator Assigns Vehicle to Call**:
        1.  Dashboard POSTs to `/tenants/{tenantId}/calls/{callId}/assign-vehicle-request` with `vehicleId`.
        2.  `CallCommandService` validates, publishes `call.assign-vehicle.requested`.
        3.  `CallEventHandlerService` consumes, updates `PbxCall` (adds vehicle, sets `timeassigned`), publishes `call.vehicle.assigned`.
        4.  `WebSocketPushService` consumes, pushes update.
        5.  (Optional) `SmsNotificationService` consumes `call.vehicle.assigned`, publishes `sms.send.request` for "vehicle assigned" SMS.
    -   **Messaging (Send SMS from Dashboard)**:
        1.  Dashboard POSTs to `/tenants/{tenantId}/messaging/sms/send-request` with phone, message.
        2.  `SmsCommandService` validates, publishes `sms.send.request`.
        3.  `SmsSendingWorker` consumes, calls SMS Gateway, publishes `sms.sent` or `sms.failed`.
        4.  `WebSocketPushService` consumes `sms.sent`/`sms.failed`, pushes status update to originating operator's dashboard.
        5.  `SmsLogService` consumes `sms.sent`/`sms.failed`, creates `SmsLog` record.
    -   **Statistics Calculation**:
        -   Nightly/periodic job (NestJS Cron or external scheduler triggering a REST endpoint) invokes `StatisticsService` to aggregate data from `pbx-call` into summary tables or Redis cache.
        -   Dashboard GETs `/tenants/{tenantId}/statistics/daily` from `StatisticsQueryService` which reads pre-aggregated data or calculates on-the-fly for recent periods.
-   **Real-time updates**: `WebSocketPushService` is the sole source of WebSocket pushes to the dashboard, driven by consuming relevant RabbitMQ events.

---

## 6. Security & Authentication

-   **Authentication**:
    -   Users (operators, admins) authenticate via a REST endpoint (e.g., `/auth/login`) providing credentials.
    -   Successful authentication returns a JWT.
    -   This JWT is used as a Bearer token for subsequent REST API calls from the dashboard.
    -   The WebSocket connection from the dashboard will also be authenticated using this JWT (e.g., passed during connection handshake or as an initial message).
-   **Authorization**:
    -   NestJS Guards (`RolesGuard`, custom permission guards) used on REST controllers and potentially on RabbitMQ event handlers (to ensure an event was published by an authorized source or pertains to an authorized tenant).
    -   Roles and permissions defined in `roles.schema.ts` and linked via `user_tenant.schema.ts`.
-   **Tenant Data Isolation**: All service logic and database queries MUST be strictly scoped by `tenantId`.
-   **API Key Management**: For external systems (PBX, SMS Gateway) calling into NestJS webhooks, implement API key authentication. Keys stored securely, associated with tenants or specific integrations.
-   **Bot Token Security**: Bot tokens in `bots.schema.ts` are encrypted using `encryptBotToken` utility (AES-256-GCM with a master key from env). Decrypted only when needed by `BotProcessingService`.

---

## 7. Technology & Libraries (Confirming from new structure)

-   **Frontend (`apps/dashboard` - Next.js)**: (Assumed, as `mini-app` is SolidJS) React, shadcn/ui, Tailwind, Zustand, React Hook Form, Zod, `next-i18next`.
-   **Backend (`apps/main-api` - NestJS)**:
    -   WebSocket: `@nestjs/websockets` (likely `ws` adapter).
    -   Database ORM: Drizzle ORM (`@monorepo/db` package).
    -   Caching/Session: `@liaoliaots/nestjs-redis`.
    -   Message Queue: `@golevelup/nestjs-rabbitmq` and `rascal`.
    -   Telegram Bot: `gramio` and its ecosystem.
    -   Auth: `@nestjs/jwt`, `passport`.
-   **Shared (`packages/`)**:
    -   `@monorepo/db`: Drizzle schemas, enums, migrations.
    -   `@monorepo/types`: (To be created) TypeScript interfaces for DTOs, RabbitMQ events, WebSocket push payloads.
-   **Build/Tooling**: Turbo, Biome, Bun, Docker.

---

## 8. Development & Deployment Guidelines

-   Monorepo structure as defined by `turbo.json` and `package.json` workspaces.
-   **Database Migrations**: Managed by Drizzle Kit scripts in `packages/db/scripts/` (`generate-migrations.ts`, `migrate.ts`).
-   **CI/CD**:
    -   Linting/formatting (Biome) and type-checking (TypeScript) in CI.
    -   Automated tests (unit, integration).
    -   Build Docker images for `main-api` and `dashboard`.
    -   Deploy to cloud (e.g., Kubernetes, ECS, Serverless Functions for parts if applicable).
    -   Run database migrations as a step in the deployment pipeline before the new application version goes live.
-   **Configuration**:
    -   NestJS `ConfigModule` (`@nestjs/config`) loading from `.env` files and environment variables.
    -   Strict validation of environment variables (e.g., using `env-var` or Joi/Zod within `ConfigModule`).

---

This revised blueprint now strongly reflects your new architecture and vision. The next steps would involve:

1.  **Detailing RabbitMQ Event Payloads and Topics/Exchanges:** This is critical for the event-driven flow.
2.  **Defining REST API DTOs:** For all command and query endpoints.
3.  **Mapping Old UI Components to New Next.js/shadcn Structure:** A more granular UI/UX design phase.
4.  **Prioritizing Features for Sprints:** Start with core auth, tenant setup, and a single end-to-end flow (e.g., PBX incoming call -> display on dashboard).
5.  **Setting up the CI/CD pipeline early.**
6.  **Ensuring the Drizzle schemas in `@monorepo/db` are complete** for the initial set of features, especially for call logging (`pbx-call.schema.ts`), scheduling, and basic contact/user info. The existing schemas are very comprehensive and provide an excellent start.
