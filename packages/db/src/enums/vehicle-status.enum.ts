import { pgEnum } from "drizzle-orm/pg-core";

const VEHICLE_STATUS_VALUES = [
	"ACTIVE",
	"DISABLED",
	"DECOMMISSIONED",
	"REPAIR",
] as const;

export const vehicleStatusEnum = pgEnum("vehicle_status", VEHICLE_STATUS_VALUES);

export type VehicleStatusType = (typeof VEHICLE_STATUS_VALUES)[number];

export enum VehicleStatus {
	ACTIVE = "ACTIVE",
	DISABLED = "DISABLED",
	DECOMMISSIONED = "DECOMMISSIONED",
	REPAIR = "REPAIR",
}
