// THIS FILE IS AUTO-GENERATED. DO NOT EDIT MANUALLY.

export type TranslationKey =
	| "call.status.new"
	| "call.status.assigned"
	| "call.status.answered"
	| "call.status.completed"
	| "call.status.unanswered"
	| "call.status.served"
	| "call.status.unserved"
	| "call.status.canceled"
	| "call.errors.CALL_CREATE_FAILED"
	| "call.errors.CALL_NOT_FOUND"
	| "call.errors.CALL_UPDATE_FAILED"
	| "call.errors.CALL_DELETE_FAILED"
	| "call.errors.RECORD_ADD_FAILED"
	| "call.errors.RECORD_UPDATE_FAILED"
	| "call.messages.CALL_CREATED"
	| "call.messages.CALL_UPDATED"
	| "call.messages.CALL_DELETED"
	| "call.warnings.CALL_NOT_FOUND"
	| "call.validation.FIELD_REQUIRED"
	| "callback.service.initialized"
	| "callback.service.initiating"
	| "callback.service.invalid_number"
	| "callback.service.published"
	| "callback.service.success"
	| "callback.service.publish_failed"
	| "callback.errors.invalid_phone_number"
	| "callback.errors.publish_failed"
	| "common.test.HELLO"
	| "common.errors.NOT_FOUND"
	| "common.errors.UNAUTHORIZED"
	| "common.errors.VALIDATION"
	| "email.email.service.initialized"
	| "email.email.service.email_sent"
	| "email.email.service.send_failed"
	| "email.email.service.unexpected_error"
	| "email.email.validation.invalid_input"
	| "messageVehicle.vehicleAtDestination"
	| "messageVehicle.noVehicle"
	| "messageVehicle.vehicleAssigned"
	| "messageVehicle.vehicleUnassigned"
	| "messageVehicle.vehicleUpdated"
	| "messageVehicle.vehicleCanceled"
	| "operator.operator.status.active"
	| "operator.operator.status.inactive"
	| "operator.operator.status.suspended"
	| "operator.operator.validation.name_required"
	| "operator.operator.validation.name_string"
	| "operator.operator.validation.name_min_length"
	| "operator.operator.validation.password_required"
	| "operator.operator.validation.password_min_length"
	| "operator.operator.validation.password_complexity"
	| "operator.operator.validation.invalid_data"
	| "operator.operator.errors.load_failed"
	| "operator.operator.errors.create_failed"
	| "operator.operator.errors.not_found"
	| "operator.operator.errors.update_failed"
	| "operator.operator.errors.delete_failed"
	| "operator.operator.errors.translation_failed"
	| "operator.operator.errors.find_failed"
	| "operator.operator.errors.status_invalid"
	| "operator.operator.errors.database_error"
	| "operator.operator.errors.auth_error"
	| "operator.operator.errors.validation_error"
	| "order_state.order_errors.ORDER_UPDATE_FAILED"
	| "order_state.order_errors.ORDER_CREATE_FAILED"
	| "order_state.order_errors.ORDER_FETCH_FAILED"
	| "order_state.order_errors.ORDERS_FETCH_FAILED"
	| "order_state.order_errors.ORDER_REMOVE_FAILED"
	| "order_state.order_status.STATUS_PENDING"
	| "order_state.order_status.STATUS_PROCESSING"
	| "order_state.order_status.STATUS_COMPLETED"
	| "order_state.order_status.STATUS_CANCELED"
	| "order_state.validation.PRODUCT_ID_INVALID"
	| "order_state.validation.QUANTITY_MIN"
	| "order_state.validation.QUANTITY_MAX"
	| "order_state.validation.TOTAL_INVALID"
	| "order_state.validation.USER_ID_REQUIRED"
	| "rabbitmq.rabbitmq.connection.retry"
	| "rabbitmq.rabbitmq.connection.retry_delay"
	| "rabbitmq.rabbitmq.connection.connected"
	| "rabbitmq.rabbitmq.connection.connection_failed"
	| "rabbitmq.rabbitmq.processor.received_message"
	| "rabbitmq.rabbitmq.processor.send_error"
	| "redis.config.created"
	| "redis.service.initialized"
	| "redis.service.getting_key"
	| "redis.service.retrieved_value"
	| "redis.service.get_error"
	| "redis.service.setting_key"
	| "redis.service.set_success"
	| "redis.service.set_error"
	| "redis.service.deleting_key"
	| "redis.service.delete_success"
	| "redis.service.delete_error"
	| "redis.service.setting_expire"
	| "redis.service.expire_success"
	| "redis.service.expire_error"
	| "schedule.create.success"
	| "schedule.create.error"
	| "schedule.create.validation.title_required"
	| "schedule.create.validation.start_time_invalid"
	| "schedule.create.validation.end_time_after_start"
	| "schedule.fetch.all.success"
	| "schedule.fetch.all.error"
	| "schedule.fetch.one.success"
	| "schedule.fetch.one.error"
	| "schedule.fetch.one.not_found"
	| "schedule.update.success"
	| "schedule.update.error"
	| "schedule.update.unauthorized"
	| "schedule.remove.success"
	| "schedule.remove.error"
	| "schedule.remove.confirmation"
	| "schedule.validation.title_length"
	| "schedule.validation.location_length"
	| "schedule.validation.invalid_status"
	| "settings.settings.errors.initialization_failed"
	| "settings.settings.errors.invalid_settings"
	| "settings.settings.messages.initialized"
	| "settings.settings.messages.updated"
	| "taxi.settings.initialized"
	| "tcpsms.errors.missing_parameters"
	| "tcpsms.errors.publish_failed"
	| "tcpsms.errors.send_failed"
	| "tcpsms.errors.log_failed"
	| "tcpsms.errors.log_write_failed"
	| "tcpsms.controller.missing_fields"
	| "tcpsms.controller.message_sent"
	| "tcpsms.controller.message_failed"
	| "tcpsms.controller.send_error"
	| "tcpsms.message.published"
	| "tcpsms.error.log_format"
	| "tcpsms.error.details"
	| "test.test.HELLO"
	| "test.test.connection.retry"
	| "test.test.connection.retry_delay"
	| "test.test.connection.connected"
	| "test.test.connection.connection_failed"
	| "test.test.processor.received_message"
	| "test.test.processor.send_error"
	| "user.service.user_created"
	| "user.service.user_exists"
	| "user.service.find_all"
	| "user.service.user_found"
	| "user.service.user_not_found"
	| "user.service.update_error"
	| "user.service.user_updated"
	| "user.service.user_removed"
	| "user.service.remove_failed"
	| "user.validation.NOT_EMPTY"
	| "user.validation.INVALID_EMAIL"
	| "user.validation.MIN_LENGTH"
	| "user.validation.EMAIL_EXISTS"
	| "validation.validation.NOT_EMPTY"
	| "validation.validation.INVALID_EMAIL"
	| "validation.validation.INVALID_BOOLEAN"
	| "validation.validation.MIN"
	| "validation.validation.MAX"
	| "validation.validation.INVALID_PHONE"
	| "validation.validation.INVALID_DATE"
	| "validation.validation.PASSWORD_MISMATCH"
	| "validation.validation.INVALID_URL"
	| "validation.validation.EMAIL_EXISTS"
	| "telegram.telegram.welcome"
	| "telegram.telegram.welcome_back"
	| "telegram.telegram.messageReceived"
	| "telegram.telegram.taksi"
	| "telegram.telegram.registration"
	| "telegram.messages.received"
	| "telegram.menu.order_taxi"
	| "telegram.menu.register"
	| "telegram.menu.request_ride"
	| "telegram.menu.back"
	| "telegram.menu.select_option"
	| "telegram.errors.general"
	| "telegram.errors.start_command"
	| "telegram.registration.already_registered"
	| "telegram.registration.request_phone"
	| "telegram.registration.share_phone"
	| "telegram.registration.invalid_phone"
	| "telegram.registration.verification_code_sent"
	| "telegram.registration.demo_code"
	| "telegram.registration.enter_code"
	| "telegram.registration.invalid_code"
	| "telegram.registration.success"
	| "telegram.registration.error"
	| "telegram.taxi.start"
	| "telegram.taxi.share_location"
	| "telegram.taxi.pickup_location"
	| "telegram.taxi.destination"
	| "telegram.taxi.vehicle_type"
	| "telegram.taxi.payment_method"
	| "telegram.taxi.notes"
	| "telegram.taxi.summary"
	| "telegram.taxi.confirm"
	| "telegram.taxi.cancel"
	| "telegram.taxi.confirmed"
	| "telegram.taxi.cancelled"
	| "telegram.taxi.error"
	| "telegram.help.title"
	| "telegram.help.commands"
	| "registration.already_registered"
	| "registration.request_phone"
	| "registration.share_phone"
	| "registration.invalid_phone"
	| "registration.verification_code_sent"
	| "registration.demo_code"
	| "registration.enter_code"
	| "registration.invalid_code"
	| "registration.success"
	| "registration.error";

export interface I18nTranslations {
	"call.status.new": string;
	"call.status.assigned": string;
	"call.status.answered": string;
	"call.status.completed": string;
	"call.status.unanswered": string;
	"call.status.served": string;
	"call.status.unserved": string;
	"call.status.canceled": string;
	"call.errors.CALL_CREATE_FAILED": string;
	"call.errors.CALL_NOT_FOUND": string;
	"call.errors.CALL_UPDATE_FAILED": string;
	"call.errors.CALL_DELETE_FAILED": string;
	"call.errors.RECORD_ADD_FAILED": string;
	"call.errors.RECORD_UPDATE_FAILED": string;
	"call.messages.CALL_CREATED": string;
	"call.messages.CALL_UPDATED": string;
	"call.messages.CALL_DELETED": string;
	"call.warnings.CALL_NOT_FOUND": string;
	"call.validation.FIELD_REQUIRED": string;
	"callback.service.initialized": string;
	"callback.service.initiating": string;
	"callback.service.invalid_number": string;
	"callback.service.published": string;
	"callback.service.success": string;
	"callback.service.publish_failed": string;
	"callback.errors.invalid_phone_number": string;
	"callback.errors.publish_failed": string;
	"common.test.HELLO": string;
	"common.errors.NOT_FOUND": string;
	"common.errors.UNAUTHORIZED": string;
	"common.errors.VALIDATION": string;
	"email.email.service.initialized": string;
	"email.email.service.email_sent": string;
	"email.email.service.send_failed": string;
	"email.email.service.unexpected_error": string;
	"email.email.validation.invalid_input": string;
	"messageVehicle.vehicleAtDestination": string;
	"messageVehicle.noVehicle": string;
	"messageVehicle.vehicleAssigned": string;
	"messageVehicle.vehicleUnassigned": string;
	"messageVehicle.vehicleUpdated": string;
	"messageVehicle.vehicleCanceled": string;
	"operator.operator.status.active": string;
	"operator.operator.status.inactive": string;
	"operator.operator.status.suspended": string;
	"operator.operator.validation.name_required": string;
	"operator.operator.validation.name_string": string;
	"operator.operator.validation.name_min_length": string;
	"operator.operator.validation.password_required": string;
	"operator.operator.validation.password_min_length": string;
	"operator.operator.validation.password_complexity": string;
	"operator.operator.validation.invalid_data": string;
	"operator.operator.errors.load_failed": string;
	"operator.operator.errors.create_failed": string;
	"operator.operator.errors.not_found": string;
	"operator.operator.errors.update_failed": string;
	"operator.operator.errors.delete_failed": string;
	"operator.operator.errors.translation_failed": string;
	"operator.operator.errors.find_failed": string;
	"operator.operator.errors.status_invalid": string;
	"operator.operator.errors.database_error": string;
	"operator.operator.errors.auth_error": string;
	"operator.operator.errors.validation_error": string;
	"order_state.order_errors.ORDER_UPDATE_FAILED": string;
	"order_state.order_errors.ORDER_CREATE_FAILED": string;
	"order_state.order_errors.ORDER_FETCH_FAILED": string;
	"order_state.order_errors.ORDERS_FETCH_FAILED": string;
	"order_state.order_errors.ORDER_REMOVE_FAILED": string;
	"order_state.order_status.STATUS_PENDING": string;
	"order_state.order_status.STATUS_PROCESSING": string;
	"order_state.order_status.STATUS_COMPLETED": string;
	"order_state.order_status.STATUS_CANCELED": string;
	"order_state.validation.PRODUCT_ID_INVALID": string;
	"order_state.validation.QUANTITY_MIN": string;
	"order_state.validation.QUANTITY_MAX": string;
	"order_state.validation.TOTAL_INVALID": string;
	"order_state.validation.USER_ID_REQUIRED": string;
	"rabbitmq.rabbitmq.connection.retry": string;
	"rabbitmq.rabbitmq.connection.retry_delay": string;
	"rabbitmq.rabbitmq.connection.connected": string;
	"rabbitmq.rabbitmq.connection.connection_failed": string;
	"rabbitmq.rabbitmq.processor.received_message": string;
	"rabbitmq.rabbitmq.processor.send_error": string;
	"redis.config.created": string;
	"redis.service.initialized": string;
	"redis.service.getting_key": string;
	"redis.service.retrieved_value": string;
	"redis.service.get_error": string;
	"redis.service.setting_key": string;
	"redis.service.set_success": string;
	"redis.service.set_error": string;
	"redis.service.deleting_key": string;
	"redis.service.delete_success": string;
	"redis.service.delete_error": string;
	"redis.service.setting_expire": string;
	"redis.service.expire_success": string;
	"redis.service.expire_error": string;
	"schedule.create.success": string;
	"schedule.create.error": string;
	"schedule.create.validation.title_required": string;
	"schedule.create.validation.start_time_invalid": string;
	"schedule.create.validation.end_time_after_start": string;
	"schedule.fetch.all.success": string;
	"schedule.fetch.all.error": string;
	"schedule.fetch.one.success": string;
	"schedule.fetch.one.error": string;
	"schedule.fetch.one.not_found": string;
	"schedule.update.success": string;
	"schedule.update.error": string;
	"schedule.update.unauthorized": string;
	"schedule.remove.success": string;
	"schedule.remove.error": string;
	"schedule.remove.confirmation": string;
	"schedule.validation.title_length": string;
	"schedule.validation.location_length": string;
	"schedule.validation.invalid_status": string;
	"settings.settings.errors.initialization_failed": string;
	"settings.settings.errors.invalid_settings": string;
	"settings.settings.messages.initialized": string;
	"settings.settings.messages.updated": string;
	"taxi.settings.initialized": string;
	"tcpsms.errors.missing_parameters": string;
	"tcpsms.errors.publish_failed": string;
	"tcpsms.errors.send_failed": string;
	"tcpsms.errors.log_failed": string;
	"tcpsms.errors.log_write_failed": string;
	"tcpsms.controller.missing_fields": string;
	"tcpsms.controller.message_sent": string;
	"tcpsms.controller.message_failed": string;
	"tcpsms.controller.send_error": string;
	"tcpsms.message.published": string;
	"tcpsms.error.log_format": string;
	"tcpsms.error.details": string;
	"test.test.HELLO": string;
	"test.test.connection.retry": string;
	"test.test.connection.retry_delay": string;
	"test.test.connection.connected": string;
	"test.test.connection.connection_failed": string;
	"test.test.processor.received_message": string;
	"test.test.processor.send_error": string;
	"user.service.user_created": string;
	"user.service.user_exists": string;
	"user.service.find_all": string;
	"user.service.user_found": string;
	"user.service.user_not_found": string;
	"user.service.update_error": string;
	"user.service.user_updated": string;
	"user.service.user_removed": string;
	"user.service.remove_failed": string;
	"user.validation.NOT_EMPTY": string;
	"user.validation.INVALID_EMAIL": string;
	"user.validation.MIN_LENGTH": string;
	"user.validation.EMAIL_EXISTS": string;
	"validation.validation.NOT_EMPTY": string;
	"validation.validation.INVALID_EMAIL": string;
	"validation.validation.INVALID_BOOLEAN": string;
	"validation.validation.MIN": string;
	"validation.validation.MAX": string;
	"validation.validation.INVALID_PHONE": string;
	"validation.validation.INVALID_DATE": string;
	"validation.validation.PASSWORD_MISMATCH": string;
	"validation.validation.INVALID_URL": string;
	"validation.validation.EMAIL_EXISTS": string;
	"telegram.telegram.welcome": string;
	"telegram.telegram.welcome_back": string;
	"telegram.telegram.messageReceived": string;
	"telegram.telegram.taksi": string;
	"telegram.telegram.registration": string;
	"telegram.messages.received": string;
	"telegram.menu.order_taxi": string;
	"telegram.menu.register": string;
	"telegram.menu.request_ride": string;
	"telegram.menu.back": string;
	"telegram.menu.select_option": string;
	"telegram.errors.general": string;
	"telegram.errors.start_command": string;
	"telegram.registration.already_registered": string;
	"telegram.registration.request_phone": string;
	"telegram.registration.share_phone": string;
	"telegram.registration.invalid_phone": string;
	"telegram.registration.verification_code_sent": string;
	"telegram.registration.demo_code": string;
	"telegram.registration.enter_code": string;
	"telegram.registration.invalid_code": string;
	"telegram.registration.success": string;
	"telegram.registration.error": string;
	"telegram.taxi.start": string;
	"telegram.taxi.share_location": string;
	"telegram.taxi.pickup_location": string;
	"telegram.taxi.destination": string;
	"telegram.taxi.vehicle_type": string;
	"telegram.taxi.payment_method": string;
	"telegram.taxi.notes": string;
	"telegram.taxi.summary": string;
	"telegram.taxi.confirm": string;
	"telegram.taxi.cancel": string;
	"telegram.taxi.confirmed": string;
	"telegram.taxi.cancelled": string;
	"telegram.taxi.error": string;
	"telegram.help.title": string;
	"telegram.help.commands": string;
	"registration.already_registered": string;
	"registration.request_phone": string;
	"registration.share_phone": string;
	"registration.invalid_phone": string;
	"registration.verification_code_sent": string;
	"registration.demo_code": string;
	"registration.enter_code": string;
	"registration.invalid_code": string;
	"registration.success": string;
	"registration.error": string;
}

export interface I18nNamespace {
	call: {
		status: {
			new: string;
			assigned: string;
			answered: string;
			completed: string;
			unanswered: string;
			served: string;
			unserved: string;
			canceled: string;
		};
		errors: {
			CALL_CREATE_FAILED: string;
			CALL_NOT_FOUND: string;
			CALL_UPDATE_FAILED: string;
			CALL_DELETE_FAILED: string;
			RECORD_ADD_FAILED: string;
			RECORD_UPDATE_FAILED: string;
		};
		messages: {
			CALL_CREATED: string;
			CALL_UPDATED: string;
			CALL_DELETED: string;
		};
		warnings: {
			CALL_NOT_FOUND: string;
		};
		validation: {
			FIELD_REQUIRED: string;
		};
	};
	callback: {
		service: {
			initialized: string;
			initiating: string;
			invalid_number: string;
			published: string;
			success: string;
			publish_failed: string;
		};
		errors: {
			invalid_phone_number: string;
			publish_failed: string;
		};
	};
	common: {
		test: {
			HELLO: string;
		};
		errors: {
			NOT_FOUND: string;
			UNAUTHORIZED: string;
			VALIDATION: string;
		};
	};
	email: {
		email: {
			service: {
				initialized: string;
				email_sent: string;
				send_failed: string;
				unexpected_error: string;
			};
			validation: {
				invalid_input: string;
			};
		};
	};
	messageVehicle: {
		vehicleAtDestination: string;
		noVehicle: string;
		vehicleAssigned: string;
		vehicleUnassigned: string;
		vehicleUpdated: string;
		vehicleCanceled: string;
		custom: {};
	};
	operator: {
		operator: {
			status: {
				active: string;
				inactive: string;
				suspended: string;
			};
			validation: {
				name_required: string;
				name_string: string;
				name_min_length: string;
				password_required: string;
				password_min_length: string;
				password_complexity: string;
				invalid_data: string;
			};
			errors: {
				load_failed: string;
				create_failed: string;
				not_found: string;
				update_failed: string;
				delete_failed: string;
				translation_failed: string;
				find_failed: string;
				status_invalid: string;
				database_error: string;
				auth_error: string;
				validation_error: string;
			};
		};
	};
	order_state: {
		order_errors: {
			ORDER_UPDATE_FAILED: string;
			ORDER_CREATE_FAILED: string;
			ORDER_FETCH_FAILED: string;
			ORDERS_FETCH_FAILED: string;
			ORDER_REMOVE_FAILED: string;
		};
		order_status: {
			STATUS_PENDING: string;
			STATUS_PROCESSING: string;
			STATUS_COMPLETED: string;
			STATUS_CANCELED: string;
		};
		validation: {
			PRODUCT_ID_INVALID: string;
			QUANTITY_MIN: string;
			QUANTITY_MAX: string;
			TOTAL_INVALID: string;
			USER_ID_REQUIRED: string;
		};
	};
	rabbitmq: {
		rabbitmq: {
			connection: {
				retry: string;
				retry_delay: string;
				connected: string;
				connection_failed: string;
			};
			processor: {
				received_message: string;
				send_error: string;
			};
		};
	};
	redis: {
		config: {
			created: string;
		};
		service: {
			initialized: string;
			getting_key: string;
			retrieved_value: string;
			get_error: string;
			setting_key: string;
			set_success: string;
			set_error: string;
			deleting_key: string;
			delete_success: string;
			delete_error: string;
			setting_expire: string;
			expire_success: string;
			expire_error: string;
		};
	};
	schedule: {
		create: {
			success: string;
			error: string;
			validation: {
				title_required: string;
				start_time_invalid: string;
				end_time_after_start: string;
			};
		};
		fetch: {
			all: {
				success: string;
				error: string;
			};
			one: {
				success: string;
				error: string;
				not_found: string;
			};
		};
		update: {
			success: string;
			error: string;
			unauthorized: string;
		};
		remove: {
			success: string;
			error: string;
			confirmation: string;
		};
		validation: {
			title_length: string;
			location_length: string;
			invalid_status: string;
		};
	};
	settings: {
		settings: {
			errors: {
				initialization_failed: string;
				invalid_settings: string;
			};
			messages: {
				initialized: string;
				updated: string;
			};
		};
	};
	taxi: {
		settings: {
			initialized: string;
		};
	};
	tcpsms: {
		errors: {
			missing_parameters: string;
			publish_failed: string;
			send_failed: string;
			log_failed: string;
			log_write_failed: string;
		};
		controller: {
			missing_fields: string;
			message_sent: string;
			message_failed: string;
			send_error: string;
		};
		message: {
			published: string;
		};
		error: {
			log_format: string;
			details: string;
		};
	};
	test: {
		test: {
			HELLO: string;
			connection: {
				retry: string;
				retry_delay: string;
				connected: string;
				connection_failed: string;
			};
			processor: {
				received_message: string;
				send_error: string;
			};
		};
	};
	user: {
		service: {
			user_created: string;
			user_exists: string;
			find_all: string;
			user_found: string;
			user_not_found: string;
			update_error: string;
			user_updated: string;
			user_removed: string;
			remove_failed: string;
		};
		validation: {
			NOT_EMPTY: string;
			INVALID_EMAIL: string;
			MIN_LENGTH: string;
			EMAIL_EXISTS: string;
		};
	};
	validation: {
		validation: {
			NOT_EMPTY: string;
			INVALID_EMAIL: string;
			INVALID_BOOLEAN: string;
			MIN: string;
			MAX: string;
			INVALID_PHONE: string;
			INVALID_DATE: string;
			PASSWORD_MISMATCH: string;
			INVALID_URL: string;
			EMAIL_EXISTS: string;
		};
	};
	telegram: {
		telegram: {
			welcome: string;
			welcome_back: string;
			messageReceived: string;
			taksi: string;
			registration: string;
		};
		messages: {
			received: string;
		};
		menu: {
			order_taxi: string;
			register: string;
			request_ride: string;
			back: string;
			select_option: string;
		};
		errors: {
			general: string;
			start_command: string;
		};
		registration: {
			already_registered: string;
			request_phone: string;
			share_phone: string;
			invalid_phone: string;
			verification_code_sent: string;
			demo_code: string;
			enter_code: string;
			invalid_code: string;
			success: string;
			error: string;
		};
		taxi: {
			start: string;
			share_location: string;
			pickup_location: string;
			destination: string;
			vehicle_type: string;
			payment_method: string;
			notes: string;
			summary: string;
			confirm: string;
			cancel: string;
			confirmed: string;
			cancelled: string;
			error: string;
		};
		help: {
			title: string;
			commands: string;
		};
	};
	registration: {
		already_registered: string;
		request_phone: string;
		share_phone: string;
		invalid_phone: string;
		verification_code_sent: string;
		demo_code: string;
		enter_code: string;
		invalid_code: string;
		success: string;
		error: string;
	};
}

export const TRANSLATION_KEYS = {
	call_status_new: "call.status.new",
	call_status_assigned: "call.status.assigned",
	call_status_answered: "call.status.answered",
	call_status_completed: "call.status.completed",
	call_status_unanswered: "call.status.unanswered",
	call_status_served: "call.status.served",
	call_status_unserved: "call.status.unserved",
	call_status_canceled: "call.status.canceled",
	call_errors_CALL_CREATE_FAILED: "call.errors.CALL_CREATE_FAILED",
	call_errors_CALL_NOT_FOUND: "call.errors.CALL_NOT_FOUND",
	call_errors_CALL_UPDATE_FAILED: "call.errors.CALL_UPDATE_FAILED",
	call_errors_CALL_DELETE_FAILED: "call.errors.CALL_DELETE_FAILED",
	call_errors_RECORD_ADD_FAILED: "call.errors.RECORD_ADD_FAILED",
	call_errors_RECORD_UPDATE_FAILED: "call.errors.RECORD_UPDATE_FAILED",
	call_messages_CALL_CREATED: "call.messages.CALL_CREATED",
	call_messages_CALL_UPDATED: "call.messages.CALL_UPDATED",
	call_messages_CALL_DELETED: "call.messages.CALL_DELETED",
	call_warnings_CALL_NOT_FOUND: "call.warnings.CALL_NOT_FOUND",
	call_validation_FIELD_REQUIRED: "call.validation.FIELD_REQUIRED",
	callback_service_initialized: "callback.service.initialized",
	callback_service_initiating: "callback.service.initiating",
	callback_service_invalid_number: "callback.service.invalid_number",
	callback_service_published: "callback.service.published",
	callback_service_success: "callback.service.success",
	callback_service_publish_failed: "callback.service.publish_failed",
	callback_errors_invalid_phone_number: "callback.errors.invalid_phone_number",
	callback_errors_publish_failed: "callback.errors.publish_failed",
	common_test_HELLO: "common.test.HELLO",
	common_errors_NOT_FOUND: "common.errors.NOT_FOUND",
	common_errors_UNAUTHORIZED: "common.errors.UNAUTHORIZED",
	common_errors_VALIDATION: "common.errors.VALIDATION",
	email_email_service_initialized: "email.email.service.initialized",
	email_email_service_email_sent: "email.email.service.email_sent",
	email_email_service_send_failed: "email.email.service.send_failed",
	email_email_service_unexpected_error: "email.email.service.unexpected_error",
	email_email_validation_invalid_input: "email.email.validation.invalid_input",
	messageVehicle_vehicleAtDestination: "messageVehicle.vehicleAtDestination",
	messageVehicle_noVehicle: "messageVehicle.noVehicle",
	messageVehicle_vehicleAssigned: "messageVehicle.vehicleAssigned",
	messageVehicle_vehicleUnassigned: "messageVehicle.vehicleUnassigned",
	messageVehicle_vehicleUpdated: "messageVehicle.vehicleUpdated",
	messageVehicle_vehicleCanceled: "messageVehicle.vehicleCanceled",
	operator_operator_status_active: "operator.operator.status.active",
	operator_operator_status_inactive: "operator.operator.status.inactive",
	operator_operator_status_suspended: "operator.operator.status.suspended",
	operator_operator_validation_name_required:
		"operator.operator.validation.name_required",
	operator_operator_validation_name_string:
		"operator.operator.validation.name_string",
	operator_operator_validation_name_min_length:
		"operator.operator.validation.name_min_length",
	operator_operator_validation_password_required:
		"operator.operator.validation.password_required",
	operator_operator_validation_password_min_length:
		"operator.operator.validation.password_min_length",
	operator_operator_validation_password_complexity:
		"operator.operator.validation.password_complexity",
	operator_operator_validation_invalid_data:
		"operator.operator.validation.invalid_data",
	operator_operator_errors_load_failed: "operator.operator.errors.load_failed",
	operator_operator_errors_create_failed:
		"operator.operator.errors.create_failed",
	operator_operator_errors_not_found: "operator.operator.errors.not_found",
	operator_operator_errors_update_failed:
		"operator.operator.errors.update_failed",
	operator_operator_errors_delete_failed:
		"operator.operator.errors.delete_failed",
	operator_operator_errors_translation_failed:
		"operator.operator.errors.translation_failed",
	operator_operator_errors_find_failed: "operator.operator.errors.find_failed",
	operator_operator_errors_status_invalid:
		"operator.operator.errors.status_invalid",
	operator_operator_errors_database_error:
		"operator.operator.errors.database_error",
	operator_operator_errors_auth_error: "operator.operator.errors.auth_error",
	operator_operator_errors_validation_error:
		"operator.operator.errors.validation_error",
	order_state_order_errors_ORDER_UPDATE_FAILED:
		"order_state.order_errors.ORDER_UPDATE_FAILED",
	order_state_order_errors_ORDER_CREATE_FAILED:
		"order_state.order_errors.ORDER_CREATE_FAILED",
	order_state_order_errors_ORDER_FETCH_FAILED:
		"order_state.order_errors.ORDER_FETCH_FAILED",
	order_state_order_errors_ORDERS_FETCH_FAILED:
		"order_state.order_errors.ORDERS_FETCH_FAILED",
	order_state_order_errors_ORDER_REMOVE_FAILED:
		"order_state.order_errors.ORDER_REMOVE_FAILED",
	order_state_order_status_STATUS_PENDING:
		"order_state.order_status.STATUS_PENDING",
	order_state_order_status_STATUS_PROCESSING:
		"order_state.order_status.STATUS_PROCESSING",
	order_state_order_status_STATUS_COMPLETED:
		"order_state.order_status.STATUS_COMPLETED",
	order_state_order_status_STATUS_CANCELED:
		"order_state.order_status.STATUS_CANCELED",
	order_state_validation_PRODUCT_ID_INVALID:
		"order_state.validation.PRODUCT_ID_INVALID",
	order_state_validation_QUANTITY_MIN: "order_state.validation.QUANTITY_MIN",
	order_state_validation_QUANTITY_MAX: "order_state.validation.QUANTITY_MAX",
	order_state_validation_TOTAL_INVALID: "order_state.validation.TOTAL_INVALID",
	order_state_validation_USER_ID_REQUIRED:
		"order_state.validation.USER_ID_REQUIRED",
	rabbitmq_rabbitmq_connection_retry: "rabbitmq.rabbitmq.connection.retry",
	rabbitmq_rabbitmq_connection_retry_delay:
		"rabbitmq.rabbitmq.connection.retry_delay",
	rabbitmq_rabbitmq_connection_connected:
		"rabbitmq.rabbitmq.connection.connected",
	rabbitmq_rabbitmq_connection_connection_failed:
		"rabbitmq.rabbitmq.connection.connection_failed",
	rabbitmq_rabbitmq_processor_received_message:
		"rabbitmq.rabbitmq.processor.received_message",
	rabbitmq_rabbitmq_processor_send_error:
		"rabbitmq.rabbitmq.processor.send_error",
	redis_config_created: "redis.config.created",
	redis_service_initialized: "redis.service.initialized",
	redis_service_getting_key: "redis.service.getting_key",
	redis_service_retrieved_value: "redis.service.retrieved_value",
	redis_service_get_error: "redis.service.get_error",
	redis_service_setting_key: "redis.service.setting_key",
	redis_service_set_success: "redis.service.set_success",
	redis_service_set_error: "redis.service.set_error",
	redis_service_deleting_key: "redis.service.deleting_key",
	redis_service_delete_success: "redis.service.delete_success",
	redis_service_delete_error: "redis.service.delete_error",
	redis_service_setting_expire: "redis.service.setting_expire",
	redis_service_expire_success: "redis.service.expire_success",
	redis_service_expire_error: "redis.service.expire_error",
	schedule_create_success: "schedule.create.success",
	schedule_create_error: "schedule.create.error",
	schedule_create_validation_title_required:
		"schedule.create.validation.title_required",
	schedule_create_validation_start_time_invalid:
		"schedule.create.validation.start_time_invalid",
	schedule_create_validation_end_time_after_start:
		"schedule.create.validation.end_time_after_start",
	schedule_fetch_all_success: "schedule.fetch.all.success",
	schedule_fetch_all_error: "schedule.fetch.all.error",
	schedule_fetch_one_success: "schedule.fetch.one.success",
	schedule_fetch_one_error: "schedule.fetch.one.error",
	schedule_fetch_one_not_found: "schedule.fetch.one.not_found",
	schedule_update_success: "schedule.update.success",
	schedule_update_error: "schedule.update.error",
	schedule_update_unauthorized: "schedule.update.unauthorized",
	schedule_remove_success: "schedule.remove.success",
	schedule_remove_error: "schedule.remove.error",
	schedule_remove_confirmation: "schedule.remove.confirmation",
	schedule_validation_title_length: "schedule.validation.title_length",
	schedule_validation_location_length: "schedule.validation.location_length",
	schedule_validation_invalid_status: "schedule.validation.invalid_status",
	settings_settings_errors_initialization_failed:
		"settings.settings.errors.initialization_failed",
	settings_settings_errors_invalid_settings:
		"settings.settings.errors.invalid_settings",
	settings_settings_messages_initialized:
		"settings.settings.messages.initialized",
	settings_settings_messages_updated: "settings.settings.messages.updated",
	taxi_settings_initialized: "taxi.settings.initialized",
	tcpsms_errors_missing_parameters: "tcpsms.errors.missing_parameters",
	tcpsms_errors_publish_failed: "tcpsms.errors.publish_failed",
	tcpsms_errors_send_failed: "tcpsms.errors.send_failed",
	tcpsms_errors_log_failed: "tcpsms.errors.log_failed",
	tcpsms_errors_log_write_failed: "tcpsms.errors.log_write_failed",
	tcpsms_controller_missing_fields: "tcpsms.controller.missing_fields",
	tcpsms_controller_message_sent: "tcpsms.controller.message_sent",
	tcpsms_controller_message_failed: "tcpsms.controller.message_failed",
	tcpsms_controller_send_error: "tcpsms.controller.send_error",
	tcpsms_message_published: "tcpsms.message.published",
	tcpsms_error_log_format: "tcpsms.error.log_format",
	tcpsms_error_details: "tcpsms.error.details",
	test_test_HELLO: "test.test.HELLO",
	test_test_connection_retry: "test.test.connection.retry",
	test_test_connection_retry_delay: "test.test.connection.retry_delay",
	test_test_connection_connected: "test.test.connection.connected",
	test_test_connection_connection_failed:
		"test.test.connection.connection_failed",
	test_test_processor_received_message: "test.test.processor.received_message",
	test_test_processor_send_error: "test.test.processor.send_error",
	user_service_user_created: "user.service.user_created",
	user_service_user_exists: "user.service.user_exists",
	user_service_find_all: "user.service.find_all",
	user_service_user_found: "user.service.user_found",
	user_service_user_not_found: "user.service.user_not_found",
	user_service_update_error: "user.service.update_error",
	user_service_user_updated: "user.service.user_updated",
	user_service_user_removed: "user.service.user_removed",
	user_service_remove_failed: "user.service.remove_failed",
	user_validation_NOT_EMPTY: "user.validation.NOT_EMPTY",
	user_validation_INVALID_EMAIL: "user.validation.INVALID_EMAIL",
	user_validation_MIN_LENGTH: "user.validation.MIN_LENGTH",
	user_validation_EMAIL_EXISTS: "user.validation.EMAIL_EXISTS",
	validation_validation_NOT_EMPTY: "validation.validation.NOT_EMPTY",
	validation_validation_INVALID_EMAIL: "validation.validation.INVALID_EMAIL",
	validation_validation_INVALID_BOOLEAN:
		"validation.validation.INVALID_BOOLEAN",
	validation_validation_MIN: "validation.validation.MIN",
	validation_validation_MAX: "validation.validation.MAX",
	validation_validation_INVALID_PHONE: "validation.validation.INVALID_PHONE",
	validation_validation_INVALID_DATE: "validation.validation.INVALID_DATE",
	validation_validation_PASSWORD_MISMATCH:
		"validation.validation.PASSWORD_MISMATCH",
	validation_validation_INVALID_URL: "validation.validation.INVALID_URL",
	validation_validation_EMAIL_EXISTS: "validation.validation.EMAIL_EXISTS",
	telegram_telegram_welcome: "telegram.telegram.welcome",
	telegram_telegram_welcome_back: "telegram.telegram.welcome_back",
	telegram_telegram_messageReceived: "telegram.telegram.messageReceived",
	telegram_telegram_taksi: "telegram.telegram.taksi",
	telegram_telegram_registration: "telegram.telegram.registration",
	telegram_messages_received: "telegram.messages.received",
	telegram_menu_order_taxi: "telegram.menu.order_taxi",
	telegram_menu_register: "telegram.menu.register",
	telegram_menu_request_ride: "telegram.menu.request_ride",
	telegram_menu_back: "telegram.menu.back",
	telegram_menu_select_option: "telegram.menu.select_option",
	telegram_errors_general: "telegram.errors.general",
	telegram_errors_start_command: "telegram.errors.start_command",
	telegram_registration_already_registered:
		"telegram.registration.already_registered",
	telegram_registration_request_phone: "telegram.registration.request_phone",
	telegram_registration_share_phone: "telegram.registration.share_phone",
	telegram_registration_invalid_phone: "telegram.registration.invalid_phone",
	telegram_registration_verification_code_sent:
		"telegram.registration.verification_code_sent",
	telegram_registration_demo_code: "telegram.registration.demo_code",
	telegram_registration_enter_code: "telegram.registration.enter_code",
	telegram_registration_invalid_code: "telegram.registration.invalid_code",
	telegram_registration_success: "telegram.registration.success",
	telegram_registration_error: "telegram.registration.error",
	telegram_taxi_start: "telegram.taxi.start",
	telegram_taxi_share_location: "telegram.taxi.share_location",
	telegram_taxi_pickup_location: "telegram.taxi.pickup_location",
	telegram_taxi_destination: "telegram.taxi.destination",
	telegram_taxi_vehicle_type: "telegram.taxi.vehicle_type",
	telegram_taxi_payment_method: "telegram.taxi.payment_method",
	telegram_taxi_notes: "telegram.taxi.notes",
	telegram_taxi_summary: "telegram.taxi.summary",
	telegram_taxi_confirm: "telegram.taxi.confirm",
	telegram_taxi_cancel: "telegram.taxi.cancel",
	telegram_taxi_confirmed: "telegram.taxi.confirmed",
	telegram_taxi_cancelled: "telegram.taxi.cancelled",
	telegram_taxi_error: "telegram.taxi.error",
	telegram_help_title: "telegram.help.title",
	telegram_help_commands: "telegram.help.commands",
	registration_already_registered: "registration.already_registered",
	registration_request_phone: "registration.request_phone",
	registration_share_phone: "registration.share_phone",
	registration_invalid_phone: "registration.invalid_phone",
	registration_verification_code_sent: "registration.verification_code_sent",
	registration_demo_code: "registration.demo_code",
	registration_enter_code: "registration.enter_code",
	registration_invalid_code: "registration.invalid_code",
	registration_success: "registration.success",
	registration_error: "registration.error",
} as const;
