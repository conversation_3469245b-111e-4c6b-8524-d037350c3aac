{"id": "b4a1bad4-695a-4948-b9c2-19a619a70af6", "prevId": "e8c71228-51e6-4e67-a86d-745dcde04df1", "version": "7", "dialect": "postgresql", "tables": {"public.addresses": {"name": "addresses", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": false}, "street_line_1": {"name": "street_line_1", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "street_line_2": {"name": "street_line_2", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "state_province": {"name": "state_province", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "postal_code": {"name": "postal_code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "country_code": {"name": "country_code", "type": "<PERSON><PERSON><PERSON>(2)", "primaryKey": false, "notNull": true}, "formatted_address": {"name": "formatted_address", "type": "text", "primaryKey": false, "notNull": false}, "raw_input_address": {"name": "raw_input_address", "type": "text", "primaryKey": false, "notNull": false}, "geom": {"name": "geom", "type": "geometry(point)", "primaryKey": false, "notNull": false}, "latitude": {"name": "latitude", "type": "double precision", "primaryKey": false, "notNull": false}, "longitude": {"name": "longitude", "type": "double precision", "primaryKey": false, "notNull": false}, "geocoding_provider": {"name": "geocoding_provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "geocoding_accuracy": {"name": "geocoding_accuracy", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "osm_place_id": {"name": "osm_place_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"addresses_tenant_id_idx": {"name": "addresses_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "addresses_postal_code_country_idx": {"name": "addresses_postal_code_country_idx", "columns": [{"expression": "postal_code", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "country_code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "addresses_geom_idx": {"name": "addresses_geom_idx", "columns": [{"expression": "geom", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"addresses\".\"geom\" IS NOT NULL", "concurrently": false, "method": "gist", "with": {}}}, "foreignKeys": {"addresses_tenant_id_tenants_id_fk": {"name": "addresses_tenant_id_tenants_id_fk", "tableFrom": "addresses", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.areas": {"name": "areas", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "geom": {"name": "geom", "type": "geometry(point)", "primaryKey": false, "notNull": true}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(2)", "primaryKey": false, "notNull": false}, "osm_tags": {"name": "osm_tags", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"areas_tenant_id_idx": {"name": "areas_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "areas_geom_idx": {"name": "areas_geom_idx", "columns": [{"expression": "geom", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "gist", "with": {}}, "areas_deleted_at_idx": {"name": "areas_deleted_at_idx", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"areas\".\"deleted_at\" IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"areas_tenant_id_tenants_id_fk": {"name": "areas_tenant_id_tenants_id_fk", "tableFrom": "areas", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.audit_logs": {"name": "audit_logs", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "actor_id": {"name": "actor_id", "type": "uuid", "primaryKey": false, "notNull": false}, "actor_type": {"name": "actor_type", "type": "actors_actor_type_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": false}, "event_type": {"name": "event_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "target_table": {"name": "target_table", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "target_id": {"name": "target_id", "type": "uuid", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "details": {"name": "details", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"audit_logs_tenant_id_idx": {"name": "audit_logs_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "audit_logs_actor_id_actor_type_idx": {"name": "audit_logs_actor_id_actor_type_idx", "columns": [{"expression": "actor_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "actor_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "audit_logs_event_type_idx": {"name": "audit_logs_event_type_idx", "columns": [{"expression": "event_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "audit_logs_target_table_target_id_idx": {"name": "audit_logs_target_table_target_id_idx", "columns": [{"expression": "target_table", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "target_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "audit_logs_created_at_idx": {"name": "audit_logs_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "audit_logs_deleted_at_idx": {"name": "audit_logs_deleted_at_idx", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"audit_logs\".\"deleted_at\" IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"audit_logs_tenant_id_tenants_id_fk": {"name": "audit_logs_tenant_id_tenants_id_fk", "tableFrom": "audit_logs", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.bots": {"name": "bots", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "text", "primaryKey": false, "notNull": true}, "webhook_path_segment": {"name": "webhook_path_segment", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "bot_username": {"name": "bot_username", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_enabled": {"name": "is_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "config": {"name": "config", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"bots_webhook_path_segment_idx": {"name": "bots_webhook_path_segment_idx", "columns": [{"expression": "webhook_path_segment", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"bots_tenant_id_tenants_id_fk": {"name": "bots_tenant_id_tenants_id_fk", "tableFrom": "bots", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"bots_webhook_path_segment_unique": {"name": "bots_webhook_path_segment_unique", "nullsNotDistinct": false, "columns": ["webhook_path_segment"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chat": {"name": "chat", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chat_tenant_id_tenants_id_fk": {"name": "chat_tenant_id_tenants_id_fk", "tableFrom": "chat", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chatbot_config": {"name": "chatbot_config", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "uuid", "primaryKey": false, "notNull": true}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chatbot_config_tenant_id_tenants_id_fk": {"name": "chatbot_config_tenant_id_tenants_id_fk", "tableFrom": "chatbot_config", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "chatbot_config_provider_id_chatbot_provider_id_fk": {"name": "chatbot_config_provider_id_chatbot_provider_id_fk", "tableFrom": "chatbot_config", "tableTo": "chatbot_provider", "columnsFrom": ["provider_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chatbot_events": {"name": "chatbot_events", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "chatbot_instance_id": {"name": "chatbot_instance_id", "type": "uuid", "primaryKey": false, "notNull": true}, "event_type": {"name": "event_type", "type": "events_event_type_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "session_id": {"name": "session_id", "type": "uuid", "primaryKey": false, "notNull": false}, "message_id": {"name": "message_id", "type": "uuid", "primaryKey": false, "notNull": false}, "details": {"name": "details", "type": "jsonb", "primaryKey": false, "notNull": true}, "occurred_at": {"name": "occurred_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"chatbot_events_chatbot_instance_id_idx": {"name": "chatbot_events_chatbot_instance_id_idx", "columns": [{"expression": "chatbot_instance_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "chatbot_events_user_id_idx": {"name": "chatbot_events_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "chatbot_events_session_id_idx": {"name": "chatbot_events_session_id_idx", "columns": [{"expression": "session_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "chatbot_events_message_id_idx": {"name": "chatbot_events_message_id_idx", "columns": [{"expression": "message_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "chatbot_events_event_type_idx": {"name": "chatbot_events_event_type_idx", "columns": [{"expression": "event_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "chatbot_events_occurred_at_idx": {"name": "chatbot_events_occurred_at_idx", "columns": [{"expression": "occurred_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "chatbot_events_deleted_at_idx": {"name": "chatbot_events_deleted_at_idx", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"chatbot_events\".\"deleted_at\" IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"chatbot_events_chatbot_instance_id_chatbot_instance_id_fk": {"name": "chatbot_events_chatbot_instance_id_chatbot_instance_id_fk", "tableFrom": "chatbot_events", "tableTo": "chatbot_instance", "columnsFrom": ["chatbot_instance_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "chatbot_events_user_id_users_id_fk": {"name": "chatbot_events_user_id_users_id_fk", "tableFrom": "chatbot_events", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "chatbot_events_session_id_chatbot_session_id_fk": {"name": "chatbot_events_session_id_chatbot_session_id_fk", "tableFrom": "chatbot_events", "tableTo": "chatbot_session", "columnsFrom": ["session_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "chatbot_events_message_id_chatbot_message_id_fk": {"name": "chatbot_events_message_id_chatbot_message_id_fk", "tableFrom": "chatbot_events", "tableTo": "chatbot_message", "columnsFrom": ["message_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chatbot_instance": {"name": "chatbot_instance", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "bot_username": {"name": "bot_username", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "webhook_url": {"name": "webhook_url", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "config": {"name": "config", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"chatbot_instance_tenant_id_tenants_id_fk": {"name": "chatbot_instance_tenant_id_tenants_id_fk", "tableFrom": "chatbot_instance", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "chatbot_instance_provider_id_chatbot_provider_id_fk": {"name": "chatbot_instance_provider_id_chatbot_provider_id_fk", "tableFrom": "chatbot_instance", "tableTo": "chatbot_provider", "columnsFrom": ["provider_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chatbot_message": {"name": "chatbot_message", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "chatbot_session_id": {"name": "chatbot_session_id", "type": "uuid", "primaryKey": false, "notNull": true}, "chatbot_user_id": {"name": "chatbot_user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "direction": {"name": "direction", "type": "message_direction", "typeSchema": "public", "primaryKey": false, "notNull": true}, "sent_at": {"name": "sent_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "message_type": {"name": "message_type", "type": "message_type_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "phone_verified": {"name": "phone_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "verification_reference": {"name": "verification_reference", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "replied_to_id": {"name": "replied_to_id", "type": "uuid", "primaryKey": false, "notNull": false}, "error_flag": {"name": "error_flag", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "provider_message_id": {"name": "provider_message_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"chatbot_message_session_id_idx": {"name": "chatbot_message_session_id_idx", "columns": [{"expression": "chatbot_session_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "chatbot_message_user_id_idx": {"name": "chatbot_message_user_id_idx", "columns": [{"expression": "chatbot_user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "chatbot_message_sent_at_idx": {"name": "chatbot_message_sent_at_idx", "columns": [{"expression": "sent_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"chatbot_message_chatbot_session_id_chatbot_session_id_fk": {"name": "chatbot_message_chatbot_session_id_chatbot_session_id_fk", "tableFrom": "chatbot_message", "tableTo": "chatbot_session", "columnsFrom": ["chatbot_session_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "chatbot_message_chatbot_user_id_chatbot_users_id_fk": {"name": "chatbot_message_chatbot_user_id_chatbot_users_id_fk", "tableFrom": "chatbot_message", "tableTo": "chatbot_users", "columnsFrom": ["chatbot_user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chatbot_message_with_geolocation": {"name": "chatbot_message_with_geolocation", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "message_id": {"name": "message_id", "type": "uuid", "primaryKey": false, "notNull": true}, "location": {"name": "location", "type": "geometry(point)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"chatbot_message_geolocation_idx": {"name": "chatbot_message_geolocation_idx", "columns": [{"expression": "location", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "gist", "with": {}}}, "foreignKeys": {"chatbot_message_with_geolocation_message_id_chatbot_message_id_fk": {"name": "chatbot_message_with_geolocation_message_id_chatbot_message_id_fk", "tableFrom": "chatbot_message_with_geolocation", "tableTo": "chatbot_message", "columnsFrom": ["message_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chatbot_provider": {"name": "chatbot_provider", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "logo_url": {"name": "logo_url", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "features": {"name": "features", "type": "jsonb", "primaryKey": false, "notNull": false}, "enabled": {"name": "enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chatbot_provider_tenant_id_tenants_id_fk": {"name": "chatbot_provider_tenant_id_tenants_id_fk", "tableFrom": "chatbot_provider", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chatbot_session": {"name": "chatbot_session", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "chatbot_user_id": {"name": "chatbot_user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "ended_at": {"name": "ended_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "verified": {"name": "verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "geolocation": {"name": "geolocation", "type": "geometry(point)", "primaryKey": false, "notNull": false}, "accuracy": {"name": "accuracy", "type": "double precision", "primaryKey": false, "notNull": false}, "context": {"name": "context", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"chatbot_session_chatbot_user_id_idx": {"name": "chatbot_session_chatbot_user_id_idx", "columns": [{"expression": "chatbot_user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "chatbot_session_tenant_id_idx": {"name": "chatbot_session_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "chatbot_session_geolocation_idx": {"name": "chatbot_session_geolocation_idx", "columns": [{"expression": "geolocation", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"chatbot_session\".\"geolocation\" IS NOT NULL", "concurrently": false, "method": "gist", "with": {}}}, "foreignKeys": {"chatbot_session_chatbot_user_id_chatbot_users_id_fk": {"name": "chatbot_session_chatbot_user_id_chatbot_users_id_fk", "tableFrom": "chatbot_session", "tableTo": "chatbot_users", "columnsFrom": ["chatbot_user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "chatbot_session_tenant_id_tenants_id_fk": {"name": "chatbot_session_tenant_id_tenants_id_fk", "tableFrom": "chatbot_session", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chatbot_users": {"name": "chatbot_users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "chatbot_instance_id": {"name": "chatbot_instance_id", "type": "uuid", "primaryKey": false, "notNull": true}, "provider_user_id": {"name": "provider_user_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone_verified": {"name": "phone_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "verification_date": {"name": "verification_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "consent": {"name": "consent", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "consent_date": {"name": "consent_date", "type": "timestamp", "primaryKey": false, "notNull": false}, "consent_revoked_at": {"name": "consent_revoked_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "blocked": {"name": "blocked", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "verified_at": {"name": "verified_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "locale": {"name": "locale", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "joined_at": {"name": "joined_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "last_seen_at": {"name": "last_seen_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"provider_user_id_chatbot_instance_idx": {"name": "provider_user_id_chatbot_instance_idx", "columns": [{"expression": "provider_user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "chatbot_instance_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "where": "\"chatbot_users\".\"provider_user_id\" IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}, "user_id_chatbot_instance_idx": {"name": "user_id_chatbot_instance_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "chatbot_instance_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"chatbot_users_user_id_users_id_fk": {"name": "chatbot_users_user_id_users_id_fk", "tableFrom": "chatbot_users", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "chatbot_users_chatbot_instance_id_chatbot_instance_id_fk": {"name": "chatbot_users_chatbot_instance_id_chatbot_instance_id_fk", "tableFrom": "chatbot_users", "tableTo": "chatbot_instance", "columnsFrom": ["chatbot_instance_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.chatbot": {"name": "chatbot", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "provider_type_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "bot_id": {"name": "bot_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "config": {"name": "config", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chatbot_tenant_id_tenants_id_fk": {"name": "chatbot_tenant_id_tenants_id_fk", "tableFrom": "chatbot", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.dispatch_assignment": {"name": "dispatch_assignment", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "operator_id": {"name": "operator_id", "type": "uuid", "primaryKey": false, "notNull": true}, "ride_id": {"name": "ride_id", "type": "uuid", "primaryKey": false, "notNull": true}, "assigned_at": {"name": "assigned_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"dispatch_assignment_tenant_id_idx": {"name": "dispatch_assignment_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "dispatch_assignment_operator_ride_idx": {"name": "dispatch_assignment_operator_ride_idx", "columns": [{"expression": "operator_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "ride_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"dispatch_assignment_tenant_id_tenants_id_fk": {"name": "dispatch_assignment_tenant_id_tenants_id_fk", "tableFrom": "dispatch_assignment", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "dispatch_assignment_operator_id_operators_id_fk": {"name": "dispatch_assignment_operator_id_operators_id_fk", "tableFrom": "dispatch_assignment", "tableTo": "operators", "columnsFrom": ["operator_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "dispatch_assignment_ride_id_rides_id_fk": {"name": "dispatch_assignment_ride_id_rides_id_fk", "tableFrom": "dispatch_assignment", "tableTo": "rides", "columnsFrom": ["ride_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.driver_vehicle": {"name": "driver_vehicle", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "driver_id": {"name": "driver_id", "type": "uuid", "primaryKey": false, "notNull": true}, "vehicle_id": {"name": "vehicle_id", "type": "uuid", "primaryKey": false, "notNull": true}, "from_time": {"name": "from_time", "type": "timestamp", "primaryKey": false, "notNull": true}, "to_time": {"name": "to_time", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {"driver_vehicles_tenant_id_idx": {"name": "driver_vehicles_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"driver_vehicle_tenant_id_tenants_id_fk": {"name": "driver_vehicle_tenant_id_tenants_id_fk", "tableFrom": "driver_vehicle", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "driver_vehicle_driver_id_users_id_fk": {"name": "driver_vehicle_driver_id_users_id_fk", "tableFrom": "driver_vehicle", "tableTo": "users", "columnsFrom": ["driver_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "driver_vehicle_vehicle_id_vehicle_id_fk": {"name": "driver_vehicle_vehicle_id_vehicle_id_fk", "tableFrom": "driver_vehicle", "tableTo": "vehicle", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.geodata_entries": {"name": "geodata_entries", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": false}, "address_id": {"name": "address_id", "type": "uuid", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(2)", "primaryKey": false, "notNull": false}, "city": {"name": "city", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "geom": {"name": "geom", "type": "geometry(point)", "primaryKey": false, "notNull": true}, "source": {"name": "source", "type": "app_source_type_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "accuracy": {"name": "accuracy", "type": "double precision", "primaryKey": false, "notNull": false}, "area_id": {"name": "area_id", "type": "uuid", "primaryKey": false, "notNull": false}, "osm_tags": {"name": "osm_tags", "type": "jsonb", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"geodata_entries_tenant_id_idx": {"name": "geodata_entries_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "geodata_entries_address_id_idx": {"name": "geodata_entries_address_id_idx", "columns": [{"expression": "address_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "geodata_entries_user_id_idx": {"name": "geodata_entries_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "geodata_entries_area_id_idx": {"name": "geodata_entries_area_id_idx", "columns": [{"expression": "area_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "geodata_entries_source_idx": {"name": "geodata_entries_source_idx", "columns": [{"expression": "source", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "geodata_entries_geom_idx": {"name": "geodata_entries_geom_idx", "columns": [{"expression": "geom", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"geodata_entries\".\"geom\" IS NOT NULL", "concurrently": false, "method": "gist", "with": {}}, "geodata_entries_deleted_at_idx": {"name": "geodata_entries_deleted_at_idx", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"geodata_entries\".\"deleted_at\" IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"geodata_entries_tenant_id_tenants_id_fk": {"name": "geodata_entries_tenant_id_tenants_id_fk", "tableFrom": "geodata_entries", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "geodata_entries_address_id_addresses_id_fk": {"name": "geodata_entries_address_id_addresses_id_fk", "tableFrom": "geodata_entries", "tableTo": "addresses", "columnsFrom": ["address_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "geodata_entries_user_id_users_id_fk": {"name": "geodata_entries_user_id_users_id_fk", "tableFrom": "geodata_entries", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "geodata_entries_area_id_areas_id_fk": {"name": "geodata_entries_area_id_areas_id_fk", "tableFrom": "geodata_entries", "tableTo": "areas", "columnsFrom": ["area_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.i18n_translations": {"name": "i18n_translations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": false}, "namespace": {"name": "namespace", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "locale": {"name": "locale", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"i18n_translations_tenant_locale_namespace_key_idx": {"name": "i18n_translations_tenant_locale_namespace_key_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "locale", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "namespace", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "i18n_translations_tenant_id_idx": {"name": "i18n_translations_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "i18n_translations_locale_idx": {"name": "i18n_translations_locale_idx", "columns": [{"expression": "locale", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "i18n_translations_key_idx": {"name": "i18n_translations_key_idx", "columns": [{"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "i18n_translations_deleted_at_idx": {"name": "i18n_translations_deleted_at_idx", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"i18n_translations\".\"deleted_at\" IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"i18n_translations_tenant_id_tenants_id_fk": {"name": "i18n_translations_tenant_id_tenants_id_fk", "tableFrom": "i18n_translations", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.invoices": {"name": "invoices", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "invoice_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'PENDING'"}, "due_date": {"name": "due_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "issued_at": {"name": "issued_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "paid_at": {"name": "paid_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"invoices_tenant_id_due_date_idx": {"name": "invoices_tenant_id_due_date_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "due_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"invoices_tenant_id_tenants_id_fk": {"name": "invoices_tenant_id_tenants_id_fk", "tableFrom": "invoices", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.map_provider": {"name": "map_provider", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "provider_name": {"name": "provider_name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "refresh_token": {"name": "refresh_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "config": {"name": "config", "type": "json", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"map_provider_tenant_provider_idx": {"name": "map_provider_tenant_provider_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "provider_name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "map_provider_access_token_idx": {"name": "map_provider_access_token_idx", "columns": [{"expression": "access_token", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"map_provider_tenant_id_tenants_id_fk": {"name": "map_provider_tenant_id_tenants_id_fk", "tableFrom": "map_provider", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.messages": {"name": "messages", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "chat_id": {"name": "chat_id", "type": "uuid", "primaryKey": false, "notNull": true}, "from_user_id": {"name": "from_user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "to_user_id": {"name": "to_user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "provider_message_id": {"name": "provider_message_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "direction": {"name": "direction", "type": "message_direction", "typeSchema": "public", "primaryKey": false, "notNull": true}, "via_channel": {"name": "via_channel", "type": "provider_type_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "message_type": {"name": "message_type", "type": "message_type_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "phone_verified": {"name": "phone_verified", "type": "boolean", "primaryKey": false, "notNull": false}, "verification_reference": {"name": "verification_reference", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "geolocation": {"name": "geolocation", "type": "geometry(point)", "primaryKey": false, "notNull": false}, "accuracy": {"name": "accuracy", "type": "double precision", "primaryKey": false, "notNull": false}, "sms_status": {"name": "sms_status", "type": "sms_status_enum", "typeSchema": "public", "primaryKey": false, "notNull": false}, "related_pbx_call_id": {"name": "related_pbx_call_id", "type": "uuid", "primaryKey": false, "notNull": false}, "replied_to_message_id": {"name": "replied_to_message_id", "type": "uuid", "primaryKey": false, "notNull": false}, "error_flag": {"name": "error_flag", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "sent_at": {"name": "sent_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"messages_tenant_id_idx": {"name": "messages_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "messages_chat_id_idx": {"name": "messages_chat_id_idx", "columns": [{"expression": "chat_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "messages_from_user_id_idx": {"name": "messages_from_user_id_idx", "columns": [{"expression": "from_user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "messages_to_user_id_idx": {"name": "messages_to_user_id_idx", "columns": [{"expression": "to_user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "messages_sent_at_idx": {"name": "messages_sent_at_idx", "columns": [{"expression": "sent_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "messages_message_type_idx": {"name": "messages_message_type_idx", "columns": [{"expression": "message_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "messages_geolocation_idx": {"name": "messages_geolocation_idx", "columns": [{"expression": "geolocation", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"messages\".\"geolocation\" IS NOT NULL", "concurrently": false, "method": "gist", "with": {}}, "messages_via_channel_idx": {"name": "messages_via_channel_idx", "columns": [{"expression": "via_channel", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "messages_sms_status_idx": {"name": "messages_sms_status_idx", "columns": [{"expression": "sms_status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"messages\".\"sms_status\" IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}, "messages_related_pbx_call_id_idx": {"name": "messages_related_pbx_call_id_idx", "columns": [{"expression": "related_pbx_call_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"messages\".\"related_pbx_call_id\" IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}, "messages_deleted_at_idx": {"name": "messages_deleted_at_idx", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"messages\".\"deleted_at\" IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"messages_tenant_id_tenants_id_fk": {"name": "messages_tenant_id_tenants_id_fk", "tableFrom": "messages", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "messages_chat_id_chat_id_fk": {"name": "messages_chat_id_chat_id_fk", "tableFrom": "messages", "tableTo": "chat", "columnsFrom": ["chat_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "messages_from_user_id_users_id_fk": {"name": "messages_from_user_id_users_id_fk", "tableFrom": "messages", "tableTo": "users", "columnsFrom": ["from_user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "messages_to_user_id_users_id_fk": {"name": "messages_to_user_id_users_id_fk", "tableFrom": "messages", "tableTo": "users", "columnsFrom": ["to_user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "messages_related_pbx_call_id_pbx_call_id_fk": {"name": "messages_related_pbx_call_id_pbx_call_id_fk", "tableFrom": "messages", "tableTo": "pbx_call", "columnsFrom": ["related_pbx_call_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "messages_replied_to_message_id_messages_id_fk": {"name": "messages_replied_to_message_id_messages_id_fk", "tableFrom": "messages", "tableTo": "messages", "columnsFrom": ["replied_to_message_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.multi_tenant_group": {"name": "multi_tenant_group", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "groups_group_type_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "contact_email": {"name": "contact_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "contact_phone": {"name": "contact_phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "parent_group_id": {"name": "parent_group_id", "type": "uuid", "primaryKey": false, "notNull": false}, "settings": {"name": "settings", "type": "jsonb", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "groups_group_status_enum", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'PENDING'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"name_unique_per_parent": {"name": "name_unique_per_parent", "columns": [{"expression": "parent_group_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "status_idx": {"name": "status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"multi_tenant_group_parent_group_id_multi_tenant_group_id_fk": {"name": "multi_tenant_group_parent_group_id_multi_tenant_group_id_fk", "tableFrom": "multi_tenant_group", "tableTo": "multi_tenant_group", "columnsFrom": ["parent_group_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.operator_extensions": {"name": "operator_extensions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_tenant_id": {"name": "user_tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "operators_operator_status_enum", "typeSchema": "public", "primaryKey": false, "notNull": false, "default": "'ACTIVE'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"operator_extensions_tenant_id_idx": {"name": "operator_extensions_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"operator_extensions_tenant_id_tenants_id_fk": {"name": "operator_extensions_tenant_id_tenants_id_fk", "tableFrom": "operator_extensions", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "operator_extensions_user_tenant_id_user_tenant_id_fk": {"name": "operator_extensions_user_tenant_id_user_tenant_id_fk", "tableFrom": "operator_extensions", "tableTo": "user_tenant", "columnsFrom": ["user_tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.operator_shift": {"name": "operator_shift", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "operator_id": {"name": "operator_id", "type": "uuid", "primaryKey": false, "notNull": true}, "shift_start": {"name": "shift_start", "type": "timestamp", "primaryKey": false, "notNull": true}, "shift_end": {"name": "shift_end", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"operator_shift_tenant_id_tenants_id_fk": {"name": "operator_shift_tenant_id_tenants_id_fk", "tableFrom": "operator_shift", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "operator_shift_operator_id_operators_id_fk": {"name": "operator_shift_operator_id_operators_id_fk", "tableFrom": "operator_shift", "tableTo": "operators", "columnsFrom": ["operator_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.operator_performance_stats": {"name": "operator_performance_stats", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "operator_user_id": {"name": "operator_user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "shift_id": {"name": "shift_id", "type": "uuid", "primaryKey": false, "notNull": false}, "answered_calls": {"name": "answered_calls", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "missed_calls": {"name": "missed_calls", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "outbound_calls": {"name": "outbound_calls", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "total_call_duration_seconds": {"name": "total_call_duration_seconds", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "avg_handle_time_seconds": {"name": "avg_handle_time_seconds", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "rides_dispatched": {"name": "rides_dispatched", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "sms_sent": {"name": "sms_sent", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"operator_performance_stats_tenant_id_idx": {"name": "operator_performance_stats_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "operator_performance_stats_operator_user_id_idx": {"name": "operator_performance_stats_operator_user_id_idx", "columns": [{"expression": "operator_user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "operator_performance_stats_date_idx": {"name": "operator_performance_stats_date_idx", "columns": [{"expression": "date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "operator_performance_stats_shift_id_idx": {"name": "operator_performance_stats_shift_id_idx", "columns": [{"expression": "shift_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"operator_performance_stats\".\"shift_id\" IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}, "operator_performance_stats_tenant_operator_date_idx": {"name": "operator_performance_stats_tenant_operator_date_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "operator_user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"operator_performance_stats_tenant_id_tenants_id_fk": {"name": "operator_performance_stats_tenant_id_tenants_id_fk", "tableFrom": "operator_performance_stats", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "operator_performance_stats_operator_user_id_users_id_fk": {"name": "operator_performance_stats_operator_user_id_users_id_fk", "tableFrom": "operator_performance_stats", "tableTo": "users", "columnsFrom": ["operator_user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.operators": {"name": "operators", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_tenant_id": {"name": "user_tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "operators_operator_status_enum", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'ACTIVE'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"operators_tenant_id_idx": {"name": "operators_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"operators_tenant_id_tenants_id_fk": {"name": "operators_tenant_id_tenants_id_fk", "tableFrom": "operators", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "operators_user_tenant_id_user_tenant_id_fk": {"name": "operators_user_tenant_id_user_tenant_id_fk", "tableFrom": "operators", "tableTo": "user_tenant", "columnsFrom": ["user_tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payment": {"name": "payment", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "ride_id": {"name": "ride_id", "type": "uuid", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": true}, "method": {"name": "method", "type": "app_payment_method_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "processor_ref": {"name": "processor_ref", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "payment_status_enum", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'PENDING'"}, "processed_at": {"name": "processed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"payment_processor_ref_idx": {"name": "payment_processor_ref_idx", "columns": [{"expression": "processor_ref", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "payment_user_status_idx": {"name": "payment_user_status_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"payment_tenant_id_tenants_id_fk": {"name": "payment_tenant_id_tenants_id_fk", "tableFrom": "payment", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "payment_ride_id_rides_id_fk": {"name": "payment_ride_id_rides_id_fk", "tableFrom": "payment", "tableTo": "rides", "columnsFrom": ["ride_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "payment_user_id_users_id_fk": {"name": "payment_user_id_users_id_fk", "tableFrom": "payment", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pbx_call_additional_operators": {"name": "pbx_call_additional_operators", "schema": "", "columns": {"pbx_call_id": {"name": "pbx_call_id", "type": "uuid", "primaryKey": false, "notNull": true}, "operator_id": {"name": "operator_id", "type": "uuid", "primaryKey": false, "notNull": true}, "assigned_at": {"name": "assigned_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"pbx_call_additional_operators_pbx_call_id_pbx_call_id_fk": {"name": "pbx_call_additional_operators_pbx_call_id_pbx_call_id_fk", "tableFrom": "pbx_call_additional_operators", "tableTo": "pbx_call", "columnsFrom": ["pbx_call_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "pbx_call_additional_operators_operator_id_operators_id_fk": {"name": "pbx_call_additional_operators_operator_id_operators_id_fk", "tableFrom": "pbx_call_additional_operators", "tableTo": "operators", "columnsFrom": ["operator_id"], "columnsTo": ["id"], "onDelete": "restrict", "onUpdate": "no action"}}, "compositePrimaryKeys": {"pbx_call_additional_operators_pbx_call_id_operator_id_pk": {"name": "pbx_call_additional_operators_pbx_call_id_operator_id_pk", "columns": ["pbx_call_id", "operator_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.pbx_call": {"name": "pbx_call", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "operator_id": {"name": "operator_id", "type": "uuid", "primaryKey": false, "notNull": false}, "ride_id": {"name": "ride_id", "type": "uuid", "primaryKey": false, "notNull": false}, "direction": {"name": "direction", "type": "calls_direction_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "calls_status_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": false}, "recording_url": {"name": "recording_url", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "started_at": {"name": "started_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "answered_at": {"name": "answered_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "ended_at": {"name": "ended_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "assigned_at": {"name": "assigned_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "external_pbx_id": {"name": "external_pbx_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "from_phone_number": {"name": "from_phone_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "to_phone_number": {"name": "to_phone_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "caller_name": {"name": "caller_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "numeric_extension": {"name": "numeric_extension", "type": "integer", "primaryKey": false, "notNull": false}, "termination_reason": {"name": "termination_reason", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "disposition_code": {"name": "disposition_code", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "pbx_raw_details": {"name": "pbx_raw_details", "type": "jsonb", "primaryKey": false, "notNull": false}, "source": {"name": "source", "type": "call_source_enum", "typeSchema": "public", "primaryKey": false, "notNull": false}, "customer_phone_number": {"name": "customer_phone_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "location_text": {"name": "location_text", "type": "text", "primaryKey": false, "notNull": false}, "destination_text": {"name": "destination_text", "type": "text", "primaryKey": false, "notNull": false}, "area_id": {"name": "area_id", "type": "uuid", "primaryKey": false, "notNull": false}, "area_name": {"name": "area_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "eta_minutes": {"name": "eta_minutes", "type": "integer", "primaryKey": false, "notNull": false}, "gis_data": {"name": "gis_data", "type": "jsonb", "primaryKey": false, "notNull": false}, "vehicle_identifiers": {"name": "vehicle_identifiers", "type": "json", "primaryKey": false, "notNull": false}, "comment": {"name": "comment", "type": "text", "primaryKey": false, "notNull": false}, "is_locked": {"name": "is_locked", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "language_code": {"name": "language_code", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "call_metadata": {"name": "call_metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"pbx_call_tenant_id_idx": {"name": "pbx_call_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pbx_call_user_id_idx": {"name": "pbx_call_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"pbx_call\".\"user_id\" IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}, "pbx_call_operator_id_idx": {"name": "pbx_call_operator_id_idx", "columns": [{"expression": "operator_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"pbx_call\".\"operator_id\" IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}, "pbx_call_ride_id_idx": {"name": "pbx_call_ride_id_idx", "columns": [{"expression": "ride_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"pbx_call\".\"ride_id\" IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}, "pbx_call_status_idx": {"name": "pbx_call_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pbx_call_started_at_idx": {"name": "pbx_call_started_at_idx", "columns": [{"expression": "started_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "pbx_call_from_phone_number_idx": {"name": "pbx_call_from_phone_number_idx", "columns": [{"expression": "from_phone_number", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"pbx_call\".\"from_phone_number\" IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}, "pbx_call_to_phone_number_idx": {"name": "pbx_call_to_phone_number_idx", "columns": [{"expression": "to_phone_number", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"pbx_call\".\"to_phone_number\" IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}, "pbx_call_tenant_external_pbx_id_uk": {"name": "pbx_call_tenant_external_pbx_id_uk", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "external_pbx_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "where": "\"pbx_call\".\"external_pbx_id\" IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}, "pbx_call_source_idx": {"name": "pbx_call_source_idx", "columns": [{"expression": "source", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"pbx_call\".\"source\" IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}, "pbx_call_assigned_at_idx": {"name": "pbx_call_assigned_at_idx", "columns": [{"expression": "assigned_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"pbx_call\".\"assigned_at\" IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}, "pbx_call_language_code_idx": {"name": "pbx_call_language_code_idx", "columns": [{"expression": "language_code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"pbx_call\".\"language_code\" IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}, "pbx_call_customer_phone_idx": {"name": "pbx_call_customer_phone_idx", "columns": [{"expression": "customer_phone_number", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"pbx_call\".\"customer_phone_number\" IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}, "pbx_call_area_id_idx": {"name": "pbx_call_area_id_idx", "columns": [{"expression": "area_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"pbx_call\".\"area_id\" IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}, "pbx_call_numeric_extension_idx": {"name": "pbx_call_numeric_extension_idx", "columns": [{"expression": "numeric_extension", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"pbx_call\".\"numeric_extension\" IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"pbx_call_tenant_id_tenants_id_fk": {"name": "pbx_call_tenant_id_tenants_id_fk", "tableFrom": "pbx_call", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "pbx_call_user_id_users_id_fk": {"name": "pbx_call_user_id_users_id_fk", "tableFrom": "pbx_call", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "pbx_call_operator_id_operators_id_fk": {"name": "pbx_call_operator_id_operators_id_fk", "tableFrom": "pbx_call", "tableTo": "operators", "columnsFrom": ["operator_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "pbx_call_ride_id_rides_id_fk": {"name": "pbx_call_ride_id_rides_id_fk", "tableFrom": "pbx_call", "tableTo": "rides", "columnsFrom": ["ride_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "pbx_call_area_id_areas_id_fk": {"name": "pbx_call_area_id_areas_id_fk", "tableFrom": "pbx_call", "tableTo": "areas", "columnsFrom": ["area_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.promos": {"name": "promos", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "discount_value": {"name": "discount_value", "type": "integer", "primaryKey": false, "notNull": true}, "discount_type": {"name": "discount_type", "type": "promo_type_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "max_uses": {"name": "max_uses", "type": "integer", "primaryKey": false, "notNull": false}, "uses": {"name": "uses", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "start_date": {"name": "start_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "status": {"name": "status", "type": "promos_promo_status_enum", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'ACTIVE'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"promo_code_idx": {"name": "promo_code_idx", "columns": [{"expression": "code", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"promos_tenant_id_tenants_id_fk": {"name": "promos_tenant_id_tenants_id_fk", "tableFrom": "promos", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.promotion": {"name": "promotion", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "promotion_type_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "usage_limit": {"name": "usage_limit", "type": "integer", "primaryKey": false, "notNull": false}, "valid_from": {"name": "valid_from", "type": "timestamp", "primaryKey": false, "notNull": false}, "valid_to": {"name": "valid_to", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"promotion_tenant_id_tenants_id_fk": {"name": "promotion_tenant_id_tenants_id_fk", "tableFrom": "promotion", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ride_event": {"name": "ride_event", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "ride_id": {"name": "ride_id", "type": "uuid", "primaryKey": false, "notNull": true}, "event_type": {"name": "event_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "details": {"name": "details", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"ride_event_tenant_id_idx": {"name": "ride_event_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "ride_event_ride_idx": {"name": "ride_event_ride_idx", "columns": [{"expression": "ride_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "ride_event_type_idx": {"name": "ride_event_type_idx", "columns": [{"expression": "event_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"ride_event_tenant_id_tenants_id_fk": {"name": "ride_event_tenant_id_tenants_id_fk", "tableFrom": "ride_event", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "ride_event_ride_id_ride_order_id_fk": {"name": "ride_event_ride_id_ride_order_id_fk", "tableFrom": "ride_event", "tableTo": "ride_order", "columnsFrom": ["ride_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ride_order": {"name": "ride_order", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "passenger_id": {"name": "passenger_id", "type": "uuid", "primaryKey": false, "notNull": false}, "driver_id": {"name": "driver_id", "type": "uuid", "primaryKey": false, "notNull": false}, "vehicle_id": {"name": "vehicle_id", "type": "uuid", "primaryKey": false, "notNull": false}, "pickup_address": {"name": "pickup_address", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "pickup_latlng": {"name": "pickup_latlng", "type": "jsonb", "primaryKey": false, "notNull": false}, "dropoff_address": {"name": "dropoff_address", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "dropoff_latlng": {"name": "dropoff_latlng", "type": "jsonb", "primaryKey": false, "notNull": false}, "scheduled_time": {"name": "scheduled_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "confirmed_time": {"name": "confirmed_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "start_time": {"name": "start_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "end_time": {"name": "end_time", "type": "timestamp", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "ride_status_enum", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'SEARCHING'"}, "order_type": {"name": "order_type", "type": "ride_type_enum", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'RIDE'"}, "estimated_fare": {"name": "estimated_fare", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": false}, "payment_method": {"name": "payment_method", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "paid": {"name": "paid", "type": "boolean", "primaryKey": false, "notNull": false, "default": false}, "promo_id": {"name": "promo_id", "type": "uuid", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"ride_order_tenant_idx": {"name": "ride_order_tenant_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "ride_order_passenger_idx": {"name": "ride_order_passenger_idx", "columns": [{"expression": "passenger_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "ride_order_driver_idx": {"name": "ride_order_driver_idx", "columns": [{"expression": "driver_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.ride_rating": {"name": "ride_rating", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "ride_id": {"name": "ride_id", "type": "uuid", "primaryKey": false, "notNull": true}, "from_user_id": {"name": "from_user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "to_user_id": {"name": "to_user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "rating": {"name": "rating", "type": "integer", "primaryKey": false, "notNull": true}, "feedback": {"name": "feedback", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"ride_rating_tenant_id_tenants_id_fk": {"name": "ride_rating_tenant_id_tenants_id_fk", "tableFrom": "ride_rating", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.rides": {"name": "rides", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "passenger_id": {"name": "passenger_id", "type": "uuid", "primaryKey": false, "notNull": true}, "driver_id": {"name": "driver_id", "type": "uuid", "primaryKey": false, "notNull": true}, "vehicle_id": {"name": "vehicle_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "ride_status_enum", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'SEARCHING'"}, "order_type": {"name": "order_type", "type": "ride_type_enum", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'RIDE'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"rides_tenant_id_idx": {"name": "rides_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"rides_tenant_id_tenants_id_fk": {"name": "rides_tenant_id_tenants_id_fk", "tableFrom": "rides", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "rides_passenger_id_users_id_fk": {"name": "rides_passenger_id_users_id_fk", "tableFrom": "rides", "tableTo": "users", "columnsFrom": ["passenger_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "rides_driver_id_users_id_fk": {"name": "rides_driver_id_users_id_fk", "tableFrom": "rides", "tableTo": "users", "columnsFrom": ["driver_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "rides_vehicle_id_vehicle_id_fk": {"name": "rides_vehicle_id_vehicle_id_fk", "tableFrom": "rides", "tableTo": "vehicle", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.roles": {"name": "roles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "permissions": {"name": "permissions", "type": "jsonb", "primaryKey": false, "notNull": false}, "is_default": {"name": "is_default", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_system": {"name": "is_system", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}}, "indexes": {"roles_tenant_id_name_idx": {"name": "roles_tenant_id_name_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"roles_tenant_id_tenants_id_fk": {"name": "roles_tenant_id_tenants_id_fk", "tableFrom": "roles", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.scheduled_calls": {"name": "scheduled_calls", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "created_by_operator_id": {"name": "created_by_operator_id", "type": "uuid", "primaryKey": false, "notNull": false}, "customer_user_id": {"name": "customer_user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "phone_number": {"name": "phone_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "base_call_details": {"name": "base_call_details", "type": "jsonb", "primaryKey": false, "notNull": false}, "scheduled_timestamp": {"name": "scheduled_timestamp", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "arrival_time_offset_minutes": {"name": "arrival_time_offset_minutes", "type": "integer", "primaryKey": false, "notNull": false}, "repeat_pattern": {"name": "repeat_pattern", "type": "jsonb", "primaryKey": false, "notNull": false}, "next_activation_timestamp": {"name": "next_activation_timestamp", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "last_activated_timestamp": {"name": "last_activated_timestamp", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "status": {"name": "status", "type": "scheduled_call_status_enum", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'PENDING'"}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"scheduled_calls_tenant_id_idx": {"name": "scheduled_calls_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "scheduled_calls_created_by_operator_id_idx": {"name": "scheduled_calls_created_by_operator_id_idx", "columns": [{"expression": "created_by_operator_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "scheduled_calls_customer_user_id_idx": {"name": "scheduled_calls_customer_user_id_idx", "columns": [{"expression": "customer_user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "scheduled_calls_phone_number_idx": {"name": "scheduled_calls_phone_number_idx", "columns": [{"expression": "phone_number", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "scheduled_calls_next_activation_timestamp_idx": {"name": "scheduled_calls_next_activation_timestamp_idx", "columns": [{"expression": "next_activation_timestamp", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "scheduled_calls_is_active_idx": {"name": "scheduled_calls_is_active_idx", "columns": [{"expression": "is_active", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "scheduled_calls_status_idx": {"name": "scheduled_calls_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"scheduled_calls_tenant_id_tenants_id_fk": {"name": "scheduled_calls_tenant_id_tenants_id_fk", "tableFrom": "scheduled_calls", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "scheduled_calls_created_by_operator_id_users_id_fk": {"name": "scheduled_calls_created_by_operator_id_users_id_fk", "tableFrom": "scheduled_calls", "tableTo": "users", "columnsFrom": ["created_by_operator_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "scheduled_calls_customer_user_id_users_id_fk": {"name": "scheduled_calls_customer_user_id_users_id_fk", "tableFrom": "scheduled_calls", "tableTo": "users", "columnsFrom": ["customer_user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sms_log": {"name": "sms_log", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "direction": {"name": "direction", "type": "sms_log_direction_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "from_number": {"name": "from_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "to_number": {"name": "to_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "sms_status_enum", "typeSchema": "public", "primaryKey": false, "notNull": false}, "gateway_reference_id": {"name": "gateway_reference_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "operator_user_id": {"name": "operator_user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "related_pbx_call_id": {"name": "related_pbx_call_id", "type": "uuid", "primaryKey": false, "notNull": false}, "logged_at": {"name": "logged_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {"sms_log_tenant_id_idx": {"name": "sms_log_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "sms_log_direction_idx": {"name": "sms_log_direction_idx", "columns": [{"expression": "direction", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "sms_log_from_number_idx": {"name": "sms_log_from_number_idx", "columns": [{"expression": "from_number", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "sms_log_to_number_idx": {"name": "sms_log_to_number_idx", "columns": [{"expression": "to_number", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "sms_log_status_idx": {"name": "sms_log_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"sms_log\".\"status\" IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}, "sms_log_operator_user_id_idx": {"name": "sms_log_operator_user_id_idx", "columns": [{"expression": "operator_user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"sms_log\".\"operator_user_id\" IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}, "sms_log_related_pbx_call_id_idx": {"name": "sms_log_related_pbx_call_id_idx", "columns": [{"expression": "related_pbx_call_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"sms_log\".\"related_pbx_call_id\" IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}, "sms_log_logged_at_idx": {"name": "sms_log_logged_at_idx", "columns": [{"expression": "logged_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"sms_log_tenant_id_tenants_id_fk": {"name": "sms_log_tenant_id_tenants_id_fk", "tableFrom": "sms_log", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "sms_log_operator_user_id_users_id_fk": {"name": "sms_log_operator_user_id_users_id_fk", "tableFrom": "sms_log", "tableTo": "users", "columnsFrom": ["operator_user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "sms_log_related_pbx_call_id_pbx_call_id_fk": {"name": "sms_log_related_pbx_call_id_pbx_call_id_fk", "tableFrom": "sms_log", "tableTo": "pbx_call", "columnsFrom": ["related_pbx_call_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.support_ticket": {"name": "support_ticket", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": true}, "details": {"name": "details", "type": "text", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "support_tickets_ticket_status_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "assigned_to_id": {"name": "assigned_to_id", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "closed_at": {"name": "closed_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.system_settings": {"name": "system_settings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": false}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "jsonb", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"system_settings_key_tenant_id_idx": {"name": "system_settings_key_tenant_id_idx", "columns": [{"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "system_settings_tenant_id_idx": {"name": "system_settings_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "system_settings_key_idx": {"name": "system_settings_key_idx", "columns": [{"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "system_settings_deleted_at_idx": {"name": "system_settings_deleted_at_idx", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"system_settings\".\"deleted_at\" IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"system_settings_tenant_id_tenants_id_fk": {"name": "system_settings_tenant_id_tenants_id_fk", "tableFrom": "system_settings", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.telegram_chats": {"name": "telegram_chats", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": true, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "last_seen": {"name": "last_seen", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.telegram_messages": {"name": "telegram_messages", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": true, "notNull": true}, "chat_id": {"name": "chat_id", "type": "bigint", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": false}, "message_type": {"name": "message_type", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "text": {"name": "text", "type": "<PERSON><PERSON><PERSON>(4096)", "primaryKey": false, "notNull": false}, "file_id": {"name": "file_id", "type": "<PERSON><PERSON><PERSON>(256)", "primaryKey": false, "notNull": false}, "caption": {"name": "caption", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "reply_to_message_id": {"name": "reply_to_message_id", "type": "bigint", "primaryKey": false, "notNull": false}, "forward_from_user_id": {"name": "forward_from_user_id", "type": "integer", "primaryKey": false, "notNull": false}, "forward_from_chat_id": {"name": "forward_from_chat_id", "type": "bigint", "primaryKey": false, "notNull": false}, "edit_date": {"name": "edit_date", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "entities": {"name": "entities", "type": "json", "primaryKey": false, "notNull": false}, "command": {"name": "command", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}, "payload": {"name": "payload", "type": "json", "primaryKey": false, "notNull": false}, "service_type": {"name": "service_type", "type": "<PERSON><PERSON><PERSON>(32)", "primaryKey": false, "notNull": false}, "date": {"name": "date", "type": "timestamp with time zone", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false, "default": "now()"}, "raw_data": {"name": "raw_data", "type": "json", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.telegram_users": {"name": "telegram_users", "schema": "", "columns": {"id": {"name": "id", "type": "bigint", "primaryKey": true, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "username": {"name": "username", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.telegram_chat_members": {"name": "telegram_chat_members", "schema": "", "columns": {"chat_id": {"name": "chat_id", "type": "bigint", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "integer", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {"telegram_chat_members_chat_id_user_id_pk": {"name": "telegram_chat_members_chat_id_user_id_pk", "columns": ["chat_id", "user_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.telegram_user_settings": {"name": "telegram_user_settings", "schema": "", "columns": {"user_id": {"name": "user_id", "type": "integer", "primaryKey": true, "notNull": true}, "language": {"name": "language", "type": "<PERSON><PERSON><PERSON>(8)", "primaryKey": false, "notNull": false}, "notifications": {"name": "notifications", "type": "boolean", "primaryKey": false, "notNull": false, "default": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tenant_billing_profile": {"name": "tenant_billing_profile", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "billing_email": {"name": "billing_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "billing_phone": {"name": "billing_phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "tax_id": {"name": "tax_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "<PERSON><PERSON><PERSON>(512)", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "payment_method": {"name": "payment_method", "type": "app_payment_method_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "default_profile": {"name": "default_profile", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "status": {"name": "status", "type": "billing_profiles_status_enum", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'ACTIVE'"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"tenant_billing_profile_tenant_id_idx": {"name": "tenant_billing_profile_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "tenant_billing_profile_tax_id_unique": {"name": "tenant_billing_profile_tax_id_unique", "columns": [{"expression": "tax_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "tenant_billing_profile_default_per_tenant": {"name": "tenant_billing_profile_default_per_tenant", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "default_profile", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"tenant_billing_profile_tenant_id_tenants_id_fk": {"name": "tenant_billing_profile_tenant_id_tenants_id_fk", "tableFrom": "tenant_billing_profile", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tenant_bots": {"name": "tenant_bots", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "bot_name": {"name": "bot_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "telegram_bot_token": {"name": "telegram_bot_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "webhook_domain": {"name": "webhook_domain", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "webhook_path": {"name": "webhook_path", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "webhook_secret_token": {"name": "webhook_secret_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "bot_status_enum", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'STOPPED'"}, "last_status_update": {"name": "last_status_update", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"telegram_bot_token_idx": {"name": "telegram_bot_token_idx", "columns": [{"expression": "telegram_bot_token", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"tenant_bots_tenant_id_tenants_id_fk": {"name": "tenant_bots_tenant_id_tenants_id_fk", "tableFrom": "tenant_bots", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tenant_localization": {"name": "tenant_localization", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "default_locale": {"name": "default_locale", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "supported_locales": {"name": "supported_locales", "type": "jsonb", "primaryKey": false, "notNull": true}, "timezone": {"name": "timezone", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "currency_symbol": {"name": "currency_symbol", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "labels": {"name": "labels", "type": "jsonb", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"tenant_localization_tenant_id_index": {"name": "tenant_localization_tenant_id_index", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"tenant_localization_tenant_id_tenants_id_fk": {"name": "tenant_localization_tenant_id_tenants_id_fk", "tableFrom": "tenant_localization", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tenant_settings": {"name": "tenant_settings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "jsonb", "primaryKey": false, "notNull": false}, "type": {"name": "type", "type": "settings_setting_type_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"tenant_settings_tenant_id_key_unique": {"name": "tenant_settings_tenant_id_key_unique", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "tenant_settings_tenant_id_idx": {"name": "tenant_settings_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "tenant_settings_key_idx": {"name": "tenant_settings_key_idx", "columns": [{"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"tenant_settings_tenant_id_tenants_id_fk": {"name": "tenant_settings_tenant_id_tenants_id_fk", "tableFrom": "tenant_settings", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.tenants": {"name": "tenants", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "legal_name": {"name": "legal_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "logo_url": {"name": "logo_url", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "address": {"name": "address", "type": "<PERSON><PERSON><PERSON>(512)", "primaryKey": false, "notNull": false}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(2)", "primaryKey": false, "notNull": false}, "timezone": {"name": "timezone", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": false}, "plan": {"name": "plan", "type": "tenants_tenant_plan_enum", "typeSchema": "public", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "tenants_tenant_status_enum", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'ACTIVE'"}, "multi_tenant_group_id": {"name": "multi_tenant_group_id", "type": "uuid", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"tenants_multi_tenant_group_id_multi_tenant_group_id_fk": {"name": "tenants_multi_tenant_group_id_multi_tenant_group_id_fk", "tableFrom": "tenants", "tableTo": "multi_tenant_group", "columnsFrom": ["multi_tenant_group_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_bot_roles": {"name": "user_bot_roles", "schema": "", "columns": {"telegram_user_id": {"name": "telegram_user_id", "type": "bigint", "primaryKey": false, "notNull": true}, "bot_id": {"name": "bot_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role_id": {"name": "role_id", "type": "uuid", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"user_bot_roles_bot_id_bots_id_fk": {"name": "user_bot_roles_bot_id_bots_id_fk", "tableFrom": "user_bot_roles", "tableTo": "bots", "columnsFrom": ["bot_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_bot_roles_role_id_roles_id_fk": {"name": "user_bot_roles_role_id_roles_id_fk", "tableFrom": "user_bot_roles", "tableTo": "roles", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {"pk_user_bot_role": {"name": "pk_user_bot_role", "columns": ["telegram_user_id", "bot_id", "role_id"]}}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_consent": {"name": "user_consent", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": false}, "consent_type": {"name": "consent_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "granted": {"name": "granted", "type": "boolean", "primaryKey": false, "notNull": true}, "granted_at": {"name": "granted_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "revoked_at": {"name": "revoked_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {"user_consent_user_consent_type_idx": {"name": "user_consent_user_consent_type_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "consent_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "user_consent_deleted_at_idx": {"name": "user_consent_deleted_at_idx", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_consent_user_id_users_id_fk": {"name": "user_consent_user_id_users_id_fk", "tableFrom": "user_consent", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_consent_tenant_id_tenants_id_fk": {"name": "user_consent_tenant_id_tenants_id_fk", "tableFrom": "user_consent", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_identity": {"name": "user_identity", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "user_role_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "external_id": {"name": "external_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "display": {"name": "display", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "avatar_url": {"name": "avatar_url", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": true, "default": "'{}'::json"}, "linked_at": {"name": "linked_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "unlinked_at": {"name": "unlinked_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"user_identity_provider_external_id_idx": {"name": "user_identity_provider_external_id_idx", "columns": [{"expression": "provider", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "external_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "user_identity_user_id_idx": {"name": "user_identity_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "user_identity_provider_idx": {"name": "user_identity_provider_idx", "columns": [{"expression": "provider", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_identity_user_id_users_id_fk": {"name": "user_identity_user_id_users_id_fk", "tableFrom": "user_identity", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_kyc": {"name": "user_kyc", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": false}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "submitted_at": {"name": "submitted_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "status": {"name": "status", "type": "kyc_status_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "document_type": {"name": "document_type", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "document_number": {"name": "document_number", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "country": {"name": "country", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "expiry_date": {"name": "expiry_date", "type": "date", "primaryKey": false, "notNull": false}, "rejection_reason": {"name": "rejection_reason", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}}, "indexes": {"user_kyc_tenant_id_idx": {"name": "user_kyc_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_kyc_user_id_idx": {"name": "user_kyc_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_kyc_tenant_id_tenants_id_fk": {"name": "user_kyc_tenant_id_tenants_id_fk", "tableFrom": "user_kyc", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "user_kyc_user_id_users_id_fk": {"name": "user_kyc_user_id_users_id_fk", "tableFrom": "user_kyc", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_onboarding": {"name": "user_onboarding", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": false}, "completed_steps": {"name": "completed_steps", "type": "jsonb", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "user_onboarding_status_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"user_onboarding_user_tenant_idx": {"name": "user_onboarding_user_tenant_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "user_onboarding_deleted_at_idx": {"name": "user_onboarding_deleted_at_idx", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_onboarding_user_id_users_id_fk": {"name": "user_onboarding_user_id_users_id_fk", "tableFrom": "user_onboarding", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_onboarding_tenant_id_tenants_id_fk": {"name": "user_onboarding_tenant_id_tenants_id_fk", "tableFrom": "user_onboarding", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_profile_history": {"name": "user_profile_history", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "changed_by_id": {"name": "changed_by_id", "type": "uuid", "primaryKey": false, "notNull": false}, "change_type": {"name": "change_type", "type": "user_profile_change_type_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "old_value": {"name": "old_value", "type": "json", "primaryKey": false, "notNull": true}, "new_value": {"name": "new_value", "type": "json", "primaryKey": false, "notNull": true}, "changed_at": {"name": "changed_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "context": {"name": "context", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {"user_profile_history_user_id_changed_at_idx": {"name": "user_profile_history_user_id_changed_at_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "changed_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "user_profile_history_deleted_at_idx": {"name": "user_profile_history_deleted_at_idx", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "where": "\"user_profile_history\".\"deleted_at\" is not null", "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_profile_history_user_id_users_id_fk": {"name": "user_profile_history_user_id_users_id_fk", "tableFrom": "user_profile_history", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_profile_history_changed_by_id_users_id_fk": {"name": "user_profile_history_changed_by_id_users_id_fk", "tableFrom": "user_profile_history", "tableTo": "users", "columnsFrom": ["changed_by_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_tenant": {"name": "user_tenant", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role_id": {"name": "role_id", "type": "uuid", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "users_status_enum", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'ACTIVE'"}, "is_primary": {"name": "is_primary", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "last_used": {"name": "last_used", "type": "timestamp", "primaryKey": false, "notNull": false}, "registered_at": {"name": "registered_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "invited_by_id": {"name": "invited_by_id", "type": "uuid", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": true, "default": "'{}'::json"}}, "indexes": {"user_tenant_user_id_tenant_id_idx": {"name": "user_tenant_user_id_tenant_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "user_tenant_invited_by_id_idx": {"name": "user_tenant_invited_by_id_idx", "columns": [{"expression": "invited_by_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_tenant_user_id_users_id_fk": {"name": "user_tenant_user_id_users_id_fk", "tableFrom": "user_tenant", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_tenant_tenant_id_tenants_id_fk": {"name": "user_tenant_tenant_id_tenants_id_fk", "tableFrom": "user_tenant", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_tenant_role_id_roles_id_fk": {"name": "user_tenant_role_id_roles_id_fk", "tableFrom": "user_tenant", "tableTo": "roles", "columnsFrom": ["role_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_tenant_invited_by_id_users_id_fk": {"name": "user_tenant_invited_by_id_users_id_fk", "tableFrom": "user_tenant", "tableTo": "users", "columnsFrom": ["invited_by_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "external_id": {"name": "external_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "verified": {"name": "verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "registered_at": {"name": "registered_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "last_login_at": {"name": "last_login_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "legal_name": {"name": "legal_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "display_name": {"name": "display_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "date_of_birth": {"name": "date_of_birth", "type": "timestamp", "primaryKey": false, "notNull": false}, "language": {"name": "language", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false}, "avatar_url": {"name": "avatar_url", "type": "<PERSON><PERSON><PERSON>(1024)", "primaryKey": false, "notNull": false}, "communication_opt_in": {"name": "communication_opt_in", "type": "json", "primaryKey": false, "notNull": false}, "privacy_flags": {"name": "privacy_flags", "type": "json", "primaryKey": false, "notNull": false}, "current_location": {"name": "current_location", "type": "geometry(point)", "primaryKey": false, "notNull": false}, "location_accuracy": {"name": "location_accuracy", "type": "double precision", "primaryKey": false, "notNull": false}, "location_updated_at": {"name": "location_updated_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "json", "primaryKey": false, "notNull": false}}, "indexes": {"users_phone_idx": {"name": "users_phone_idx", "columns": [{"expression": "phone", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "users_email_idx": {"name": "users_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "users_external_id_idx": {"name": "users_external_id_idx", "columns": [{"expression": "external_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}, "users_deleted_at_idx": {"name": "users_deleted_at_idx", "columns": [{"expression": "deleted_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"users\".\"deleted_at\" IS NOT NULL", "concurrently": false, "method": "btree", "with": {}}, "users_current_location_idx": {"name": "users_current_location_idx", "columns": [{"expression": "current_location", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "where": "\"users\".\"current_location\" IS NOT NULL", "concurrently": false, "method": "gist", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_phone_unique": {"name": "users_phone_unique", "nullsNotDistinct": false, "columns": ["phone"]}, "users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}, "users_external_id_unique": {"name": "users_external_id_unique", "nullsNotDistinct": false, "columns": ["external_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vehicle": {"name": "vehicle", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "license_plate": {"name": "license_plate", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "make": {"name": "make", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "model": {"name": "model", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "color": {"name": "color", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "year": {"name": "year", "type": "integer", "primaryKey": false, "notNull": false}, "fuel_type": {"name": "fuel_type", "type": "fuel_type_enum", "typeSchema": "public", "primaryKey": false, "notNull": false}, "registration_id": {"name": "registration_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "vehicles_vehicle_status_enum", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'ACTIVE'"}, "purchase_date": {"name": "purchase_date", "type": "date", "primaryKey": false, "notNull": false}, "purchase_price": {"name": "purchase_price", "type": "numeric(12, 2)", "primaryKey": false, "notNull": false}, "sold_date": {"name": "sold_date", "type": "date", "primaryKey": false, "notNull": false}, "sold_price": {"name": "sold_price", "type": "numeric(12, 2)", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"license_plate_tenant_id_idx": {"name": "license_plate_tenant_id_idx", "columns": [{"expression": "license_plate", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_tenant_id_tenants_id_fk": {"name": "vehicle_tenant_id_tenants_id_fk", "tableFrom": "vehicle", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vehicle_expense": {"name": "vehicle_expense", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "vehicle_id": {"name": "vehicle_id", "type": "uuid", "primaryKey": false, "notNull": true}, "expense_type": {"name": "expense_type", "type": "vehicle_expense_type_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "date", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "cost": {"name": "cost", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"vehicle_expense_tenant_id_idx": {"name": "vehicle_expense_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_expense_vehicle_id_idx": {"name": "vehicle_expense_vehicle_id_idx", "columns": [{"expression": "vehicle_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_expense_type_idx": {"name": "vehicle_expense_type_idx", "columns": [{"expression": "expense_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_expense_date_idx": {"name": "vehicle_expense_date_idx", "columns": [{"expression": "date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_expense_tenant_id_tenants_id_fk": {"name": "vehicle_expense_tenant_id_tenants_id_fk", "tableFrom": "vehicle_expense", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "vehicle_expense_vehicle_id_vehicle_id_fk": {"name": "vehicle_expense_vehicle_id_vehicle_id_fk", "tableFrom": "vehicle_expense", "tableTo": "vehicle", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vehicle_note": {"name": "vehicle_note", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "vehicle_id": {"name": "vehicle_id", "type": "uuid", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "note": {"name": "note", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"vehicle_note_tenant_id_idx": {"name": "vehicle_note_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_note_vehicle_id_idx": {"name": "vehicle_note_vehicle_id_idx", "columns": [{"expression": "vehicle_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_note_created_at_idx": {"name": "vehicle_note_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_note_tenant_id_tenants_id_fk": {"name": "vehicle_note_tenant_id_tenants_id_fk", "tableFrom": "vehicle_note", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "vehicle_note_vehicle_id_vehicle_id_fk": {"name": "vehicle_note_vehicle_id_vehicle_id_fk", "tableFrom": "vehicle_note", "tableTo": "vehicle", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vehicle_odometer_log": {"name": "vehicle_odometer_log", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "vehicle_id": {"name": "vehicle_id", "type": "uuid", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "date", "primaryKey": false, "notNull": true}, "initial_value": {"name": "initial_value", "type": "integer", "primaryKey": false, "notNull": false}, "odometer_value": {"name": "odometer_value", "type": "integer", "primaryKey": false, "notNull": true}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"vehicle_odometer_log_tenant_id_idx": {"name": "vehicle_odometer_log_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_odometer_log_vehicle_id_idx": {"name": "vehicle_odometer_log_vehicle_id_idx", "columns": [{"expression": "vehicle_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_odometer_log_date_idx": {"name": "vehicle_odometer_log_date_idx", "columns": [{"expression": "date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_odometer_log_tenant_id_tenants_id_fk": {"name": "vehicle_odometer_log_tenant_id_tenants_id_fk", "tableFrom": "vehicle_odometer_log", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "vehicle_odometer_log_vehicle_id_vehicle_id_fk": {"name": "vehicle_odometer_log_vehicle_id_vehicle_id_fk", "tableFrom": "vehicle_odometer_log", "tableTo": "vehicle", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vehicle_parts_log": {"name": "vehicle_parts_log", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "vehicle_id": {"name": "vehicle_id", "type": "uuid", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "date", "primaryKey": false, "notNull": true}, "part_number": {"name": "part_number", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "supplier": {"name": "supplier", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "quantity": {"name": "quantity", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "cost_per_unit": {"name": "cost_per_unit", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "total_cost": {"name": "total_cost", "type": "numeric(12, 2)", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"vehicle_parts_log_tenant_id_idx": {"name": "vehicle_parts_log_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_parts_log_vehicle_id_idx": {"name": "vehicle_parts_log_vehicle_id_idx", "columns": [{"expression": "vehicle_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_parts_log_date_idx": {"name": "vehicle_parts_log_date_idx", "columns": [{"expression": "date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_parts_log_part_number_idx": {"name": "vehicle_parts_log_part_number_idx", "columns": [{"expression": "part_number", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_parts_log_supplier_idx": {"name": "vehicle_parts_log_supplier_idx", "columns": [{"expression": "supplier", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_parts_log_tenant_id_tenants_id_fk": {"name": "vehicle_parts_log_tenant_id_tenants_id_fk", "tableFrom": "vehicle_parts_log", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "vehicle_parts_log_vehicle_id_vehicle_id_fk": {"name": "vehicle_parts_log_vehicle_id_vehicle_id_fk", "tableFrom": "vehicle_parts_log", "tableTo": "vehicle", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vehicle_reminder": {"name": "vehicle_reminder", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "vehicle_id": {"name": "vehicle_id", "type": "uuid", "primaryKey": false, "notNull": true}, "urgency": {"name": "urgency", "type": "reminder_urgency_enum", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'NOT_URGENT'"}, "trigger_odometer": {"name": "trigger_odometer", "type": "integer", "primaryKey": false, "notNull": false}, "trigger_date": {"name": "trigger_date", "type": "date", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "is_dismissed": {"name": "is_dismissed", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "dismissed_at": {"name": "dismissed_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"vehicle_reminder_tenant_id_idx": {"name": "vehicle_reminder_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_reminder_vehicle_id_idx": {"name": "vehicle_reminder_vehicle_id_idx", "columns": [{"expression": "vehicle_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_reminder_urgency_idx": {"name": "vehicle_reminder_urgency_idx", "columns": [{"expression": "urgency", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_reminder_trigger_date_idx": {"name": "vehicle_reminder_trigger_date_idx", "columns": [{"expression": "trigger_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_reminder_is_dismissed_idx": {"name": "vehicle_reminder_is_dismissed_idx", "columns": [{"expression": "is_dismissed", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_reminder_tenant_id_tenants_id_fk": {"name": "vehicle_reminder_tenant_id_tenants_id_fk", "tableFrom": "vehicle_reminder", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "vehicle_reminder_vehicle_id_vehicle_id_fk": {"name": "vehicle_reminder_vehicle_id_vehicle_id_fk", "tableFrom": "vehicle_reminder", "tableTo": "vehicle", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vehicle_repair": {"name": "vehicle_repair", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "vehicle_id": {"name": "vehicle_id", "type": "uuid", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "date", "primaryKey": false, "notNull": true}, "odometer": {"name": "odometer", "type": "integer", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "cost": {"name": "cost", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "repair_shop": {"name": "repair_shop", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"vehicle_repair_tenant_id_idx": {"name": "vehicle_repair_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_repair_vehicle_id_idx": {"name": "vehicle_repair_vehicle_id_idx", "columns": [{"expression": "vehicle_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_repair_date_idx": {"name": "vehicle_repair_date_idx", "columns": [{"expression": "date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_repair_tenant_id_tenants_id_fk": {"name": "vehicle_repair_tenant_id_tenants_id_fk", "tableFrom": "vehicle_repair", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "vehicle_repair_vehicle_id_vehicle_id_fk": {"name": "vehicle_repair_vehicle_id_vehicle_id_fk", "tableFrom": "vehicle_repair", "tableTo": "vehicle", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vehicle_service_record": {"name": "vehicle_service_record", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "vehicle_id": {"name": "vehicle_id", "type": "uuid", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "date", "primaryKey": false, "notNull": true}, "odometer": {"name": "odometer", "type": "integer", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "cost": {"name": "cost", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "service_provider": {"name": "service_provider", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"vehicle_service_record_tenant_id_idx": {"name": "vehicle_service_record_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_service_record_vehicle_id_idx": {"name": "vehicle_service_record_vehicle_id_idx", "columns": [{"expression": "vehicle_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_service_record_date_idx": {"name": "vehicle_service_record_date_idx", "columns": [{"expression": "date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_service_record_tenant_id_tenants_id_fk": {"name": "vehicle_service_record_tenant_id_tenants_id_fk", "tableFrom": "vehicle_service_record", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "vehicle_service_record_vehicle_id_vehicle_id_fk": {"name": "vehicle_service_record_vehicle_id_vehicle_id_fk", "tableFrom": "vehicle_service_record", "tableTo": "vehicle", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vehicle_upgrade": {"name": "vehicle_upgrade", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "vehicle_id": {"name": "vehicle_id", "type": "uuid", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "date", "primaryKey": false, "notNull": true}, "odometer": {"name": "odometer", "type": "integer", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": true}, "cost": {"name": "cost", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "notes": {"name": "notes", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp with time zone", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"vehicle_upgrade_tenant_id_idx": {"name": "vehicle_upgrade_tenant_id_idx", "columns": [{"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_upgrade_vehicle_id_idx": {"name": "vehicle_upgrade_vehicle_id_idx", "columns": [{"expression": "vehicle_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "vehicle_upgrade_date_idx": {"name": "vehicle_upgrade_date_idx", "columns": [{"expression": "date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"vehicle_upgrade_tenant_id_tenants_id_fk": {"name": "vehicle_upgrade_tenant_id_tenants_id_fk", "tableFrom": "vehicle_upgrade", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "vehicle_upgrade_vehicle_id_vehicle_id_fk": {"name": "vehicle_upgrade_vehicle_id_vehicle_id_fk", "tableFrom": "vehicle_upgrade", "tableTo": "vehicle", "columnsFrom": ["vehicle_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification_event": {"name": "verification_event", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "chatbot_user_id": {"name": "chatbot_user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "method": {"name": "method", "type": "verification_method_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "verification_status_enum", "typeSchema": "public", "primaryKey": false, "notNull": true}, "reference": {"name": "reference", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "initiated_at": {"name": "initiated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "jsonb", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"verification_event_chatbot_user_id_chatbot_users_id_fk": {"name": "verification_event_chatbot_user_id_chatbot_users_id_fk", "tableFrom": "verification_event", "tableTo": "chatbot_users", "columnsFrom": ["chatbot_user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.wallet": {"name": "wallet", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": true}, "balance": {"name": "balance", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true, "default": "'0.0'"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"wallet_user_tenant_currency_idx": {"name": "wallet_user_tenant_currency_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "tenant_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "currency", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": true, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"wallet_user_id_users_id_fk": {"name": "wallet_user_id_users_id_fk", "tableFrom": "wallet", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "wallet_tenant_id_tenants_id_fk": {"name": "wallet_tenant_id_tenants_id_fk", "tableFrom": "wallet", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.webhook_subscriber": {"name": "webhook_subscriber", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "tenant_id": {"name": "tenant_id", "type": "uuid", "primaryKey": false, "notNull": true}, "url": {"name": "url", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "events": {"name": "events", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"webhook_subscriber_tenant_id_tenants_id_fk": {"name": "webhook_subscriber_tenant_id_tenants_id_fk", "tableFrom": "webhook_subscriber", "tableTo": "tenants", "columnsFrom": ["tenant_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.actors_actor_type_enum": {"name": "actors_actor_type_enum", "schema": "public", "values": ["USER", "OPERATOR", "SYSTEM"]}, "public.billing_method": {"name": "billing_method", "schema": "public", "values": ["CREDIT_CARD", "PAYPAL", "BANK_TRANSFER", "CRYPTO"]}, "public.billing_profiles_status_enum": {"name": "billing_profiles_status_enum", "schema": "public", "values": ["ACTIVE", "INACTIVE", "DEPRECATED"]}, "public.bot_status_enum": {"name": "bot_status_enum", "schema": "public", "values": ["RUNNING", "STOPPED", "ERROR"]}, "public.calls_direction_enum": {"name": "calls_direction_enum", "schema": "public", "values": ["INBOUND", "OUTBOUND"]}, "public.call_source_enum": {"name": "call_source_enum", "schema": "public", "values": ["PBX_INCOMING", "PBX_OUTGOING", "MANUAL", "CHATBOT_TELEGRAM", "CHATBOT_VIBER", "CHATBOT_WHATSAPP", "SCHEDULED_ACTIVATION", "APP_PASSENGER", "APP_DRIVER", "API", "DUPLICATE"]}, "public.calls_status_enum": {"name": "calls_status_enum", "schema": "public", "values": ["SCHEDULED", "RINGING", "ANSWERED", "MISSED", "ENDED", "FAILED"]}, "public.events_event_type_enum": {"name": "events_event_type_enum", "schema": "public", "values": ["MESSAGE_RECEIVED", "MESSAGE_SENT", "USER_LINKED", "USER_UNLINKED", "CONSENT_GRANTED", "CONSENT_REVOKED", "VERIFICATION_REQUESTED", "VERIFICATION_SUCCEEDED", "VERIFICATION_FAILED", "SESSION_STARTED", "SESSION_ENDED", "ERROR", "WEBHOOK_RECEIVED"]}, "public.fuel_type_enum": {"name": "fuel_type_enum", "schema": "public", "values": ["GASOLINE", "DIESEL", "ELECTRIC", "HYBRID", "LPG", "CNG", "HYDROGEN", "OTHER"]}, "public.groups_group_status_enum": {"name": "groups_group_status_enum", "schema": "public", "values": ["ACTIVE", "PENDING", "INACTIVE"]}, "public.groups_group_type_enum": {"name": "groups_group_type_enum", "schema": "public", "values": ["FRANCHISE", "AGGREGATOR", "BRAND", "CORPORATE"]}, "public.invoice_status": {"name": "invoice_status", "schema": "public", "values": ["PENDING", "PAID", "OPEN", "FAILED"]}, "public.kyc_status_enum": {"name": "kyc_status_enum", "schema": "public", "values": ["pending", "approved", "rejected", "expired"]}, "public.message_direction": {"name": "message_direction", "schema": "public", "values": ["IN", "OUT"]}, "public.message_type_enum": {"name": "message_type_enum", "schema": "public", "values": ["TEXT", "IMAGE", "VOICE", "LOCATION", "VERIFICATION", "DOCUMENT", "STICKER", "BUTTON", "TEMPLATE", "INTERACTIVE", "CAROUSEL", "GROUP_CHAT", "UNKNOWN"]}, "public.user_onboarding_status_enum": {"name": "user_onboarding_status_enum", "schema": "public", "values": ["INCOMPLETE", "IN_PROGRESS", "COMPLETED"]}, "public.operators_operator_status_enum": {"name": "operators_operator_status_enum", "schema": "public", "values": ["ACTIVE", "INACTIVE", "SUSPENDED", "PENDING"]}, "public.app_payment_method_enum": {"name": "app_payment_method_enum", "schema": "public", "values": ["CARD", "WIRE", "CRYPTO", "INVOICE"]}, "public.payment_status_enum": {"name": "payment_status_enum", "schema": "public", "values": ["PENDING", "SUCCESS", "FAILED"]}, "public.user_profile_change_type_enum": {"name": "user_profile_change_type_enum", "schema": "public", "values": ["ONBOARDING", "PHONE_CHANGE", "ADMIN_UPDATE"]}, "public.promos_promo_status_enum": {"name": "promos_promo_status_enum", "schema": "public", "values": ["ACTIVE", "INACTIVE", "EXPIRED", "LIMIT_REACHED"]}, "public.promo_type_enum": {"name": "promo_type_enum", "schema": "public", "values": ["PERCENTAGE", "FIXED", "FREE_RIDE"]}, "public.promotion_type_enum": {"name": "promotion_type_enum", "schema": "public", "values": ["PERCENTAGE", "VALUE", "FREE_RIDE"]}, "public.provider_type_enum": {"name": "provider_type_enum", "schema": "public", "values": ["TELEGRAM", "VIBER", "FACEBOOK", "GOOGLE", "APPLE", "PHONE"]}, "public.reminder_urgency_enum": {"name": "reminder_urgency_enum", "schema": "public", "values": ["NOT_URGENT", "URGENT", "VERY_URGENT", "PAST_DUE"]}, "public.ride_ratings_status_enum": {"name": "ride_ratings_status_enum", "schema": "public", "values": ["ONE", "TWO", "THREE", "FOUR", "FIVE"]}, "public.ride_status_enum": {"name": "ride_status_enum", "schema": "public", "values": ["SEARCHING", "ASSIGNED", "PICKED_UP", "COMPLETED", "CANCELLED"]}, "public.ride_type_enum": {"name": "ride_type_enum", "schema": "public", "values": ["RIDE", "DELIVERY", "POOLED", "SHARED"]}, "public.scheduled_call_status_enum": {"name": "scheduled_call_status_enum", "schema": "public", "values": ["PENDING", "ACTIVE_ONCE", "ACTIVE_RECURRING", "COMPLETED", "PAUSED", "DISABLED"]}, "public.settings_setting_type_enum": {"name": "settings_setting_type_enum", "schema": "public", "values": ["SYSTEM", "PAYMENT", "I18N", "UI"]}, "public.sms_log_direction_enum": {"name": "sms_log_direction_enum", "schema": "public", "values": ["OUTBOUND_OPERATOR", "INBOUND_REPLY"]}, "public.sms_status_enum": {"name": "sms_status_enum", "schema": "public", "values": ["PENDING", "QUEUED", "SENT", "FAILED", "DELIVERED", "UNDELIVERED", "RECEIVED", "READ"]}, "public.app_source_type_enum": {"name": "app_source_type_enum", "schema": "public", "values": ["TELEGRAM", "ANDROID", "MQTT", "WEB", "MANUAL"]}, "public.support_tickets_ticket_status_enum": {"name": "support_tickets_ticket_status_enum", "schema": "public", "values": ["OPEN", "CLOSED", "PENDING"]}, "public.tenants_tenant_plan_enum": {"name": "tenants_tenant_plan_enum", "schema": "public", "values": ["FREE", "PRO", "ENTERPRISE"]}, "public.tenants_tenant_status_enum": {"name": "tenants_tenant_status_enum", "schema": "public", "values": ["ACTIVE", "SUSPENDED", "CANCELLED"]}, "public.user_role_enum": {"name": "user_role_enum", "schema": "public", "values": ["PASSENGER", "DRIVER", "OPERATOR", "MANAGER", "ADMIN", "SYSTEM_SUPPORT"]}, "public.users_status_enum": {"name": "users_status_enum", "schema": "public", "values": ["ACTIVE", "INACTIVE", "BANNED", "SUSPENDED"]}, "public.vehicle_expense_type_enum": {"name": "vehicle_expense_type_enum", "schema": "public", "values": ["TAX", "INSURANCE", "REGISTRATION", "MAINTENANCE", "FUEL", "OTHER"]}, "public.vehicles_vehicle_status_enum": {"name": "vehicles_vehicle_status_enum", "schema": "public", "values": ["ACTIVE", "DISABLED", "DECOMMISSIONED", "REPAIR"]}, "public.verification_method_enum": {"name": "verification_method_enum", "schema": "public", "values": ["SMS", "EMAIL", "PHONE_CALL", "OTHER"]}, "public.verification_status_enum": {"name": "verification_status_enum", "schema": "public", "values": ["INITIATED", "CODE_SENT", "VERIFIED", "FAILED", "EXPIRED"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}