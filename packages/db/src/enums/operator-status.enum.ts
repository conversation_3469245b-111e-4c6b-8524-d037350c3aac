import { pgEnum } from "drizzle-orm/pg-core";

const OPERATOR_STATUS_VALUES = [
	"ACTIVE",
	"INACTIVE",
	"SUSPENDED",
	"PENDING",
] as const;

export const operatorStatusEnum = pgEnum(
	"operator_status",
	OPERATOR_STATUS_VALUES,
);

export type OperatorStatusType = (typeof OPERATOR_STATUS_VALUES)[number];

export enum OperatorStatus {
	ACTIVE = "ACTIVE",
	INACTIVE = "INACTIVE",
	SUSPENDED = "SUSPENDED",
	PENDING = "PENDING",
}
