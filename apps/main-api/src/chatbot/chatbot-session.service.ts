import { Inject, Injectable, Logger } from "@nestjs/common";
import type { IRedisService } from "@/redis/redis.interface";
import { RedisServiceToken } from "@/redis/redis.token";

// Define a simple session data structure
interface ChatbotSessionData {
	lastInteractionTime: string; // ISO 8601
	currentStep?: string;
	collectedData?: Record<string, any>;
	// Add other session-specific fields
}

@Injectable()
export class ChatbotSessionService {
	private readonly logger = new Logger(ChatbotSessionService.name);
	private readonly SESSION_TTL_SECONDS = 3600; // 1 hour, adjust as needed

	constructor(
		@Inject(RedisServiceToken) private readonly redisService: IRedisService,
	) {}

	private getSessionKey(platform: string, chatId: string | number): string {
		return `chatbot_session:${platform}:${chatId}`;
	}

	async getSession(
		tenantId: string,
		platform: string,
		chatId: string | number,
	): Promise<ChatbotSessionData | null> {
		const key = this.getSessionKey(platform, chatId);
		const sessionString = await this.redisService.get(tenantId, key);
		if (sessionString) {
			try {
				return JSON.parse(sessionString) as ChatbotSessionData;
			} catch (error) {
				this.logger.error(`Failed to parse session data for key ${key}, tenant ${tenantId}:`, error);
				return null;
			}
		}
		return null;
	}

	async setSession(
		tenantId: string,
		platform: string,
		chatId: string | number,
		sessionData: ChatbotSessionData,
		ttlSeconds: number = this.SESSION_TTL_SECONDS,
	): Promise<void> {
		const key = this.getSessionKey(platform, chatId);
		const sessionString = JSON.stringify(sessionData);
		await this.redisService.set(tenantId, key, sessionString, ttlSeconds);
	}

	async deleteSession(tenantId: string, platform: string, chatId: string | number): Promise<void> {
		const key = this.getSessionKey(platform, chatId);
		await this.redisService.del(tenantId, key);
	}
}