{"name": "@repo/main-api", "version": "0.0.1", "description": "Telegram bot API service", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "start": "node dist/main.js", "dev": "nest start --watch", "format": "biome format src --write", "lint": "biome lint src", "typecheck": "tsc --noEmit", "test": "jest --passWithNoTests", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "clean": "rm -rf dist .turbo tsconfig.tsbuildinfo"}, "dependencies": {"@golevelup/nestjs-rabbitmq": "^6.0.0", "@gramio/auto-answer-callback-query": "^0.0.2", "@gramio/auto-retry": "^0.0.3", "@gramio/autoload": "^1.1.0", "@gramio/i18n": "^1.3.0", "@gramio/keyboards": "^1.2.1", "@gramio/media-cache": "^0.0.4", "@gramio/media-group": "^0.0.4", "@gramio/prompt": "^1.1.4", "@gramio/scenes": "^0.3.0", "@gramio/session": "^0.1.6", "@gramio/storage": "^1.0.0", "@gramio/storage-redis": "^1.0.4", "@nestjs/axios": "^4.0.0", "@nestjs/common": "^11.1.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.1", "@nestjs/mapped-types": "^2.1.0", "@nestjs/microservices": "^11.1.2", "@nestjs/platform-express": "^11.1.1", "@nestjs/swagger": "^11.2.0", "@nestjs/terminus": "^11.0.0", "@repo/db": "workspace:*", "@repo/types": "workspace:*", "@types/rascal": "^10.2.1", "@verrou/core": "^0.5.1", "ari-client": "^2.2.0", "class-validator": "^0.14.2", "drizzle-orm": "^0.44.0", "env-var": "^7.5.0", "event-emmiter": "^1.0.4", "events": "^3.3.0", "exponential-backoff": "^3.1.2", "gramio": "^0.4.3", "ioredis": "^5.6.1", "nestjs-pino": "^4.4.0", "pino": "^9.7.0", "pino-http": "^10.4.0", "pino-pretty": "^13.0.0", "rascal": "^20.1.1", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "uuid": "^11.1.0", "ws": "^8.18.2"}, "devDependencies": {"@gramio/types": "^9.0.3", "@nestjs/cli": "^11.0.7", "@nestjs/schematics": "^11.0.5", "@nestjs/testing": "^11.1.1", "@repo/typescript-config": "workspace:*", "@swc/core": "^1.11.24", "@types/ari-client": "^2.2.13", "@types/bun": "^1.2.13", "@types/events": "^3.0.3", "@types/ws": "^8.18.1", "swc-loader": "^0.2.6", "ts-jest": "^29.3.4", "typescript": "^5.8.3", "webpack-node-externals": "^3.0.0"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}