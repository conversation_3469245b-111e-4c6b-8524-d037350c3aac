// @repo/types/src/events/chatbot-domain.events.ts
import type { BaseEventPayload } from "./base-event.payload.js";

export interface ChatbotIntentRecognizedEvent extends BaseEventPayload {
	botDbId: string;
	platform: string;
	platformUserId: string;
	platformChatId: string;
	sessionId: string; // Your internal session ID from chatbot_sessions.schema.ts
	intent: string; // e.g., "request_ride", "check_status", "ask_faq"
	entities: Record<string, any>; // Extracted entities (location, time, etc.)
	originalMessageText?: string;
	confidence?: number;
}

export interface ChatbotActionRequiredEvent extends BaseEventPayload {
	// When the bot needs the core system to do something
	botDbId: string;
	sessionId: string;
	actionType:
		| "CREATE_DISPATCH_CALL"
		| "SEND_OPERATOR_ALERT"
		| "LOOKUP_ORDER_STATUS";
	actionPayload: any; // Specific payload for the action
}

export interface ChatbotOutgoingMessageRequestEvent extends BaseEventPayload {
	// When the bot logic decides to send a message back to the user
	botDbId: string;
	platform: string;
	platformChatId: string;
	platformUserId: string; // Usually same as chatId for private chats
	message: {
		text?: string;
		// Add other platform-specific message components: photo, keyboard, etc.
		// This should be a generic structure that a platform-specific sender can interpret.
		platformSpecificPayload?: any;
	};
	replyToPlatformMessageId?: string;
}

// Event published when a chatbot needs the core system to perform a dispatch-related action
export type ChatbotDispatchActionRequestedEvent = BaseEventPayload<{
	botDbId: string;
	internalSessionId: string;
	platformUserId: string;
	actionType: "CREATE_PBX_CALL" | "CREATE_RIDE_ORDER" | "GET_CALL_STATUS";
	// Payload specific to the action, e.g.:
	dispatchDetails?: {
		customerPhoneNumber?: string;
		pickupLocationText?: string;
		// ...
	};
	callIdToQuery?: string;
}>;
