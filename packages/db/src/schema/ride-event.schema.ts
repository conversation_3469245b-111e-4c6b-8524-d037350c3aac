import { relations } from "drizzle-orm";
import {
	index,
	jsonb,
	pgTable,
	uniqueIndex,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { timestamp } from "drizzle-orm/pg-core"; // Moved timestamp import
import { rideOrder } from "./ride-order.schema.js";
import { tenants } from "./tenants.schema.js";

export const rideEvent = pgTable(
	"ride_event",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id")
			.references(() => tenants.id, { onDelete: "cascade" })
			.notNull(),
		rideId: uuid("ride_id")
			.references(() => rideOrder.id)
			.notNull(),
		eventType: varchar("event_type", { length: 50 }).notNull(),
		details: jsonb("details"),
		createdAt: timestamp("created_at").defaultNow().notNull(),
	},
	(table) => [
		index("ride_event_tenant_id_idx").on(table.tenantId),
		uniqueIndex("ride_event_ride_idx").on(table.rideId),
		uniqueIndex("ride_event_type_idx").on(table.eventType),
	],
);

export const rideEventRelations = relations(rideEvent, ({ one }) => ({
	tenant: one(tenants, {
		fields: [rideEvent.tenantId],
		references: [tenants.id],
	}),
	ride: one(rideOrder, {
		fields: [rideEvent.rideId],
		references: [rideOrder.id],
	}),
}));
