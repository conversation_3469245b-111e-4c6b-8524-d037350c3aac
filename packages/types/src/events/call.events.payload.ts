import type { Pbx<PERSON>all } from "@repo/db";
// packages/types/src/events/call.events.payload.ts
import type { BaseEventPayload } from "./base-event.payload.js";

// This might represent the initial logging of a call from any source (PBX, Manual)
export interface CallLoggedPayload extends BaseEventPayload {
	data: PbxCall; // The full call object as initially created in the DB
}

// When a call (already logged) is answered by an operator
export interface CallAnsweredPayload extends BaseEventPayload {
	data: {
		callId: string; // UUID of the PbxCall
		operatorId: string; // UUID of the Operator
		answeredTimestamp: string; // ISO 8601
		// Include any other fields updated upon answering
		updatedFields: Pick<PbxCall, "status" | "operatorId" | "endedAt">; // endedAt might be null if call just started
	};
}

export interface CallAssignVehicleRequestedPayload extends BaseEventPayload {
	data: {
		callId: string;
		vehicleId: string;
		requestingOperatorId: string; // Operator who made the assignment request
		tenantId: string; // Ensure tenantId is here for the handler
	};
}

// Existing CallVehicleAssignedPayload - ensure it aligns with PbxCall schema
export interface CallVehicleAssignedPayload extends BaseEventPayload {
	data: {
		callId: string;
		vehicleId: string;
		assignedByOperatorId?: string; // User ID of the operator
		assignmentTimestamp: string; // ISO 8601
		estimatedArrivalTimeMinutes?: number;
		// Include relevant fields from PbxCall that are updated
		updatedFields: Pick<PbxCall, "status"> & { vehicles?: string[] }; // Example, adjust as per your PbxCall schema
	};
}

export interface CallEndedPayload extends BaseEventPayload {
	data: {
		callId: string;
		durationSeconds?: number;
		terminationReason?: string;
		endedTimestamp: string; // ISO 8601
		updatedFields: Pick<PbxCall, "status" | "duration" | "endedAt">;
	};
}

// If you still need a generic CallUpdatedPayload for other types of updates:
export interface CallUpdatedPayload extends BaseEventPayload {
	data: {
		callId: string; // UUID of the PbxCall
		updatedFields: Partial<PbxCall>;
		previousFields?: Partial<PbxCall>; // Optional: for audit or complex logic
		updatedByActor: BaseEventPayload["actor"];
	};
}

export interface CallLockRequestedPayload extends BaseEventPayload {
	data: {
		callId: string; // UUID of PbxCall
		operatorId: string; // UUID of Operator requesting lock
	};
}

export interface CallUnlockRequestedPayload extends BaseEventPayload {
	data: {
		callId: string; // UUID of PbxCall
		operatorId: string; // UUID of Operator requesting unlock (should match locker)
	};
}

export interface CallLockedEventPayload extends BaseEventPayload {
	data: {
		callId: string;
		operatorId: string; // Operator who now holds the lock
	};
}

export interface CallLockFailedEventPayload extends BaseEventPayload {
	data: {
		callId: string;
		requestingOperatorId: string;
		currentLockHolderOperatorId?: string;
		reason: string;
	};
}

export interface CallUnlockedEventPayload extends BaseEventPayload {
	data: {
		callId: string;
		operatorId?: string; // Operator who released the lock (if applicable)
	};
}

export interface CallDialRequestedPayload extends BaseEventPayload {
	data: {
		customerPhoneNumber: string;
		operatorExtension?: string; // Operator's line/extension
		relatedCallId?: string; // Optional: if dialing in context of an existing call
		// actor should be the operator initiating the dial
	};
}

export interface CallRemovedPayload extends BaseEventPayload {
	data: {
		callId: string; // UUID of the PbxCall removed
		// actor should be the operator who removed the call
	};
}

export interface CallAssignmentCancelledPayload extends BaseEventPayload {
	data: {
		callId: string; // UUID of the PbxCall
		vehicleId?: string; // Optional: specific vehicle assignment cancelled
		// actor should be the operator who cancelled the assignment
		updatedFields: Pick<PbxCall, "status" | "assignedAt" | "vehicleIdentifiers">; // assignedAt might be nullified
	};
}
