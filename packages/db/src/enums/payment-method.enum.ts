import { pgEnum } from "drizzle-orm/pg-core";

// 1. Define enum values as the single source of truth
const PAYMENT_METHOD_VALUES = ["CARD", "WIRE", "CRYPTO", "INVOICE"] as const;

// 2. Create the Drizzle ORM pgEnum for database schema definition
export const paymentMethodEnum = pgEnum(
	"payment_method",
	PAYMENT_METHOD_VALUES,
);

// 3. Define TypeScript union type for strict type safety
export type PaymentMethodType = (typeof PAYMENT_METHOD_VALUES)[number];

// 4. Define TypeScript string enum for application logic
export enum PaymentMethod {
	CARD = "CARD",
	WIRE = "WIRE",
	CRYPTO = "CRYPTO",
	INVOICE = "INVOICE",
}
