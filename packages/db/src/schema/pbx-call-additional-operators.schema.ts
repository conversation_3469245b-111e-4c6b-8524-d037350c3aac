import { relations } from "drizzle-orm";
import {
	pgTable,
	primaryKey,
	timestamp,
	uuid,
} from "drizzle-orm/pg-core";
import { operators } from "./operators.schema.js";
import { pbxCalls } from "./pbx-call.schema.js";

const pbxCallAdditionalOperatorColumns = {
  pbxCallId: uuid('pbx_call_id')
    .references(() => pbxCalls.id, { onDelete: "cascade" })
    .notNull(),
  operatorId: uuid('operator_id')
    .references(() => operators.id, { onDelete: "restrict" })
    .notNull(),
  assignedAt: timestamp('assigned_at', { withTimezone: true })
    .defaultNow()
    .notNull(),
};

export const pbxCallAdditionalOperators = pgTable(
  'pbx_call_additional_operators',
  pbxCallAdditionalOperatorColumns,
  (table) => ([ // Updated: pgTable constraints should be an array
 primaryKey({ columns: [table.pbxCallId, table.operatorId] }),
  ])
);

export
 const pbxCallAdditionalOperatorsRelations = relations(pbxCallAdditionalOperators, ({ one }) => ({
	pbxCall: one(pbxCalls, { fields: [pbxCallAdditionalOperators.pbxCallId], references: [pbxCalls.id] }),
	operator: one(operators, { fields: [pbxCallAdditionalOperators.operatorId], references: [operators.id] }),
}));