import { pgEnum } from "drizzle-orm/pg-core";

const MESSAGE_VALUES = [
	"TEXT",
	"IMAGE",
	"VOICE",
	"LOCATION",
	"VERIFICATION",
	"DOCUMENT",
	"STICKER",
	"BUTTO<PERSON>",
	"TEMPLAT<PERSON>",
	"INTERACTIVE",
	"CAROUS<PERSON>",
	"GROUP_CHAT",
	"UNKNOWN",
] as const;

export const messagesEnum = pgEnum("message_type", MESSAGE_VALUES);

export type MessagesType = (typeof MESSAGE_VALUES)[number];

export enum Messages {
	TEXT = "TEXT",
	IMAGE = "IMAGE",
	VOICE = "VOICE",
	LOCATION = "LOCATION",
	VERIFICATION = "VERIFICATION",
	DOCUMENT = "DOCUMENT",
	STICKER = "STICKER",
	BUTTON = "BUTTON",
	TEMPLATE = "TEMPLATE",
	INTERACTIVE = "INTERACTIVE",
	CAROUSEL = "CAROUS<PERSON>",
	GROUP_CHAT = "GROUP_CHAT",
	UNKNOWN = "UNKNOWN",
}
