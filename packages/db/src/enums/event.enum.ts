import { pgEnum } from "drizzle-orm/pg-core";

const EVENT_VALUES = [
	"MESSAGE_RECEIVED",
	"MESSAGE_SENT",
	"USER_LINKED",
	"USER_UNLINKED",
	"CONSENT_GRANTED",
	"CONSENT_REVOKED",
	"VERIFICATION_REQUESTED",
	"VERIFICATION_SUCCEEDED",
	"VERIFICATION_FAILED",
	"SESSION_STARTED",
	"SESSION_ENDED",
	"ERROR",
	"WEBHOOK_RECEIVED",
] as const;

export const eventEnum = pgEnum("event_type", EVENT_VALUES);

export type EventType = (typeof EVENT_VALUES)[number];

export enum Event {
	MESSAGE_RECEIVED = "MESSAGE_RECEIVED",
	MESSAGE_SENT = "MESSAGE_SENT",
	USER_LINKED = "USER_LINKED",
	USER_UNLINKED = "USER_UNLINKED",
	CONSENT_GRANTED = "CONSENT_GRANTED",
	CONSENT_REVOKED = "CONSENT_REVOKED",
	VERIFICATION_REQUESTED = "VERIFICATION_REQUESTED",
	VERIFICATION_SUCCEEDED = "VERIFICATION_SUCCEEDED",
	VERIFICATION_FAILED = "VERIFICATION_FAILED",
	SESSION_STARTED = "SESSION_STARTED",
	SESSION_ENDED = "SESSION_ENDED",
	ERROR = "ERROR",
	WEBHOOK_RECEIVED = "WEBHOOK_RECEIVED",
}
