import {
	AdminOnly,
	ModeratorOnly,
} from "@/decorators/role-based-access.decorator.js";
import type { AppBot, AppCommandContext } from "@/types/bot-context.js";
// src/commands/command-handlers.service.ts
import { Injectable, Logger } from "@nestjs/common";

@Injectable()
export class CommandHandlersService {
	private readonly logger = new Logger(CommandHandlersService.name);

	private botInstance!: AppBot;

	public initialize(bot: AppBot): void {
		this.botInstance = bot;
	}

	registerHandlers(): void {
		this.logger.log("Registering command handlers");

		// Public commands - available to all users
		// Use type assertion to handle complex GramIO typing
		this.botInstance.command("start", (ctx) =>
			this.handleStartCommand(ctx as AppCommandContext),
		);

		// Admin-only commands
		this.botInstance.command("admin", async (ctx) => {
			const context = ctx as AppCommandContext;
			if (!context.userRoles?.includes("admin")) {
				await context.send(context.t("errors.accessDenied"));
				return;
			}
			await this.handleAdminCommand(context);
		});

		// Moderator commands
		this.botInstance.command("mod", async (ctx) => {
			const context = ctx as AppCommandContext;
			if (!context.userRoles?.some((r) => ["admin", "moderator"].includes(r))) {
				await context.send(context.t("errors.accessDenied"));
				return;
			}
			await this.handleModeratorCommand(context);
		});
	}

	async handleStartCommand(
		context: AppCommandContext, // Use AppCommandContext
	): Promise<void> {
		this.logger.log(
			`[start] Handling /start command from user ${context.from?.id} for tenant ${context.tenantId || "unknown"}`,
		);

		// Check if we have required context
		if (!context.tenantId || !context.tenantBotApiClient) {
			this.logger.error("Missing required context in handleStartCommand");
			if (context.chat?.id) {
				await context.tenantBotApiClient?.sendMessage({
					chat_id: context.chat.id,
					text: context.t("errors.missingContext"),
				});
			}
			return;
		}

		const userName = context.from?.firstName || "User";
		const greetingMessage = context.t("commands.start.greeting", userName);

		if (!context.tenantBotApiClient) {
			throw new Error("tenantBotApiClient is not available in context");
		}
		await context.tenantBotApiClient.sendMessage({
			chat_id: context.chat.id,
			text: greetingMessage,
			parse_mode: "HTML", // Ensure format tags work
		});

		// If the start command could also lead into a scene:
		// if (context.args === 'profile_setup') {
		//   await context.scene.enter('profileSetupScene');
		// }
	}

	/**
	 * Handle the /admin command
	 * This command is only available to users with the admin role
	 */
	@AdminOnly()
	async handleAdminCommand(context: AppCommandContext): Promise<void> {
		this.logger.log(
			`[admin] Handling /admin command from user ${context.from?.id} for tenant ${context.tenantId || "unknown"}`,
		);

		// Check if we have required context
		if (!context.tenantId || !context.t) {
			this.logger.error(
				"Missing tenant context or t function in handleAdminCommand. Cannot send message.",
			);
			return;
		}

		const panelMessage = context.t(
			"commands.admin.panel",
			context.tenant?.name || context.tenantId,
			context.userRoles?.join(", ") ?? "",
		);

		await context.tenantBotApiClient.sendMessage({
			chat_id: context.chat.id,
			text: panelMessage,
			parse_mode: "HTML",
		});
	}

	/**
	 * Handle the /mod command
	 * This command is only available to users with the moderator or admin role
	 */
	@ModeratorOnly()
	async handleModeratorCommand(context: AppCommandContext): Promise<void> {
		this.logger.log(
			`[mod] Handling /mod command from user ${context.from?.id} for tenant ${context.tenantId || "unknown"}`,
		);

		// Check if we have required context
		if (!context.tenantId || !context.t) {
			this.logger.error(
				"Missing tenant context or t function in handleModeratorCommand. Cannot send message.",
			);
			return;
		}

		// Use the tenant-specific bot API client
		await context.tenantBotApiClient.sendMessage({
			chat_id: context.chat.id,
			text: `Moderator panel for tenant ${context.tenant?.name || context.tenantId}. Your roles: ${context.userRoles?.join(", ")}`,
		});
	}
}
