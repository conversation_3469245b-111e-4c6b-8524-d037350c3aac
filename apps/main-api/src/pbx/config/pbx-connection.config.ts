// DTO/Interface for PBX connection details from DB/env
// apps/main-api/src/pbx/config/pbx-connection.config.ts
// (Alternatively, and better, define these in @repo/types/src/pbx/ if shared or used by Drizzle $type)

export interface AriConnectionDetails {
  type: 'ASTERISK_ARI';
  url: string; // e.g., http://localhost:8088 (base URL for REST, WS will be derived)
  username?: string;
  password?: string;
  appName: string; // Stasis application name to connect to for events
}

export interface FreePbxGraphqlConnectionDetails {
  type: 'FREEPBX_GRAPHQL';
  apiUrl: string; // e.g., https://freepbx.example.com/graphql
  clientId?: string;
  clientSecret?: string;
  tokenUrl?: string; // If OAuth2 client credentials grant is used
}

export type PbxConnectionDetails = AriConnectionDetails | FreePbxGraphqlConnectionDetails;

export interface TenantRoutingRule {
  type: 'DID' | 'EXTENSION_PREFIX' | 'QUEUE_NAME' | 'CHANNEL_VAR_MATCH';
  // For DID, EXTENSION_PREFIX, QUEUE_NAME:
  pattern?: string; // e.g., "1234567" for DID, "1XX" for extension, "sales_queue" for queue
  // For CHANNEL_VAR_MATCH:
  variableName?: string; // e.g., "X-Tenant-ID"
  variableValue?: string; // Optional: match specific value, if not present, just existence of varName is enough
  tenantId: string; // UUID of the tenant this rule maps to
  priority?: number; // Lower numbers processed first
}

// This interface can be used with Drizzle's .$type<>() on the pbx_instances schema
export interface PbxInstanceConfigurable {
  id: string;
  name: string;
  type: 'ASTERISK_ARI' | 'FREEPBX_GRAPHQL';
  isEnabled: boolean;
  connectionDetails: PbxConnectionDetails;
  tenantAssociationType: 'SINGLE_TENANT' | 'MULTI_TENANT_DID' | 'MULTI_TENANT_CONTEXT_VAR' | 'MULTI_TENANT_QUEUE';
  defaultTenantId?: string | null;
  tenantRoutingRules?: TenantRoutingRule[] | null;
}