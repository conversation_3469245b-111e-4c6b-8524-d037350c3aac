{"name": "@repo/monorepo", "private": true, "packageManager": "bun@1.2.3", "workspaces": ["apps/*", "packages/*"], "scripts": {"build": "turbo build", "dev": "turbo dev", "start": "turbo start", "test": "turbo test", "lint": "turbo lint", "typecheck": "turbo typecheck", "clean": "turbo clean", "format": "turbo format", "format:check": "biome check .", "format:fix": "biome check . --apply", "lint:biome": "biome lint .", "lint:biome:fix": "biome lint . --apply", "format:biome": "biome format .", "format:biome:fix": "biome format . --write", "start:main-api": "turbo start --filter=@repo/main-api", "start:dashboard": "turbo start --filter=@repo/dashboard", "start:mini-app": "turbo start --filter=@repo/mini-app", "dev:main-api": "turbo dev --filter=@repo/main-api", "dev:dashboard": "turbo dev --filter=@repo/dashboard", "dev:mini-app": "turbo dev --filter=@repo/mini-app", "build:main-api": "turbo build --filter=@repo/main-api", "build:dashboard": "turbo build --filter=@repo/dashboard", "build:mini-app": "turbo build --filter=@repo/mini-app", "db:build": "turbo build --filter=@repo/db", "db:migrations:generate": "turbo build --filter=@repo/db && turbo _generate_and_apply_migrations_interactive --filter=@repo/db", "db:migrations:apply": "turbo _apply_migrations --filter=@repo/db", "db:seed": "turbo _seed_database --filter=@repo/db", "db:reset:full": "echo 'INFO: Clearing old migration files (if any)...' && rm -rf packages/db/drizzle && bun run db:reset:drop-recreate && echo 'INFO: Generating initial migrations...' && bun run db:migrations:generate && echo 'INFO: Seeding database...' && bun run db:seed", "db:reset:drop-recreate": "turbo _drop_recreate_db --filter=@repo/db", "db:kit:generate": "turbo kit:generate --filter=@repo/db", "db:kit:push": "turbo kit:push --filter=@repo/db", "db:kit:studio": "turbo kit:studio --filter=@repo/db", "db:check:exports": "turbo check:exports --filter=@repo/db", "test:integration": "echo 'Running integration tests...' && bun test --filter='*.integration.*'", "test:performance": "echo 'Running performance tests...' && bun test --filter='*.performance.*'", "test:coverage": "echo 'Running test coverage...' && bun test --coverage", "docker:build": "./docker-build.sh build", "docker:up": "./docker-build.sh start", "docker:down": "./docker-build.sh stop", "docker:clean": "./docker-build.sh clean", "docker:logs": "./docker-build.sh logs", "docker:status": "./docker-build.sh status", "ci": "./docker-build.sh ci", "ci:local": "bun run lint && bun run typecheck && bun run format:check && bun run build && bun run test", "prepare": "echo 'Setting up development environment...'", "postinstall": "echo 'Dependencies installed successfully!'"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@swc/core": "^1.11.24", "swc-loader": "^0.2.6", "turbo": "^2.5.4"}, "dependencies": {"@types/node": "^22.15.1"}, "trustedDependencies": ["@biomejs/biome", "@nestjs/core", "@swc/core", "core-js", "esbuild", "nest<PERSON><PERSON>-pino", "sharp", "unrs-resolver"]}