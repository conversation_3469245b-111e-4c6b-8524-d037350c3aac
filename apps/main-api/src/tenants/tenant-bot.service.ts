import type { AppBotErrorDefinitions } from "@/types/bot-context.js";
import { Injectable, Logger } from "@nestjs/common";
import { db } from "@repo/db";
import { Bot } from "gramio";

/**
 * Service responsible for managing tenant-specific bot instances
 */
@Injectable()
export class TenantBotService {
	private readonly logger = new Logger(TenantBotService.name);
	private tenantBotInstances: Map<string, Bot<AppBotErrorDefinitions>> =
		new Map();

	/**
	 * Get a bot instance for a specific tenant
	 * @param tenantId The tenant ID
	 * @returns The bot instance for the tenant
	 */
	async getBotForTenant(
		tenantId: string,
	): Promise<Bot<AppBotErrorDefinitions> | null> {
		// Check if we already have an instance for this tenant
		if (this.tenantBotInstances.has(tenantId)) {
			const botInstance = this.tenantBotInstances.get(tenantId);
			if (botInstance) {
				return botInstance;
			}
		}

		// If not, try to create one
		try {
			// Get the tenant bot configuration from the database
			// Using SQL query instead of the query builder due to schema import issues
			const result = await db.execute(
				`SELECT * FROM bots WHERE tenant_id = '${tenantId}' LIMIT 1`,
			);
			const tenantBot = result.length > 0 ? result[0] : null;

			if (!tenantBot) {
				this.logger.warn(`No bot configuration found for tenant ${tenantId}`);
				return null;
			}

			// Create a new bot instance for this tenant
			if (!tenantBot?.token || typeof tenantBot.token !== "string") {
				this.logger.error(`Invalid token for tenant ${tenantId}`);
				return null;
			}
			const botInstance = new Bot<AppBotErrorDefinitions>(tenantBot.token);

			// Store the instance for future use
			this.tenantBotInstances.set(tenantId, botInstance);

			return botInstance;
		} catch (error) {
			this.logger.error(`Failed to get bot for tenant ${tenantId}:`, error);
			return null;
		}
	}

	/**
	 * Get all active tenant bots
	 * @returns Array of tenant bot configurations
	 */
	async getAllActiveTenantBots() {
		try {
			// Using SQL query instead of the query builder due to schema import issues
			const result = await db.execute(
				"SELECT * FROM bots WHERE is_enabled = true",
			);
			return result;
		} catch (error) {
			this.logger.error("Failed to get active tenant bots:", error);
			return [];
		}
	}
}
