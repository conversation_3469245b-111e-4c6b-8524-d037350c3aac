// packages/types/src/events/call-ride.events.ts
import type { BaseEventPayload } from './base-event.payload.js';
import type { PbxCall, rideOrder, User, Operator, vehicles, ScheduledCall } from '@repo/db'; // Drizzle types

   // --- PbxCall Domain Events ---
   export type PbxCallCreatedDomainEvent = BaseEventPayload<PbxCall & { // The full PbxCall object from DB
     customerUser?: Pick<User, 'id' | 'display_name' | 'phone' >; // Optional enrichment
   }>;
   // Routing Key: call.pbxcall.created.<tenantId>.<pbxCallId>

   export type PbxCallUpdatedDomainEvent = BaseEventPayload<{
     callId: string;
     updatedFields: Partial<PbxCall>;
     previousFields?: Partial<PbxCall>; // For detailed auditing or conditional logic
   }>;
   // Routing Key: call.pbxcall.updated.<tenantId>.<pbxCallId>

   export type PbxCallStatusChangedDomainEvent = BaseEventPayload<{
     callId: string;
     newStatus: PbxCall['status'];
     oldStatus?: PbxCall['status'];
     timestamp: string; // Time of this specific status change
     reason?: string;
   }>;
   // Routing Key: call.pbxcall.status_changed.<tenantId>.<pbxCallId>

   export type PbxCallVehicleAssignedDomainEvent = BaseEventPayload<{
     callId: string;
     vehicleIds: Array<typeof vehicles.$inferSelect['id']>; // Array of vehicle UUIDs from your DB
     assignedByOperatorId?: Operator['id'];
     assignedAt: string; // ISO 8601
     etaMinutes?: number;
   }>;
   // Routing Key: call.pbxcall.vehicle_assigned.<tenantId>.<callId>

   // --- ScheduledCall Domain Events ---
   export type ScheduledCallCreatedDomainEvent = BaseEventPayload<ScheduledCall>;
   // Routing Key: schedule.scheduled_call.created.<tenantId>.<scheduleId>

   export type ScheduledCallActivatedDomainEvent = BaseEventPayload<{
     scheduledCallId: string;
     activatedPbxCallId: string; // ID of the PbxCall record created upon activation
     originalScheduleDetails: ScheduledCall;
   }>;
   // Routing Key: schedule.scheduled_call.activated.<tenantId>.<scheduleId>

   // --- RideOrder Domain Events (Examples) ---
   export type RideOrderRequestedDomainEvent = BaseEventPayload<typeof rideOrder.$inferSelect>; // Initial request
   export type RideOrderDriverAssignedDomainEvent = BaseEventPayload<typeof rideOrder.$inferSelect & { driverDetails: User, vehicleDetails: typeof vehicles.$inferSelect }>;
   export type RideOrderCompletedDomainEvent = BaseEventPayload<typeof rideOrder.$inferSelect>;

   // ... many more domain events as your system evolves ...