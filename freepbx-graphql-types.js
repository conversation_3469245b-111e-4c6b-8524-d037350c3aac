const fetch = require('node-fetch');
const fs = require('node:fs');
const path = require('node:path');

// Your OAuth2 credentials
const CLIENT_ID = 'aa8751a9c1b8015ae3502f995be79fd8bab66dacbc3610da7a6856a840523080';
const CLIENT_SECRET = 'b2952b5278b67ced79a6a6da6ed04ffe';
const FREEPBX_SERVER = 'http://192.168.1.103'; // Your FreePBX server

class FreePBXAPIClient {
  constructor(serverUrl, clientId, clientSecret) {
    this.serverUrl = serverUrl;
    this.clientId = clientId;
    this.clientSecret = clientSecret;
    this.accessToken = null;
  }

  async authenticate() {
    try {
      const response = await fetch(`${this.serverUrl}/admin/api/api/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams({
          grant_type: 'client_credentials',
          client_id: this.clientId,
          client_secret: this.clientSecret,
        }),
      });

      const data = await response.json();
      
      if (data.access_token) {
        this.accessToken = data.access_token;
        console.log('Authentication successful!');
        console.log(`Token expires in: ${data.expires_in} seconds`);
        return this.accessToken;
      } else {
        throw new Error('Failed to get access token: ' + JSON.stringify(data));
      }
    } catch (error) {
      console.error('Authentication error:', error);
      throw error;
    }
  }

  async downloadGraphQLSchema() {
    if (!this.accessToken) {
      await this.authenticate();
    }

    const introspectionQuery = `
      query IntrospectionQuery {
        __schema {
          queryType { name }
          mutationType { name }
          subscriptionType { name }
          types {
            ...FullType
          }
          directives {
            name
            description
            locations
            args {
              ...InputValue
            }
          }
        }
      }

      fragment FullType on __Type {
        kind
        name
        description
        fields(includeDeprecated: true) {
          name
          description
          args {
            ...InputValue
          }
          type {
            ...TypeRef
          }
          isDeprecated
          deprecationReason
        }
        inputFields {
          ...InputValue
        }
        interfaces {
          ...TypeRef
        }
        enumValues(includeDeprecated: true) {
          name
          description
          isDeprecated
          deprecationReason
        }
        possibleTypes {
          ...TypeRef
        }
      }

      fragment InputValue on __InputValue {
        name
        description
        type { ...TypeRef }
        defaultValue
      }

      fragment TypeRef on __Type {
        kind
        name
        ofType {
          kind
          name
          ofType {
            kind
            name
            ofType {
              kind
              name
              ofType {
                kind
                name
                ofType {
                  kind
                  name
                  ofType {
                    kind
                    name
                    ofType {
                      kind
                      name
                    }
                  }
                }
              }
            }
          }
        }
      }
    `;

    try {
      const response = await fetch(`${this.serverUrl}/admin/api/api/gql`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.accessToken}`,
        },
        body: JSON.stringify({ query: introspectionQuery }),
      });

      const schema = await response.json();
      
      if (schema.data) {
        // Save raw schema
        fs.writeFileSync('freepbx-graphql-schema.json', JSON.stringify(schema, null, 2));
        console.log('GraphQL schema downloaded successfully to freepbx-graphql-schema.json');
        
        // Generate TypeScript types
        await this.generateGraphQLTypes(schema.data.__schema);
        
        return schema;
      } else {
        throw new Error('Failed to download GraphQL schema: ' + JSON.stringify(schema));
      }
    } catch (error) {
      console.error('GraphQL schema download error:', error);
      throw error;
    }
  }

  async downloadSwaggerAPI() {
    if (!this.accessToken) {
      await this.authenticate();
    }

    try {
      // Try common Swagger/OpenAPI documentation endpoints
      const swaggerEndpoints = [
        `${this.serverUrl}/admin/api/api/swagger.json`,
        `${this.serverUrl}/admin/api/api/openapi.json`,
        `${this.serverUrl}/admin/api/api/docs.json`,
        `${this.serverUrl}/admin/api/api/swagger`,
        `${this.serverUrl}/admin/api/api/docs`,
        `${this.serverUrl}/admin/api/swagger.json`,
        `${this.serverUrl}/admin/api/openapi.json`,
      ];

      let swaggerData = null;
      let successUrl = null;

      for (const endpoint of swaggerEndpoints) {
        try {
          console.log(`Trying Swagger endpoint: ${endpoint}`);
          const response = await fetch(endpoint, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${this.accessToken}`,
              'Accept': 'application/json',
            },
          });

          console.log(`Response status: ${response.status} for ${endpoint}`);
          
          if (response.ok) {
            const contentType = response.headers.get('content-type');
            const text = await response.text();
            
            // Check if response looks like JSON
            if (text.trim().startsWith('{') || text.trim().startsWith('[')) {
              try {
                swaggerData = JSON.parse(text);
                // Validate it's actually swagger/openapi
                if (swaggerData.swagger || swaggerData.openapi || swaggerData.paths) {
                  successUrl = endpoint;
                  break;
                }
              } catch (e) {
                console.log(`Failed to parse JSON from ${endpoint}`);
              }
            } else {
              console.log(`Endpoint ${endpoint} returned non-JSON data (HTML/text)`);
            }
          } else {
            console.log(`HTTP ${response.status}: ${response.statusText} for ${endpoint}`);
          }
        } catch (error) {
          console.log(`Failed to fetch from ${endpoint}: ${error.message}`);
        }
      }

      if (swaggerData) {
        // Save raw Swagger/OpenAPI spec
        fs.writeFileSync('freepbx-swagger-api.json', JSON.stringify(swaggerData, null, 2));
        console.log(`Swagger API documentation downloaded successfully from ${successUrl} to freepbx-swagger-api.json`);
        
        // Generate TypeScript types
        await this.generateSwaggerTypes(swaggerData);
        
        return swaggerData;
      } else {
        // If no swagger endpoint works, try to discover available endpoints
        console.log('No Swagger documentation found, attempting to discover available API endpoints...');
        return await this.discoverAvailableEndpoints();
      }
    } catch (error) {
      console.error('Swagger API download error:', error);
      throw error;
    }
  }

  async discoverAvailableEndpoints() {
    try {
      // Try to get information about available endpoints
      const discoveryEndpoints = [
        `${this.serverUrl}/admin/api/api`,
        `${this.serverUrl}/admin/api`,
        `${this.serverUrl}/admin/api/api/info`,
        `${this.serverUrl}/admin/api/api/health`,
      ];

      let discoveryData = {};
      const workingEndpoints = [];

      for (const endpoint of discoveryEndpoints) {
        try {
          console.log(`Trying discovery endpoint: ${endpoint}`);
          const response = await fetch(endpoint, {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${this.accessToken}`,
              'Accept': 'application/json',
            },
          });

          console.log(`Discovery response status: ${response.status} for ${endpoint}`);
          
          if (response.ok) {
            const text = await response.text();
            console.log(`Response from ${endpoint} (first 200 chars): ${text.substring(0, 200)}`);
            
            workingEndpoints.push({
              url: endpoint,
              status: response.status,
              contentType: response.headers.get('content-type'),
              responsePreview: text.substring(0, 500)
            });

            // Try to parse as JSON
            if (text.trim().startsWith('{') || text.trim().startsWith('[')) {
              try {
                const jsonData = JSON.parse(text);
                discoveryData[endpoint] = jsonData;
              } catch (e) {
                // Not JSON, store as text
                discoveryData[endpoint] = { raw: text };
              }
            } else {
              discoveryData[endpoint] = { raw: text };
            }
          }
        } catch (error) {
          console.log(`Failed to fetch from ${endpoint}: ${error.message}`);
        }
      }

      // Create a comprehensive discovery result
      const apiDiscovery = {
        info: {
          title: 'FreePBX API Discovery',
          version: '1.0.0',
          description: 'Discovered FreePBX API endpoints and capabilities',
          discoveredAt: new Date().toISOString()
        },
        servers: [
          { url: `${this.serverUrl}/admin/api/api/gql`, description: 'GraphQL API' },
          { url: `${this.serverUrl}/admin/api/api`, description: 'Base API' }
        ],
        discovery: {
          workingEndpoints,
          responseData: discoveryData,
          recommendations: [
            'GraphQL API is available and working',
            'REST API documentation may not be available via standard endpoints',
            'Consider using GraphQL introspection for API exploration'
          ]
        },
        paths: this.generateBasicRestPaths(),
        discovered: true
      };

      fs.writeFileSync('freepbx-api-discovery.json', JSON.stringify(apiDiscovery, null, 2));
      console.log('API discovery results saved to freepbx-api-discovery.json');

      // Generate basic TypeScript types based on discovery
      await this.generateDiscoveryTypes(apiDiscovery);

      return apiDiscovery;
    } catch (error) {
      console.error('API discovery error:', error);
      throw error;
    }
  }

  generateBasicRestPaths() {
    // Generate some common REST paths that might exist
    return {
      '/extensions': {
        get: {
          summary: 'Get all extensions',
          operationId: 'getExtensions'
        },
        post: {
          summary: 'Create extension',
          operationId: 'createExtension'
        }
      },
      '/extensions/{id}': {
        get: {
          summary: 'Get extension by ID',
          operationId: 'getExtension'
        },
        put: {
          summary: 'Update extension',
          operationId: 'updateExtension'
        },
        delete: {
          summary: 'Delete extension',
          operationId: 'deleteExtension'
        }
      },
      '/calls': {
        get: {
          summary: 'Get call logs',
          operationId: 'getCalls'
        }
      },
      '/system/info': {
        get: {
          summary: 'Get system information',
          operationId: 'getSystemInfo'
        }
      }
    };
  }

  async generateDiscoveryTypes(discoveryData) {
    let typeDefs = `// Generated API Discovery TypeScript definitions for FreePBX
// Generated on: ${new Date().toISOString()}
// Note: These are estimated types based on API discovery

`;

    // Add discovery info
    typeDefs += `// API Discovery Results
// Working endpoints found: ${discoveryData.discovery.workingEndpoints.length}
// GraphQL API: Available
// REST API: Documentation not found via standard endpoints

`;

    // Generate basic types
    typeDefs += `// Basic FreePBX Types (estimated)
export interface Extension {
  id: string | number;
  extension: string;
  name: string;
  tech?: string;
  secret?: string;
  context?: string;
  voicemail?: string;
}

export interface CallLog {
  id: string | number;
  src: string;
  dst: string;
  dcontext: string;
  clid: string;
  channel: string;
  dstchannel: string;
  lastapp: string;
  lastdata: string;
  start: string;
  answer: string;
  end: string;
  duration: number;
  billsec: number;
  disposition: string;
  amaflags: number;
}

export interface SystemInfo {
  version: string;
  uptime: string;
  load: string[];
  memory: {
    total: number;
    used: number;
    free: number;
  };
}

// API Client Interface
export interface FreePBXAPIClient {
  // GraphQL methods
  query<T = any>(query: string, variables?: Record<string, any>): Promise<{ data?: T; errors?: any[] }>;
  
  // REST methods (estimated endpoints)
  getExtensions(): Promise<Extension[]>;
  getExtension(id: string | number): Promise<Extension>;
  createExtension(data: Partial<Extension>): Promise<Extension>;
  updateExtension(id: string | number, data: Partial<Extension>): Promise<Extension>;
  deleteExtension(id: string | number): Promise<boolean>;
  
  getCalls(): Promise<CallLog[]>;
  getSystemInfo(): Promise<SystemInfo>;
  
  // Generic REST method
  restRequest<T = any>(endpoint: string, method?: string, data?: any): Promise<T>;
}

// Discovery Results Interface
export interface APIDiscoveryResult {
  info: {
    title: string;
    version: string;
    description: string;
    discoveredAt: string;
  };
  servers: Array<{
    url: string;
    description: string;
  }>;
  discovery: {
    workingEndpoints: Array<{
      url: string;
      status: number;
      contentType: string | null;
      responsePreview: string;
    }>;
    responseData: Record<string, any>;
    recommendations: string[];
  };
  paths: Record<string, any>;
  discovered: boolean;
}
`;

    fs.writeFileSync('freepbx-discovery-types.ts', typeDefs);
    console.log('API Discovery TypeScript definitions generated: freepbx-discovery-types.ts');
  }

  async generateGraphQLTypes(schema) {
    let typeDefs = `// Generated GraphQL TypeScript definitions for FreePBX
// Generated on: ${new Date().toISOString()}

`;

    // Generate basic GraphQL types
    if (schema.types) {
      for (const type of schema.types) {
        if (type.name && !type.name.startsWith('__')) {
          typeDefs += this.generateGraphQLTypeDefinition(type);
        }
      }
    }

    // Add client interface
    typeDefs += `
export interface GraphQLClient {
  query<T = any>(query: string, variables?: Record<string, any>): Promise<{ data?: T; errors?: any[] }>;
}

export interface FreePBXGraphQLSchema {
  Query: ${schema.queryType ? schema.queryType.name : 'any'};
  Mutation: ${schema.mutationType ? schema.mutationType.name : 'any'};
  Subscription: ${schema.subscriptionType ? schema.subscriptionType.name : 'any'};
}
`;

    fs.writeFileSync('freepbx-graphql-types.ts', typeDefs);
    console.log('GraphQL TypeScript definitions generated: freepbx-graphql-types.ts');
  }

  generateGraphQLTypeDefinition(type) {
    if (!type.name) return '';

    let typeDef = `\n// ${type.description || type.name}\n`;

    switch (type.kind) {
      case 'OBJECT':
        typeDef += `export interface ${type.name} {\n`;
        if (type.fields) {
          for (const field of type.fields) {
            const fieldType = this.getGraphQLFieldType(field.type);
            const optional = fieldType.includes('null') ? '?' : '';
            typeDef += `  ${field.name}${optional}: ${fieldType};\n`;
          }
        }
        typeDef += `}\n`;
        break;

      case 'ENUM':
        typeDef += `export enum ${type.name} {\n`;
        if (type.enumValues) {
          for (const enumValue of type.enumValues) {
            typeDef += `  ${enumValue.name} = "${enumValue.name}",\n`;
          }
        }
        typeDef += `}\n`;
        break;

      case 'INPUT_OBJECT':
        typeDef += `export interface ${type.name}Input {\n`;
        if (type.inputFields) {
          for (const field of type.inputFields) {
            const fieldType = this.getGraphQLFieldType(field.type);
            const optional = fieldType.includes('null') ? '?' : '';
            typeDef += `  ${field.name}${optional}: ${fieldType};\n`;
          }
        }
        typeDef += `}\n`;
        break;

      default:
        // Skip scalar and other types for now
        return '';
    }

    return typeDef;
  }

  getGraphQLFieldType(type) {
    if (!type) return 'any';

    switch (type.kind) {
      case 'NON_NULL':
        return this.getGraphQLFieldType(type.ofType);
      case 'LIST':
        return `${this.getGraphQLFieldType(type.ofType)}[]`;
      case 'SCALAR':
        switch (type.name) {
          case 'String': return 'string';
          case 'Int': return 'number';
          case 'Float': return 'number';
          case 'Boolean': return 'boolean';
          case 'ID': return 'string';
          default: return 'any';
        }
      case 'OBJECT':
      case 'ENUM':
      case 'INPUT_OBJECT':
        return type.name || 'any';
      default:
        return 'any';
    }
  }

  async generateSwaggerTypes(swaggerData) {
    let typeDefs = `// Generated REST API TypeScript definitions for FreePBX
// Generated on: ${new Date().toISOString()}

`;

    // Add basic API info
    if (swaggerData.info) {
      typeDefs += `// API: ${swaggerData.info.title || 'FreePBX REST API'}
// Version: ${swaggerData.info.version || '1.0.0'}
// Description: ${swaggerData.info.description || 'FreePBX REST API'}

`;
    }

    // Generate types from definitions/components
    if (swaggerData.definitions) {
      typeDefs += this.generateSwaggerDefinitions(swaggerData.definitions);
    } else if (swaggerData.components && swaggerData.components.schemas) {
      typeDefs += this.generateSwaggerDefinitions(swaggerData.components.schemas);
    }

    // Generate API endpoints interface
    if (swaggerData.paths) {
      typeDefs += this.generateAPIEndpoints(swaggerData.paths);
    }

    // Add client interface
    typeDefs += `
export interface RestAPIClient {
  get<T = any>(path: string, params?: Record<string, any>): Promise<T>;
  post<T = any>(path: string, data?: any): Promise<T>;
  put<T = any>(path: string, data?: any): Promise<T>;
  delete<T = any>(path: string): Promise<T>;
}

export interface FreePBXRestAPI {
  baseUrl: string;
  version: string;
  endpoints: APIEndpoints;
}
`;

    fs.writeFileSync('freepbx-rest-types.ts', typeDefs);
    console.log('REST API TypeScript definitions generated: freepbx-rest-types.ts');
  }

  generateSwaggerDefinitions(definitions) {
    let typeDefs = '// Data Models\n\n';

    for (const [name, definition] of Object.entries(definitions)) {
      typeDefs += `export interface ${name} {\n`;
      
      if (definition.properties) {
        for (const [propName, propDef] of Object.entries(definition.properties)) {
          const isRequired = definition.required && definition.required.includes(propName);
          const optional = isRequired ? '' : '?';
          const type = this.getSwaggerPropertyType(propDef);
          
          typeDefs += `  ${propName}${optional}: ${type};\n`;
        }
      }
      
      typeDefs += `}\n\n`;
    }

    return typeDefs;
  }

  generateAPIEndpoints(paths) {
    let endpointDefs = '// API Endpoints\n\nexport interface APIEndpoints {\n';

    for (const [path, methods] of Object.entries(paths)) {
      for (const [method, operation] of Object.entries(methods)) {
        if (typeof operation === 'object' && operation.operationId) {
          const operationName = operation.operationId || `${method}_${path.replace(/[\/{}]/g, '_')}`;
          endpointDefs += `  ${operationName}: {\n`;
          endpointDefs += `    method: '${method.toUpperCase()}';\n`;
          endpointDefs += `    path: '${path}';\n`;
          
          if (operation.summary) {
            endpointDefs += `    summary: '${operation.summary}';\n`;
          }
          
          endpointDefs += `  };\n`;
        }
      }
    }

    endpointDefs += '}\n\n';
    return endpointDefs;
  }

  getSwaggerPropertyType(property) {
    if (!property) return 'any';

    switch (property.type) {
      case 'string':
        return property.enum ? `'${property.enum.join("' | '")}'` : 'string';
      case 'number':
      case 'integer':
        return 'number';
      case 'boolean':
        return 'boolean';
      case 'array':
        const itemType = property.items ? this.getSwaggerPropertyType(property.items) : 'any';
        return `${itemType}[]`;
      case 'object':
        return 'Record<string, any>';
      default:
        if (property.$ref) {
          // Extract type name from $ref
          const refParts = property.$ref.split('/');
          return refParts[refParts.length - 1];
        }
        return 'any';
    }
  }

  async query(graphqlQuery) {
    if (!this.accessToken) {
      await this.authenticate();
    }

    try {
      const response = await fetch(`${this.serverUrl}/admin/api/api/gql`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.accessToken}`,
        },
        body: JSON.stringify({ query: graphqlQuery }),
      });

      return await response.json();
    } catch (error) {
      console.error('GraphQL query error:', error);
      throw error;
    }
  }

  async restRequest(endpoint, method = 'GET', data = null) {
    if (!this.accessToken) {
      await this.authenticate();
    }

    try {
      const options = {
        method,
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json',
        },
      };

      if (data && method !== 'GET') {
        options.body = JSON.stringify(data);
      }

      // Try multiple possible REST API base paths
      const basePaths = [
        `${this.serverUrl}/admin/api/api/rest`,
        `${this.serverUrl}/admin/api/api`,
        `${this.serverUrl}/admin/api/rest`,
      ];

      let lastError = null;

      for (const basePath of basePaths) {
        try {
          const url = `${basePath}${endpoint.startsWith('/') ? endpoint : '/' + endpoint}`;
          console.log(`Trying REST request: ${method} ${url}`);
          
          const response = await fetch(url, options);
          
          if (response.ok) {
            const contentType = response.headers.get('content-type');
            if (contentType && contentType.includes('application/json')) {
              return await response.json();
            } else {
              const text = await response.text();
              // Try to parse as JSON even if content-type is wrong
              try {
                return JSON.parse(text);
              } catch (e) {
                return { success: true, data: text, contentType };
              }
            }
          } else {
            const errorText = await response.text();
            console.log(`HTTP ${response.status} from ${url}: ${errorText.substring(0, 200)}`);
            
            // If we get a structured error, parse it
            if (errorText.trim().startsWith('{')) {
              try {
                const errorJson = JSON.parse(errorText);
                lastError = new Error(`HTTP ${response.status}: ${errorJson.message || errorJson.error || 'Unknown error'}`);
              } catch (e) {
                lastError = new Error(`HTTP ${response.status}: ${errorText.substring(0, 100)}`);
              }
            } else {
              lastError = new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
          }
        } catch (error) {
          console.log(`Request failed for ${basePath}: ${error.message}`);
          lastError = error;
        }
      }

      throw lastError || new Error('All REST API base paths failed');
    } catch (error) {
      console.error('REST API request error:', error.message);
      throw error;
    }
  }
}

// Usage example
async function main() {
  const client = new FreePBXAPIClient(FREEPBX_SERVER, CLIENT_ID, CLIENT_SECRET);
  
  try {
    console.log('🔐 Authenticating...');
    await client.authenticate();
    
    console.log('\n📊 Downloading GraphQL schema...');
    await client.downloadGraphQLSchema();
    
    console.log('\n🔄 Downloading REST API documentation...');
    await client.downloadSwaggerAPI();
    
    console.log('\n✅ API documentation and TypeScript types generated successfully!');
    console.log('\nGenerated files:');
    console.log('- freepbx-graphql-schema.json (Raw GraphQL schema)');
    console.log('- freepbx-graphql-types.ts (GraphQL TypeScript types)');
    console.log('- freepbx-api-discovery.json (API discovery results)');
    console.log('- freepbx-discovery-types.ts (Discovery-based TypeScript types)');
    if (fs.existsSync('freepbx-swagger-api.json')) {
      console.log('- freepbx-swagger-api.json (REST API documentation)');
      console.log('- freepbx-rest-types.ts (REST API TypeScript types)');
    }
    
    // Test GraphQL query
    console.log('\n🧪 Testing GraphQL...');
    const gqlResult = await client.query('{ __typename }');
    console.log('GraphQL test result:', gqlResult);
    
    // Test REST API discovery
    console.log('\n🧪 Testing REST API discovery...');
    try {
      // Try a few basic REST endpoints to see what's available
      const testEndpoints = [
        '/system',
        '/extensions', 
        '/trunks',
        '/routes',
        '/users'
      ];

      for (const endpoint of testEndpoints) {
        try {
          const result = await client.restRequest(endpoint, 'GET');
          console.log(`✅ ${endpoint}: Available`);
          if (result && typeof result === 'object') {
            console.log(`   Response type: ${Array.isArray(result) ? 'Array' : 'Object'}`);
            if (Array.isArray(result)) {
              console.log(`   Items count: ${result.length}`);
            }
          }
        } catch (error) {
          console.log(`❌ ${endpoint}: ${error.message}`);
        }
      }
    } catch (error) {
      console.log('REST API discovery failed:', error.message);
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  }
}

// Run the example
if (require.main === module) {
  main();
}

module.exports = FreePBXAPIClient;