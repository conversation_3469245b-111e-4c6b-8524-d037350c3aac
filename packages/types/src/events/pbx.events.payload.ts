import type { CallDirectionDb } from '@repo/db'; // Drizzle enums
import type { FreePbxCdr } from '../pbx/cdr.types.payload.js'; // Corrected import path
// packages/types/src/pbx/events.payload.ts
import type { BaseEventPayload } from './base-event.payload.js'; 
import type { Event as AriClientEvent } from 'ari-client'; // Import ARI client event type

/**
 * Common data structure included in the `data` field of all standardized PBX events.
 * This is created by PbxEventRouterService after tenant resolution.
 */
export interface StandardizedPbxEventCoreData {
	pbxInstanceId: string; // The ID of the pbx_instances record this event originated from
	// tenantId is already in BaseEventPayload

	externalCallId: string; // Primary unique ID from the PBX for this call/leg (e.g., Asterisk UniqueID)
	linkedCallId?: string; // For bridged calls (e.g., Asterisk LinkedID)
	threadId?: string; // Some PBXs use a thread ID for related call legs

	channelName?: string; // e.g., PJSIP/101-00001abc
	channelState?: string; // Raw state from PBX (e.g., "Up", "Ringing")

	callerIdNum?: string;
	callerIdName?: string;
	connectedLineNum?: string;
	connectedLineName?: string;
	dialedNum?: string; // DNID - what number was originally dialed to reach the system
	extension?: string; // Extension involved in this event

	eventTimestamp: string; // ISO 8601 UTC - Timestamp of the original PBX event

	// Store the original raw event for debugging or if downstream needs more specific details
	// Be mindful of size if these are very large.
	rawPdsEvent?:
		| {
				type: "ARI";
				eventType: string; // e.g., 'StasisStart', 'ChannelHangupRequest'
				payload: AriClientEvent; // The actual event object from ari-client
		  }
		| {
				type: "FREEPBX_GRAPHQL";
				eventType: string; // e.g., 'CDR_RECEIVED'
				payload: any; // The raw data from GraphQL (e.g., FreePbxCdr object)
		  }
		| {
				type: "GENERIC_WEBHOOK"; // For events coming from pbx-webhook.controller.ts
				eventType: string; // Hint provided by the webhook source
				payload: any;
		  };
}

// --- Specific Standardized PBX Event Payloads ---
// These are published by PbxEventRouterService to RabbitMQ (e.g., to pbx_events_ex)

export type PbxCallInitiatedStandardizedEvent = BaseEventPayload<
	StandardizedPbxEventCoreData & {
		standardizedEventType: "PBX_CALL_INITIATED"
		direction: CallDirectionDb; // Drizzle enums;
		// Specific details from StasisStart, Newchannel, or GraphQL new call indication
		stasisStartArgs?: string[]; // If from ARI StasisStart
	}
>;

export type PbxCallAnsweredStandardizedEvent = BaseEventPayload<
	StandardizedPbxEventCoreData & {
		standardizedEventType: "PBX_CALL_ANSWERED";
		answeredByTarget: string; // e.g., extension that answered, agent ID from queue event
		// Specific details from BridgeEnter, AgentConnect, GraphQL answered status
	}
>;

export type PbxCallTerminatedStandardizedEvent = BaseEventPayload<
	StandardizedPbxEventCoreData & {
		standardizedEventType: "PBX_CALL_TERMINATED";
		durationSeconds: number;
		terminationReason: string; // Standardized reason if possible, else raw PBX reason
		dispositionCode?: string; // PBX-specific disposition code, if available
		// Specific details from Hangup, AgentComplete, GraphQL ended status (like full CDR)
		cdrDetails?: FreePbxCdr; // If this event was derived from a FreePBX CDR
	}
>;

export type PbxDtmfReceivedStandardizedEvent = BaseEventPayload<
	StandardizedPbxEventCoreData & {
		standardizedEventType: "PBX_DTMF_RECEIVED";
		digit: string;
		durationMs?: number;
	}
>;

export type PbxCallHoldStandardizedEvent = BaseEventPayload<
	StandardizedPbxEventCoreData & {
		standardizedEventType: "PBX_CALL_HOLD";
		// Specific details from Hold event
	}
>;

export type PbxCallUnholdStandardizedEvent = BaseEventPayload<
	StandardizedPbxEventCoreData & {
		standardizedEventType: "PBX_CALL_UNHOLD";
		// Specific details from Unhold event
	}
>;

export type PbxTransferInitiatedStandardizedEvent = BaseEventPayload<
	StandardizedPbxEventCoreData & {
		standardizedEventType: "PBX_TRANSFER_INITIATED";
		transferTarget?: string; // e.g., extension, number
	}
>;

export type PbxTransferCompletedStandardizedEvent = BaseEventPayload<
	StandardizedPbxEventCoreData & { standardizedEventType: "PBX_TRANSFER_COMPLETED" }
>;
// Queue-related events
export type PbxQueueCallerJoinStandardizedEvent = BaseEventPayload<StandardizedPbxEventCoreData & {
	standardizedEventType: "PBX_QUEUE_CALLER_JOIN";
	queueName: string;
	position: number;
	waitTime: number;
}>;

export type PbxQueueCallerLeaveStandardizedEvent = BaseEventPayload<StandardizedPbxEventCoreData & {
	standardizedEventType: "PBX_QUEUE_CALLER_LEAVE";
	queueName: string;
	reason: string;
	position: number;
}>;

export type PbxQueueAgentConnectStandardizedEvent = BaseEventPayload<StandardizedPbxEventCoreData & {
	standardizedEventType: "PBX_QUEUE_AGENT_CONNECT";
	queueName: string;
	agentId: string;
}>;

export type PbxQueueAgentDisconnectStandardizedEvent = BaseEventPayload<StandardizedPbxEventCoreData & {
	standardizedEventType: "PBX_QUEUE_AGENT_DISCONNECT";
	queueName: string;
	agentId: string;
	reason: string;
}>;
// Union type for any standardized PBX event your PbxRawEventHandler might consume
export type AnyStandardizedPbxEvent =
	| PbxCallInitiatedStandardizedEvent
	| PbxCallAnsweredStandardizedEvent
	| PbxCallTerminatedStandardizedEvent
	| PbxDtmfReceivedStandardizedEvent
	| PbxCallHoldStandardizedEvent
	| PbxCallUnholdStandardizedEvent
	| PbxTransferInitiatedStandardizedEvent
	| PbxTransferCompletedStandardizedEvent
	| PbxQueueCallerJoinStandardizedEvent
	| PbxQueueCallerLeaveStandardizedEvent
	| PbxQueueAgentConnectStandardizedEvent
	| PbxQueueAgentDisconnectStandardizedEvent;

// Note: Other specific event types like Hold, Unhold, Transfer, Recording, etc., need to be defined if used.
