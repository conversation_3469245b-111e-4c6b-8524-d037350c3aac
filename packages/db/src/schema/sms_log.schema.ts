import { relations, sql } from "drizzle-orm";
import {
	index,
	pgTable,
	text,
	timestamp,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { smsLogDirectionEnum } from "../enums/sms-log-direction.enum.js";
import { smsStatusEnum } from "../enums/sms-status.enum.js"; // Reusing existing SMS status enum
import { tenants } from "./tenants.schema.js";
import { users } from "./users.schema.js"; // For operator_user_id
import { pbxCalls } from "./pbx-call.schema.js"; // For related_pbx_call_id

export const smsLogs = pgTable(
	"sms_log",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id")
			.references(() => tenants.id, { onDelete: "cascade" })
			.notNull(),
		direction: smsLogDirectionEnum("direction").notNull(),
		fromNumber: varchar("from_number", { length: 50 }).notNull(), // Operator's line or system number
		toNumber: varchar("to_number", { length: 50 }).notNull(), // Customer's number
		content: text("content").notNull(),
		status: smsStatusEnum("status"), // e.g., 'SENT', 'FAILED', 'DELIVERED'
		gatewayReferenceId: varchar("gateway_reference_id", { length: 255 }), // ID from the SMS gateway
		operatorUserId: uuid("operator_user_id").references(() => users.id, { // User who initiated or is related to the SMS
			onDelete: "set null",
		}),
		relatedPbxCallId: uuid("related_pbx_call_id").references(
			() => pbxCalls.id,
			{ onDelete: "set null" },
		),
		loggedAt: timestamp("logged_at", { withTimezone: true })
			.defaultNow()
			.notNull(),
		errorMessage: text("error_message"), // If status is 'FAILED'
	},
	(table) => [
		index("sms_log_tenant_id_idx").on(table.tenantId),
		index("sms_log_direction_idx").on(table.direction),
		index("sms_log_from_number_idx").on(table.fromNumber),
		index("sms_log_to_number_idx").on(table.toNumber),
		index("sms_log_status_idx").on(table.status).where(sql`${table.status} IS NOT NULL`),
		index("sms_log_operator_user_id_idx").on(table.operatorUserId).where(sql`${table.operatorUserId} IS NOT NULL`),
		index("sms_log_related_pbx_call_id_idx").on(table.relatedPbxCallId).where(sql`${table.relatedPbxCallId} IS NOT NULL`),
		index("sms_log_logged_at_idx").on(table.loggedAt),
	],
);

export const smsLogsRelations = relations(smsLogs, ({ one }) => ({
	tenant: one(tenants, {
		fields: [smsLogs.tenantId],
		references: [tenants.id],
	}),
	operatorUser: one(users, {
		fields: [smsLogs.operatorUserId],
		references: [users.id],
		relationName: "smsLogOperator",
	}),
	relatedPbxCall: one(pbxCalls, {
		fields: [smsLogs.relatedPbxCallId],
		references: [pbxCalls.id],
		relationName: "smsLogRelatedCall",
	}),
}));

export type SmsLog = typeof smsLogs.$inferSelect;
export type NewSmsLog = typeof smsLogs.$inferInsert;