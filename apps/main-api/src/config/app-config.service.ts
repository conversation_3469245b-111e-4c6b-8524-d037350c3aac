import { Injectable, Logger } from "@nestjs/common";
// biome-ignore lint/style/useImportType: <explanation>
import { ConfigService as NestConfigService } from "@nestjs/config";

@Injectable()
export class AppConfigService {
	private readonly logger = new Logger(AppConfigService.name);
	appPort = 3004; // Initialize with a default value

	constructor(private readonly nestConfig: NestConfigService) {
		// Debug log for nestConfig DI correctness
		console.debug(
			"AppConfigService constructed. nestConfig:",
			nestConfig,
			"Type:",
			typeof nestConfig,
		);
	}

	get nodeEnv() {
		return this.nestConfig.get<string>("NODE_ENV", "development");
	}
	get botToken() {
		return this.nestConfig.get<string>("BOT_TOKEN", "");
	}
	get primaryBotToken() {
		return this.nestConfig.get<string>("PRIMARY_BOT_TOKEN", "");
	}
	get tokenEncryptionKey() {
		return this.nestConfig.get<string>("TOKEN_ENCRYPTION_KEY", "");
	}
	get databaseUrl() {
		const defaultDevUrl = "postgresql://postgres:postgres@localhost:5432/taxi";
		const url = this.nestConfig.get<string>("DATABASE_URL", "");
		if (url) {
			return url;
		}
		this.logger.warn(
			"DATABASE_URL is not set, using default development database URL",
		);
		return defaultDevUrl;
	}

	get lockStore(): string {
		// Always use memory store until we need production Redis
		return "memory";
	}

	get isDatabaseConfigured() {
		const url = this.databaseUrl;
		return !!url && url.includes("://") && url.includes("@");
	}
	get redisHost() {
		return this.nestConfig.get<string>("REDIS_HOST", "localhost");
	}
	get redisPort(): number {
		const portStr = this.nestConfig.get<string>("REDIS_PORT");
		return portStr ? Number.parseInt(portStr, 10) : 6379;
	}
	get redisPassword() {
		return this.nestConfig.get<string | undefined>("REDIS_PASSWORD", undefined);
	}
	get redisDb(): number {
		const dbStr = this.nestConfig.get<string>("REDIS_DB");
		return dbStr ? Number.parseInt(dbStr, 10) : 0;
	}
	get redisPrefix(): string {
		return this.nestConfig.get<string>("REDIS_PREFIX", "gramio:");
	}

	get rabbitMQUrl(): string {
		const url = this.nestConfig.get<string>("RABBITMQ_URL");
		if (url) {
			return url;
		}
		const defaultDevUrl = "amqp://guest:guest@localhost:5672";
		this.logger.warn(
			`RABBITMQ_URL is not set, using default development URL: ${defaultDevUrl}`,
		);
		return defaultDevUrl;
	}

	getAriAppName(): string {
		return this.nestConfig.get<string>(
			"ASTERISK_ARI_APP_NAME",
			"default-stasis-app",
		);
	}

	getAriDefaultContext(tenantId: string): string {
		// Logic to determine default context, maybe from tenant settings or global config
		return `from-ari-${tenantId}`;
	}

	getAriDefaultExtension(tenantId: string): string {
		return "s"; // Default extension 's'
	}

	getSystemTenantId(): string {
		// For Redis keys not strictly tied to a single tenant during event processing
		return this.nestConfig.get<string>("SYSTEM_TENANT_ID", "system_global");
	}
}
