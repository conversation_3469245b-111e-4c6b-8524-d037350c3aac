import { pgEnum } from "drizzle-orm/pg-core";

// 1. Define enum values as the single source of truth
const SMS_STATUS_VALUES = [
	"PENDING",
	"QUEUED",
	"SENT",
	"FAILED",
	"DELIVERED",
	"UNDELIVERED",
	"RECEIVED",
	"READ",
] as const;

// 2. Create the Drizzle ORM pgEnum for database schema definition
export const smsStatusEnum = pgEnum("sms_status", SMS_STATUS_VALUES);

// 3. Define TypeScript union type for strict type safety
export type SmsStatusType = (typeof SMS_STATUS_VALUES)[number];

// 4. Define TypeScript string enum for application logic
export enum SmsStatus {
	PENDING = "PENDING",
	QUEUED = "QUEUED",
	SENT = "SENT",
	FAILED = "FAILED",
	DELIVERED = "DELIVERED",
	UNDELIVERED = "UNDELIVERED",
	RECEIVED = "RECEIVED",
	READ = "READ",
}
