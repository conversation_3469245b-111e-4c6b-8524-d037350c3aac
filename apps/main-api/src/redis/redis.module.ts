import { AppConfigService } from "@/config/app-config.service.js";
import { ConfigModule } from "@/config/config.module.js";
import { Global, Module } from "@nestjs/common";
import { Logger } from "@nestjs/common";
import { ClientsModule, Transport } from "@nestjs/microservices";
import { redisClientsFactory } from "./redis.providers.js"; // Import the factory
import { RedisDataService } from "./redis-data.service.js"; // Import the new service
import { RedisServiceToken } from "./redis.token.js";

export const REDIS_MICROSERVICE_CLIENT = "REDIS_MICROSERVICE_CLIENT";

@Global()
@Module({
	imports: [
		ConfigModule,
		ClientsModule.registerAsync([
			{
				name: REDIS_MICROSERVICE_CLIENT,
				inject: [AppConfigService],
				useFactory: (config: AppConfigService) => {
					const logger = new Logger("RedisModule");
					const options = {
						host: config.redisHost,
						port: config.redisPort,
						...(config.redisPassword ? { password: config.redisPassword } : {}),
						db: config.redisDb,
						keyPrefix: config.redisPrefix,
						retryAttempts: 5,
						retryDelay: 3000,
					};
					logger.log(
						`Connecting to Redis at ${options.host}:${options.port} (db: ${options.db}, prefix: "${options.keyPrefix}")`,
					);
					return {
						transport: Transport.REDIS,
						options,
					};
				},
			},
		]),
	],
	providers: [
		redisClientsFactory, // Add the factory to create Redis clients
		RedisDataService,    // Add the new data service
		{
			provide: RedisServiceToken, // Provide RedisDataService for IRedisService
			useClass: RedisDataService,
		},
	],
	exports: [RedisServiceToken, RedisDataService, ClientsModule], // Export the token and service
})
export class RedisModule {}
