import { pgEnum } from "drizzle-orm/pg-core";

const SMS_LOG_DIRECTION_VALUES = ["OUTBOUND_OPERATOR", "INBOUND_REPLY"] as const;

export const smsLogDirectionEnum = pgEnum(
	"sms_log_direction",
	SMS_LOG_DIRECTION_VALUES,
);

export type SmsLogDirectionType = (typeof SMS_LOG_DIRECTION_VALUES)[number];

export enum SmsLogDirection {
	OUTBOUND_OPERATOR = "OUTBOUND_OPERATOR",
	INBOUND_REPLY = "INBOUND_REPLY",
}
