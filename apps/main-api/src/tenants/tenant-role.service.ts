import { Injectable } from "@nestjs/common";

/**
 * Service responsible for managing user roles for tenants
 */
@Injectable()
export class TenantRoleService {
	// In-memory cache of user roles for tenants
	// In a real implementation, this would be backed by a database
	private userRoles: Map<string, Map<number, string[]>> = new Map();

	/**
	 * Get the roles for a user in a tenant
	 * @param tenantId The tenant ID
	 * @param userId The user ID
	 * @returns Array of role names
	 */
	async getRolesForUser(tenantId: string, userId: number): Promise<string[]> {
		// Check if we have roles for this tenant
		if (!this.userRoles.has(tenantId)) {
			// Initialize with mock data
			this.initializeMockRoles(tenantId);
		}

		// Get the roles for this user in this tenant
		const tenantRoles = this.userRoles.get(tenantId);
		if (!tenantRoles) {
			return []; // Return an empty array if tenantRoles is undefined
		}

		return tenantRoles.get(userId) || [];
	}

	/**
	 * Check if a user has a specific role in a tenant
	 * @param tenantId The tenant ID
	 * @param userId The user ID
	 * @param role The role to check
	 * @returns True if the user has the role, false otherwise
	 */
	async hasRole(
		tenantId: string,
		userId: number,
		role: string,
	): Promise<boolean> {
		const roles = await this.getRolesForUser(tenantId, userId);
		return roles.includes(role);
	}

	/**
	 * Add a role to a user in a tenant
	 * @param tenantId The tenant ID
	 * @param userId The user ID
	 * @param role The role to add
	 */
	async addRoleToUser(
		tenantId: string,
		userId: number,
		role: string,
	): Promise<void> {
		// Check if we have roles for this tenant
		if (!this.userRoles.has(tenantId)) {
			this.userRoles.set(tenantId, new Map());
		}

		// Get the roles for this tenant
		const tenantRoles = this.userRoles.get(tenantId);

		if (!tenantRoles) {
			return; // Exit if tenantRoles is undefined
		}

		// Check if we have roles for this user, if not initialize an empty array
		if (!tenantRoles.has(userId)) {
			tenantRoles.set(userId, []);
		}

		// Get the roles for this user
		const userRoles = tenantRoles.get(userId) || [];

		// Add the role if it doesn't already exist
		if (!userRoles.includes(role)) {
			userRoles.push(role);
		}
	}

	/**
	 * Remove a role from a user in a tenant
	 * @param tenantId The tenant ID
	 * @param userId The user ID
	 * @param role The role to remove
	 */
	async removeRoleFromUser(
		tenantId: string,
		userId: number,
		role: string,
	): Promise<void> {
		// Check if we have roles for this tenant
		if (!this.userRoles.has(tenantId)) {
			return;
		}

		// Get the roles for this tenant
		const tenantRoles = this.userRoles.get(tenantId);
		if (!tenantRoles) return;
		// Check if we have roles for this user
		if (!tenantRoles.has(userId)) {
			return;
		}

		// Get the roles for this user
		const userRoles = tenantRoles.get(userId) || [];

		// Remove the role if it exists
		const index = userRoles.indexOf(role);
		if (index !== -1) {
			userRoles.splice(index, 1);
		}
	}

	/**
	 * Initialize mock roles for a tenant
	 * @param tenantId The tenant ID
	 */
	private initializeMockRoles(tenantId: string): void {
		// Create a new map for this tenant
		const tenantRoles = new Map<number, string[]>();

		// Add some mock roles
		tenantRoles.set(123456789, ["admin", "user"]);
		tenantRoles.set(987654321, ["user"]);

		// Store the roles for this tenant
		this.userRoles.set(tenantId, tenantRoles);
	}
}
