import { relations } from "drizzle-orm";
import { jsonb, pgTable, timestamp, uuid, varchar } from "drizzle-orm/pg-core";
import { tenantPlanEnum } from "../enums/tenant-plan.enum.js";
import { tenantStatusEnum } from "../enums/tenant-status.enum.js";
// Import other schemas that 'tenants' relates to
import { bots } from "./bots.schema.js";
import { multiTenantGroup } from "./multi-tenant-group.schema.js";
import { roles } from "./role.schema.js";

export const tenants = pgTable("tenants", {
	id: uuid("id").defaultRandom().primaryKey(),
	name: varchar("name", { length: 255 }).notNull(),
	legalName: varchar("legal_name", { length: 255 }),
	email: varchar("email", { length: 255 }),
	phone: varchar("phone", { length: 50 }),
	logoUrl: varchar("logo_url", { length: 1024 }),
	address: varchar("address", { length: 512 }),
	country: varchar("country", { length: 2 }), // Assuming 2-letter ISO country code
	timezone: varchar("timezone", { length: 64 }),
	plan: tenantPlanEnum("plan"),
	status: tenantStatusEnum("status").default("ACTIVE").notNull(),
	multiTenantGroupId: uuid("multi_tenant_group_id").references(
		() => multiTenantGroup.id,
		{ onDelete: "set null" },
	),
	metadata: jsonb("metadata").$type<Record<string, any>>(),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
	deletedAt: timestamp("deleted_at"), // For soft deletes
});

// Define relations after all involved tables are defined
export const tenantsRelations = relations(tenants, ({ one, many }) => ({
	bots: many(bots),
	roles: many(roles),
	multiTenantGroup: one(multiTenantGroup, {
		fields: [tenants.multiTenantGroupId],
		references: [multiTenantGroup.id],
	}),
}));

export type Tenant = typeof tenants.$inferSelect;
export type NewTenant = typeof tenants.$inferInsert;
