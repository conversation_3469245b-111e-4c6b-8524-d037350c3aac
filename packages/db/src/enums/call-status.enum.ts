import { pgEnum } from "drizzle-orm/pg-core";

// 1. Define enum values as the single source of truth
const CALL_STATUS_VALUES = [
	"SCHEDULED",
	"RINGING",
	"ANSWERED",
	"MISSED",
	"ENDED",
	"FAILED",
] as const;

// 2. Create the Drizzle ORM pgEnum for database schema definition
export const callStatusEnum = pgEnum("call_status", CALL_STATUS_VALUES);

// 3. Define TypeScript union type for strict type safety
export type CallStatusType = (typeof CALL_STATUS_VALUES)[number];

// 4. Define TypeScript string enum for application logic
export enum CallStatus {
	SCHEDULED = "SCHEDULED",
	RINGING = "RINGING",
	ANSWERED = "ANSWERED",
	MISSED = "MISSED",
	ENDED = "ENDED",
	FAILED = "FAILED",
}
