import { pgEnum } from "drizzle-orm/pg-core";

// 1. Define enum values as the single source of truth
const PROMOTIONS_VALUES = ["PERCENTAGE", "VALUE", "FREE_RIDE"] as const;

// 2. Create the Drizzle ORM pgEnum for database schema definition
export const promotionsEnum = pgEnum(
	"promotion_type",
	PROMOTIONS_VALUES,
);

// 3. Define TypeScript union type for strict type safety
export type PromotionsType = (typeof PROMOTIONS_VALUES)[number];

// 4. Define TypeScript string enum for application logic
export enum Promotions {
	PERCENTAGE = "PERCENTAGE",
	VALUE = "VALUE",
	FREE_RIDE = "FREE_RIDE",
}
