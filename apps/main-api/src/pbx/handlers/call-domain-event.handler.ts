// # Consumes domain events (like call.created) to update other systems/UI
// apps/main-api/src/pbx/handlers/call-domain-event.handler.ts
import { Injectable, Logger } from '@nestjs/common';
import type { RascalService } from '@/rabbitmq/rascal.service'; // Corrected: WsPushNotificationPayload is exported from @repo/types
import type { PbxCallCreatedDomainEvent, PbxCallVehicleAssignedDomainEvent, WsPushNotificationPayload, SmsSendRequestPayload } from '@repo/types';
import { v4 as uuidv4 } from 'uuid';
import type { AckOrNack } from 'rascal';
import { RabbitHandler } from '@/rabbitmq/handler.decorator';

@Injectable()
export class CallDomainEventHandler {
  private readonly logger = new Logger(CallDomainEventHandler.name);

  constructor(private readonly rascalService: RascalService) {}

  @RabbitHandler({ subscription: 'sub_call_domain_event_notifications' }) // Matches the subscription name in rascal-definitions.ts
  async handleCallCreatedForNotification(
    content: PbxCallCreatedDomainEvent,
    ackOrNack: AckOrNack,
  ): Promise<void> {
    const { eventId, tenantId, data: newCall } = content;
    this.logger.log(`[${eventId}] CallCreatedDomainEvent received for tenant ${tenantId}, call ID: ${newCall.id}. Preparing notification.`);

 if (!tenantId) {
 this.logger.warn(`[${eventId}] Cannot push WebSocket notification for CallCreatedDomainEvent: tenantId is null.`);
 ackOrNack(); // Acknowledge as unprocessable for UI push
 return;
 }

    try {
      // Example: Publish an event for the WebSocketPushService to notify the dashboard.
      const notificationPayload: WsPushNotificationPayload = { // This is NOT a BaseEventPayload
        pushId: uuidv4(),
        timestamp: new Date().toISOString(),
        tenantId: tenantId,
        correlationId: eventId,
        type: 'call.created', // UI-specific notification type
        title: 'New Incoming Call',
        message: `Call from ${newCall.callerName || newCall.fromPhoneNumber} to ${newCall.toPhoneNumber || newCall.numericExtension}.`,
        payload: { callId: newCall.id, status: newCall.status, /* other relevant UI fields */ },
        // targetRoom: `tenant:${tenantId}:operators`, // Or more specific targeting
      };

      await this.rascalService.publish('pub_dashboard_push_event', notificationPayload, { // tenantId is guaranteed by the event structure.
 routingKeyCtx: { tenantId: tenantId, dataType: 'new_call_notification' }
      });

      // Example: If a new call from a known VIP customer, maybe send an internal alert
      // if (newCall.customerUser?.isVip) {
      //   const alertEvent: BaseEventPayload<any> = { ... };
      //   await this.rascalService.publish('pub_system_event', alertEvent, { routingKeyCtx: { eventType: 'system.alert.vip_caller', tenantId }});
      // }

      ackOrNack();
    } catch (error) {
      this.logger.error(`[${eventId}] Error handling CallCreatedDomainEvent for notification:`, error);
      ackOrNack(error as Error, { strategy: 'nack' });    }
  }

  @RabbitHandler({ subscription: 'sub_call_domain_event_vehicle_assignment' }) // Example
  async handleVehicleAssignedForSms(
    content: PbxCallVehicleAssignedDomainEvent,
    ackOrNack: AckOrNack,
  ): Promise<void> {
    const { eventId, tenantId, data: assignmentData } = content;
    this.logger.log(`[${eventId}] PbxCallVehicleAssignedDomainEvent for tenant ${tenantId}, call ${assignmentData.callId}. Preparing SMS.`);

    try {
      // 1. Fetch PbxCall details to get customer phone number
      // const callDetails = await this.dbService.getDb().query.pbxCalls.findFirst(...);
      // if (!callDetails || !callDetails.customerPhoneNumber) {
      //   this.logger.warn(`[${eventId}] No customer phone number for call ${assignmentData.callId}. Cannot send SMS.`);
      //   ackOrNack();
      //   return;
      // }

      // 2. Construct SMS message
      const smsContent = `Your vehicle ${assignmentData.vehicleIds.join(', ')} is on its way. ETA: ${assignmentData.etaMinutes || 'N/A'} min.`;

      // 3. Publish SmsSendRequestEvent
      const smsRequest: SmsSendRequestPayload = {
        eventId: uuidv4(),
        timestamp: new Date().toISOString(),
        version: "1.0",
        sourceService: CallDomainEventHandler.name,
        tenantId,
        correlationId: eventId,
        data: {
          // toPhoneNumber: callDetails.customerPhoneNumber, // This needs to be fetched
          toPhoneNumber: "FETCH_CUSTOMER_PHONE_NUMBER", // Placeholder
          messageContent: smsContent,
          relatedCallId: assignmentData.callId,
        }
      };
      await this.rascalService.publish('pub_messaging_domain_event', smsRequest, {
        routingKeyCtx: { entity: 'sms', action: 'send_request', tenantId }
      });

      ackOrNack();
    } catch (error) {
      this.logger.error(`[${eventId}] Error handling PbxCallVehicleAssignedDomainEvent for SMS:`, error);
      ackOrNack(error as Error, { strategy: 'nack' });
    }
  }

  // ... other handlers for other domain events ...
}