import { relations } from "drizzle-orm";
import {
	boolean,
	jsonb,
	numeric,
	pgTable,
	timestamp,
	uniqueIndex,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { rideStatusEnum } from "../enums/ride-status.enum.js";
import { ridesEnum } from "../enums/ride.enum.js";
import { promotions } from "./promotion.schema.js";
import { tenants } from "./tenants.schema.js";
import { users } from "./users.schema.js";
import { vehicles } from "./vehicle.schema.js";

export const rideOrder = pgTable(
	"ride_order",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id").notNull(),
		passengerId: uuid("passenger_id"),
		driverId: uuid("driver_id"),
		vehicleId: uuid("vehicle_id"),
		pickupAddress: varchar("pickup_address", { length: 255 }),
		pickupLatLng: jsonb("pickup_latlng"),
		dropoffAddress: varchar("dropoff_address", { length: 255 }),
		dropoffLatLng: jsonb("dropoff_latlng"),
		scheduledTime: timestamp("scheduled_time"),
		confirmedTime: timestamp("confirmed_time"),
		startTime: timestamp("start_time"),
		endTime: timestamp("end_time"),
		status: rideStatusEnum("status").notNull().default("SEARCHING"),
		orderType: ridesEnum("order_type").notNull().default("RIDE"),
		estimatedFare: numeric("estimated_fare", { precision: 10, scale: 2 }),
		currency: varchar("currency", { length: 3 }),
		paymentMethod: varchar("payment_method", { length: 50 }),
		paid: boolean("paid").default(false),
		promoId: uuid("promo_id"),
		metadata: jsonb("metadata"),
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull(),
	},
	(table) => [
		uniqueIndex("ride_order_tenant_idx").on(table.tenantId),
		uniqueIndex("ride_order_passenger_idx").on(table.passengerId),
		uniqueIndex("ride_order_driver_idx").on(table.driverId),
	],
);

export const rideOrderRelations = relations(rideOrder, ({ one }) => ({
	tenant: one(tenants, {
		fields: [rideOrder.tenantId],
		references: [tenants.id],
	}),
	passenger: one(users, {
		fields: [rideOrder.passengerId],
		references: [users.id],
	}),
	driver: one(users, {
		fields: [rideOrder.driverId],
		references: [users.id],
	}),
	vehicle: one(vehicles, {
		fields: [rideOrder.vehicleId],
		references: [vehicles.id],
	}),
	promo: one(promotions, {
		fields: [rideOrder.promoId],
		references: [promotions.id],
	}),
}));
