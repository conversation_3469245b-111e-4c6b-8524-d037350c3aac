import { pgEnum } from "drizzle-orm/pg-core";

const VERIFICATION_STATUS_VALUES = [
	"INITIATED",
	"CODE_SENT",
	"VERIFIED",
	"FAILED",
	"EXPIRED",
] as const;

export const verificationStatusEnum = pgEnum(
	"verification_status",
	VERIFICATION_STATUS_VALUES,
);

export type VerificationStatusType = (typeof VERIFICATION_STATUS_VALUES)[number];

export enum VerificationStatus {
	INITIATED = "INITIATED",
	CODE_SENT = "CODE_SENT",
	VERIFIED = "VERIFIED",
	FAILED = "FAILED",
	EXPIRED = "EXPIRED",
}
