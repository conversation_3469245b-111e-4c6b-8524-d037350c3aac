{
	"$schema": "https://json.schemastore.org/tsconfig",
	"compilerOptions": {
		"target": "ES2022",
		"module": "NodeNext",
		"moduleResolution": "NodeNext",
		"esModuleInterop": true,
		"allowSyntheticDefaultImports": true,

		"strict": true,
		"skipLibCheck": true,
		"noEmit": true,
		"baseUrl": ".",
		"paths": {
			// Keep paths for shared configs if needed, but remove for buildable packages
			// "@repo/db": ["packages/db/src/index.ts"], // Remove or ensure it points to dist if absolutely necessary for some tool
			// "@repo/db/*": ["packages/db/src/*"],
			"@repo/typescript-config/*": ["packages/typescript-config/*"],
			"@repo/eslint-config/*": ["packages/eslint-config/*"]
			// "@repo/types": ["packages/types/src/index.ts"], // Remove or ensure it points to dist
			// "@repo/types/*": ["packages/types/src/*"]
		}
	},
	"include": [],
	"exclude": ["node_modules", "**/dist", "**/.next", "**/out", "**/.turbo"],
	"references": [
		{ "path": "./packages/db" },
		{ "path": "./packages/eslint-config" },
		{ "path": "./apps/main-api" },
		{ "path": "./apps/dashboard" },
		{ "path": "./apps/mini-app" },
		{ "path": "./packages/types" }
	]
}
