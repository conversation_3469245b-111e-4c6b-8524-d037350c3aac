import { relations } from "drizzle-orm";
import { index, pgTable, timestamp, uuid } from "drizzle-orm/pg-core";
import { rideStatusEnum } from "../enums/ride-status.enum.js";
import { ridesEnum } from "../enums/ride.enum.js";
import { tenants } from "./tenants.schema.js";
import { users } from "./users.schema.js";
import { vehicles } from "./vehicle.schema.js";

export const rides = pgTable(
	"rides",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id")
			.references(() => tenants.id, { onDelete: "cascade" })
			.notNull(),
		passengerId: uuid("passenger_id")
			.notNull()
			.references(() => users.id),
		driverId: uuid("driver_id")
			.notNull()
			.references(() => users.id),
		vehicleId: uuid("vehicle_id")
			.notNull()
			.references(() => vehicles.id),
		status: rideStatusEnum("status").notNull().default("SEARCHING"),
		orderType: ridesEnum("order_type").notNull().default("RIDE"),
		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at").defaultNow().notNull(),
	},
	(table) => [index("rides_tenant_id_idx").on(table.tenantId)],
);

export const ridesRelations = relations(rides, ({ one }) => ({
	tenant: one(tenants, {
		fields: [rides.tenantId],
		references: [tenants.id],
	}),
	passenger: one(users, {
		fields: [rides.passengerId],
		references: [users.id],
	}),
	driver: one(users, {
		fields: [rides.driverId],
		references: [users.id],
	}),
	vehicle: one(vehicles, {
		fields: [rides.vehicleId],
		references: [vehicles.id],
	}),
}));

export type Ride = typeof rides.$inferSelect;
export type NewRide = typeof rides.$inferInsert;
