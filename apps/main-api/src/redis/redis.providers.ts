import { ConfigService } from "@nestjs/config";
import Redis from "ioredis";
import {
	REDIS_CACHE_CLIENT,
	REDIS_CLIENTS_PROVIDER,
	REDIS_PUBSUB_CLIENT,
} from "./redis.constants";

interface RedisClientOptions {
	host: string | undefined;
	port: number | undefined;
	password?: string;
	db?: number;
	max?: number; // Add max connections to the pool
}

export const redisClientsFactory = {
	provide: REDIS_CLIENTS_PROVIDER, // Use a constant instead of string
	useFactory: (configService: ConfigService): { [key: string]: Redis } => {
		const baseConfig: RedisClientOptions = {
			host: configService.get<string>("REDIS_HOST"),
			port: configService.get<number>("REDIS_PORT"),
			password: configService.get<string>("REDIS_PASSWORD"),
		};
		// Debug log to confirm actual values used for Redis connection (DO NOT log password in production)
		console.log("[RedisFactory] baseConfig:", {
			...baseConfig,
			password: baseConfig.password ? "***" : "(empty)",
		});

		// Explicitly check for undefined or empty host and port
		if (!baseConfig.host || !baseConfig.port) {
			throw new Error("Redis host and port are required");
		}

		// Enhanced Redis client configuration with explicit auth and retry strategy
		const enhancedConfig = {
			...baseConfig,
			// Explicitly set auth credentials
			username: configService.get<string>("REDIS_USERNAME") || undefined,
			password: configService.get<string>("REDIS_PASSWORD") || "internaut22",
			// Connection options
			connectTimeout: 10000,
			maxRetriesPerRequest: 3,
			enableReadyCheck: true,
			// Retry strategy for connection failures
			retryStrategy: (times: number) => {
				console.log(`Redis connection retry attempt ${times}`);
				return Math.min(times * 100, 3000); // Exponential backoff with max 3s
			},
			// Reconnect on error
			reconnectOnError: (err: Error) => {
				const targetError = "READONLY";
				if (err.message.includes(targetError)) {
					// Only reconnect on specific errors
					return true;
				}
				return false;
			},
		};

		const cacheClient = new Redis({
			...enhancedConfig,
			db: configService.get<number>("REDIS_CACHE_DB") || 0,
			connectionName: "cache",
		});

		const pubsubClient = new Redis({
			...enhancedConfig,
			db: configService.get<number>("REDIS_PUBSUB_DB") || 0,
			connectionName: "pubsub",
		});

		// Optional: Add event listeners
		for (const client of [cacheClient, pubsubClient]) {
			client.on("error", (err: Error) => console.error("Redis error:", err));
			client.on("connect", () => console.log("Redis connected"));
		}

		return {
			[REDIS_CACHE_CLIENT]: cacheClient,
			[REDIS_PUBSUB_CLIENT]: pubsubClient,
		};
	},
	inject: [ConfigService],
};
