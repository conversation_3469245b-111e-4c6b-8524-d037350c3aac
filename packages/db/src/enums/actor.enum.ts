import { pgEnum } from "drizzle-orm/pg-core";

// 1. Define enum values as the single source of truth
const ACTOR_VALUES = ["USER", "OPERATOR", "SYSTEM"] as const;

// 2. Create the Drizzle ORM pgEnum for database schema definition
export const actorEnum = pgEnum("actor_type", ACTOR_VALUES);

// 3. Define TypeScript union type for strict type safety
export type ActorType = (typeof ACTOR_VALUES)[number];

// 4. Define TypeScript string enum for application logic
export enum Actor {
	USER = "USER",
	OPERATOR = "OPERATOR",
	SYSTEM = "SYSTEM",
}
