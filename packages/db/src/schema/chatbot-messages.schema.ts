import { relations } from "drizzle-orm";
import {
	boolean,
	geometry,
	index, // Import index
	jsonb,
	pgTable,
	text,
	timestamp,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { messageDirectionEnum } from "../enums/message-direction.enum.js"; // Ensure this path is correct
import { messagesEnum } from "../enums/message.enum.js";
import { chatbotSessions } from "./chatbot-sessions.schema.js";
import { chatbotUsers } from "./chatbot-users.schema.js";

export const chatbotMessages = pgTable(
	"chatbot_message",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		chatbotSessionId: uuid("chatbot_session_id")
			.references(() => chatbotSessions.id)
			.notNull(), // Consider onDelete: "cascade" or "set null"
		chatbotUserId: uuid("chatbot_user_id")
			.references(() => chatbotUsers.id)
			.notNull(),
		direction: messageDirectionEnum("direction").notNull(), // Consider renaming enum to chatbot_message_direction_enum
		sentAt: timestamp("sent_at").notNull(),
		messageType: messagesEnum("message_type").notNull(),
		content: text("content").notNull(),
		phoneVerified: boolean("phone_verified").notNull().default(false),
		verificationReference: varchar("verification_reference", {
			length: 255,
		}),
		repliedToId: uuid("replied_to_id"),
		errorFlag: boolean("error_flag").notNull().default(false),
		providerMessageId: varchar("provider_message_id", { length: 255 }),
		metadata: jsonb("metadata").$type<Record<string, unknown>>(),
	},
	(table) => [
		index("chatbot_message_session_id_idx").on(
			// Changed to non-unique index
			table.chatbotSessionId,
		),
		index("chatbot_message_user_id_idx").on(
			// Changed to non-unique index
			table.chatbotUserId,
		),
		index("chatbot_message_sent_at_idx").on(
			// Changed to non-unique index
			table.sentAt,
		),
	],
);

export const chatbotMessagesWithGeolocation = pgTable(
	"chatbot_message_with_geolocation",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		messageId: uuid("message_id") // Consistent naming
			.references(() => chatbotMessages.id)
			.notNull(),
		location: geometry("location", {
			type: "point",
			mode: "xy",
			srid: 4326,
		}).notNull(), // SRID 4326 is for WGS84 (standard GPS coordinates)
		createdAt: timestamp("created_at").defaultNow().notNull(),
	},
	(table) => [
		index("chatbot_message_geolocation_idx").using("gist", table.location),
	],
);

export const chatbotMessagesRelations = relations(
	chatbotMessages,
	({ one }) => ({
		user: one(chatbotUsers, {
			fields: [chatbotMessages.chatbotUserId],
			references: [chatbotUsers.id],
		}),
		reply: one(chatbotMessages, {
			fields: [chatbotMessages.repliedToId],
			references: [chatbotMessages.id],
		}),
	}),
);

export const chatbotMessagesWithGeolocationRelations = relations(
	chatbotMessagesWithGeolocation,
	({ one }) => ({
		message: one(chatbotMessages, {
			fields: [chatbotMessagesWithGeolocation.messageId],
			references: [chatbotMessages.id],
		}),
	}),
);
