# Scheduled Call Management Features

## Scheduled Call Management - Fetch Initial

**Functional Description:**
Loads the initial list of all scheduled calls (both past and future, including recurring templates if not yet processed for next occurrence).

### API Interface

- **Protocol:** WebSocket (Server Push)
- **Event/Endpoint:** `schedule:init` (sent by server after `client:init`)
- **Request:** Implicitly triggered after `client:init`.
- **Response (Unicast to self):**

  ```json
  {
    "event": "schedule:init",
    "data": [
      {
        "id": "string (uuid)",
        "timestamp": "number (epoch ms or base for recurring)",
        "vehicles": "Array<string> (optional)",
        "phone": "string",
        "location": "string (optional)",
        "destination": "string (optional)",
        "area": "string (optional)",
        "arrivaltime": "number (minutes)",
        "source": "string ('schedule')",
        "operator_ids": "Array<number> (optional)",
        "timeassigned": "number (epoch ms, optional)",
        "comment": "string (optional)",
        "gisdata": "object (optional)",
        "repeat": "Array<string|0> (optional, e.g., [0, \"08:00\", ...])",
        "metadata": "object (optional)"
      }
    ]
  }
  ```

- **Authentication:** WebSocket (Operator Session)

### Business Rules

- Backend (`schedule.js`) loads scheduled calls from Redis (keys matching `schedule:*`).
- For recurring calls (`repeat` field is present), `timestamp` might be the original base time or the next calculated occurrence.
- The list is sorted by `timestamp` (future calls first, then past/template calls).

### Data Model

```mermaid
erDiagram
  SCHEDULED_CALLS_REDIS {
    string id PK
    timestamp timestamp
    int arrivaltime
    string phone
    string[] repeat
  }
```

### Error Handling

- Errors in fetching data from Redis would typically prevent `schedule:init` or result in an empty list.

---

## Scheduled Call Management - Fetch Active

**Functional Description:**
Loads scheduled calls that are due soon or have recently become active based on their scheduled time and `arrivaltime`. These are calls that should be visible on the main "live calls" board.

### API Interface

- **Protocol:** WebSocket (Server Push)
- **Event/Endpoint:** `schedule:active` (sent by server after `client:init`)
- **Request:** Implicitly triggered after `client:init`.
- **Response (Unicast to self):**

  ```json
  {
    "event": "schedule:active",
    "data": [ /* Array of Call objects that are considered active scheduled calls */ ]
  }
  ```

- **Authentication:** WebSocket (Operator Session)

### Business Rules

- Backend (`schedule.js` -> `eventHandlers.js` -> `core.js`) queries the `calls` PostgreSQL table for calls with `source: 'schedule'`.
- Filters these calls to include those where `(call.timestamp + call.arrivaltime * 60000 + core.taxiSettings.activePeriod * 60000) > Date.now()`.
- This means the call's effective "due time" plus an active display period is still in the future or very recent past.
- These calls are also sent as part of `call:init` but are re-sent here grouped for client convenience.

### Data Model

```mermaid
erDiagram
  CALLS {
    uuid id PK
    timestamp timestamp
    int arrivaltime
    string source "'schedule'"
  }
```

### Error Handling

- Database query errors would prevent this event or result in an empty list.

---

## Scheduled Call Management - Add

**Functional Description:**
Allows an operator to schedule a new call for a future time, potentially with recurrence.

### API Interface

- **Protocol:** WebSocket
- **Event/Endpoint:** `schedule:add`
- **Request:**

  ```json
  {
    "event": "schedule:add",
    "data": {
      "id": "string (uuid, optional, if based on existing call)",
      "phone": "string",
      "location": "string (optional)",
      "destination": "string (optional)",
      "area": "string (optional)",
      "comment": "string (optional)",
      "source": "string (e.g., 'manual' if base call, will be 'schedule')",
      "vehicles": "Array<string> (optional)",
      "arrivaltime": "number (minutes)",
      "timestamp": "number (epoch ms for specific date/time or base for recurring)",
      "repeat": "Array<string|0> (optional, e.g., [0, \"08:00\", 0, ...])",
      "gisdata": "object (optional)"
    }
  }
  ```

- **Response (Broadcast to all clients):**

  ```json
  {
    "event": "schedule:add",
    "data": { /* Full scheduled Call object with server-assigned id, updated source, operator_ids, etc. */ }
  }
  ```

  (May also be preceded by `call:add` or `call:update` if a base call record is created/updated in PostgreSQL)
- **Authentication:** WebSocket (Operator Session)

### Business Rules

- Backend (`schedule.js` via `eventHandlers.js`):
  - If `data.id` is not provided (new schedule not based on an existing call), a placeholder call record is first created via `call:add` (source 'manual', no vehicles, `arrivaltime: -1`). The ID from this new call is used for the schedule.
  - If `data.id` is provided, the existing call record is updated via `call:update` (e.g., to set `arrivaltime: -1` if it wasn't already a placeholder).
  - The `operator_id` of the requester is added to `operator_ids`.
  - `source` is set to `'schedule'`.
  - If `vehicles` are provided, `timeassigned` is set to `Date.now()`.
  - If `repeat` is present, `timestamp` is calculated as the next occurrence using `nextOccurence(data.repeat)`. Otherwise, the provided `timestamp` is used.
  - The scheduled call data is saved to Redis (`schedule:<id>`).
  - An in-memory timer is set using `setTimeout(activateCall, delay, call)` where `delay = call.timestamp - Date.now() - call.arrivaltime * 60000`.
  - The `schedule:add` event is broadcast.

### Data Model

```mermaid
erDiagram
  SCHEDULED_CALLS_REDIS {
    string id PK
    timestamp timestamp
    int arrivaltime
    string phone
    string[] repeat
    int[] operator_ids
  }
  CALLS {
    uuid id PK
    string source
    int arrivaltime
  }
  SCHEDULED_CALLS_REDIS ||..o{ CALLS : "references_or_creates"
  OPERATORS { int id PK }
  SCHEDULED_CALLS_REDIS ||--o{ OPERATORS : "scheduled_by"
```

### Error Handling

- Issues with creating/updating the base call record in PostgreSQL.
- Issues saving to Redis.

---

## Scheduled Call Management - Update

**Functional Description:**
Allows an operator to modify an existing scheduled call's details, time, or recurrence.

### API Interface

- **Protocol:** WebSocket
- **Event/Endpoint:** `schedule:update`
- **Request:**

  ```json
  {
    "event": "schedule:update",
    "data": { /* Call object with 'id' and fields to update */
      "id": "string (uuid)",
      "timestamp": "number (updated epoch ms)",
      "arrivaltime": "number (updated minutes)",
      "repeat": "Array<string|0> (updated)"
      // ... other fields
    }
  }
  ```

- **Response (Broadcast to all clients):**

  ```json
  {
    "event": "schedule:update",
    "data": { /* Full updated scheduled Call object */ }
  }
  ```

- **Authentication:** WebSocket (Operator Session). Call must be locked by the requesting operator.

### Business Rules

- Backend (`schedule.js` via `eventHandlers.js`):
  - Verifies the call is locked by the operator.
  - Updates `operator_ids`.
  - If `vehicles` are assigned and `timeassigned` was not set, `timeassigned` is set to `Date.now()`. If vehicles are removed, `timeassigned` is set to `null`.
  - If `repeat` is present, `timestamp` is recalculated using `nextOccurence(data.repeat, data.timestamp)`.
  - The existing in-memory timer for activation is cleared (`clearTimeout`).
  - The scheduled call data is updated in Redis (`schedule:<id>`).
  - A new in-memory timer is set if `timestamp` or `arrivaltime` changed and the call is still in the future.
  - The `schedule:update` event is broadcast.
  - The call is unlocked.

### Data Model

```mermaid
erDiagram
  SCHEDULED_CALLS_REDIS {
    string id PK
    timestamp timestamp
    int arrivaltime
    string[] repeat
  }
```

### Error Handling

- If call is not locked by the operator.
- Issues updating Redis or managing timers.

---

## Scheduled Call Management - Remove

**Functional Description:**
Allows an operator to delete a scheduled call.

### API Interface

- **Protocol:** WebSocket
- **Event/Endpoint:** `schedule:remove`
- **Request:**

  ```json
  {
    "event": "schedule:remove",
    "data": { "id": "string (uuid)" }
  }
  ```

- **Response (Broadcast to all clients):**

  ```json
  {
    "event": "schedule:remove",
    "data": "string (id of the removed scheduled call)"
  }
  ```

  (May be followed by `call:update` to modify the base call record in PostgreSQL)
- **Authentication:** WebSocket (Operator Session). (Locking is implied if it's an edit operation before removal, but `schedule:remove` itself doesn't explicitly check lock in `schedule.js`'s `api.remove`, it relies on the client flow).

### Business Rules

- Backend (`schedule.js` via `eventHandlers.js`):
  - Deletes the scheduled call entry from Redis (`schedule:<id>`).
  - Clears its associated in-memory activation timer.
  - Triggers a `call:update` for the corresponding call ID in PostgreSQL, typically to set `arrivaltime: null` to signify it's no longer a scheduled-active call.
  - The `schedule:remove` event is broadcast.

### Data Model

```mermaid
erDiagram
  SCHEDULED_CALLS_REDIS {
    string id PK
  }
  CALLS {
    uuid id PK
    int arrivaltime
  }
  SCHEDULED_CALLS_REDIS ..|> CALLS : "updates_base_record"
```

### Error Handling

- If the scheduled call ID doesn't exist in Redis or timers.

---

## Scheduled Call Management - Cancel (Activate Now)

**Functional Description:**
Operator cancels a future schedule, effectively converting it into an immediate, active call to be handled.

### API Interface

- **Protocol:** WebSocket
- **Event/Endpoint:** `schedule:cancel`
- **Request:**

  ```json
  {
    "event": "schedule:cancel",
    "data": { /* Call object of the scheduled call to be canceled/activated */
      "id": "string (uuid)",
      "phone": "string",
      // ... other fields from the scheduled call
    }
  }
  ```

- **Response (Broadcast to all clients):**

  ```json
  [
    { // First, removal of the schedule
      "event": "schedule:remove",
      "data": "string (id of the removed scheduled call)"
    },
    { // Then, addition as a new active call
      "event": "call:add",
      "data": { /* New active Call object based on the scheduled call, with updated timestamp */ }
    }
  ]
  ```

- **Authentication:** WebSocket (Operator Session).

### Business Rules

- Backend (`schedule.js` via `eventHandlers.js`):
  - Removes the scheduled entry from Redis (`schedule:<id>`) and clears its timer. Broadcasts `schedule:remove`.
  - Creates a new active call:
    - `id` is set to `null` (so `call:add` generates a new one).
    - `timestamp` is set to `Date.now()`.
    - `arrivaltime` is calculated based on the difference between original scheduled `timestamp` and `Date.now()`.
    - `repeat` field is deleted.
    - If `timeassigned` was set on the schedule, it's updated to `Date.now() + 1ms` (hack for `activeOrders`).
  - This new call object is then processed via `restricted["call:add"]` (the main handler for adding live calls), which saves it to PostgreSQL and broadcasts `call:add`.

### Data Model

```mermaid
erDiagram
  SCHEDULED_CALLS_REDIS {
    string id PK
  }
  CALLS {
    uuid id PK
    timestamp timestamp
    string source
  }
  SCHEDULED_CALLS_REDIS --|> CALLS : "is_converted_to"
```

### Error Handling

- If the scheduled call ID doesn't exist.
- Errors during the subsequent `call:add` process.
