import { pgEnum } from "drizzle-orm/pg-core";

// 1. Define enum values as the single source of truth
const INVOICE_STATUS_VALUES = ["PENDING", "PAID", "OPEN", "FAILED"] as const;

// 2. Create the Drizzle ORM pgEnum for database schema definition
export const invoiceStatusEnum = pgEnum(
	"invoice_status",
	INVOICE_STATUS_VALUES,
);

// 3. Define TypeScript union type for strict type safety
export type InvoiceStatusType = (typeof INVOICE_STATUS_VALUES)[number];

// 4. Define TypeScript string enum for application logic
export enum InvoiceStatus {
	PENDING = "PENDING",
	PAID = "PAID",
	OPEN = "OPEN",
	FAILED = "FAILED",
}
