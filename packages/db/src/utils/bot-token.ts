import { <PERSON><PERSON><PERSON> } from "node:buffer"; // Explicit import for <PERSON><PERSON><PERSON>
import crypto from "node:crypto";

const ALGORITHM = "aes-256-gcm";
const IV_LENGTH = 12; // For GCM, 12 bytes is a common IV length
const SALT_LENGTH = 16;
// const TAG_LENGTH = 16; // GCM authentication tag length (handled automatically by GCM)
const KEY_LENGTH = 32; // For AES-256
const PBKDF2_ITERATIONS = 100000; // Number of iterations for PBKDF2

/**
 * Derives a key from a master key and salt using PBKDF2.
 * @param masterKey The master encryption key (from environment variables).
 * @param salt A random salt.
 * @returns A derived key.
 */
function deriveKey(masterKey: string, salt: Buffer): Buffer {
	return crypto.pbkdf2Sync(
		masterKey,
		salt,
		PBKDF2_ITERATIONS,
		KEY_LENGTH,
		"sha512",
	);
}

/**
 * Encrypts a bot token using AES-256-GCM.
 *
 * @param token The bot token to encrypt.
 * @param secretKey Your master encryption key (should be stored securely, e.g., in environment variables).
 * @returns A string in the format "salt:iv:authtag:encryptedToken" (all hex encoded), or null if encryption fails.
 */
export function encryptBotToken(
	token: string,
	secretKey?: string,
): string | null {
	if (!token) {
		console.error("Token is required for encryption.");
		return null;
	}

	// Use environment variable if secretKey is not provided
	const key = secretKey || process.env.TOKEN_ENCRYPTION_KEY;
	if (!key) {
		console.error(
			"Secret key is required for encryption. Set TOKEN_ENCRYPTION_KEY environment variable.",
		);
		return null;
	}

	try {
		const salt = crypto.randomBytes(SALT_LENGTH);
		const derivedKey = deriveKey(key, salt);
		const iv = crypto.randomBytes(IV_LENGTH);

		const cipher = crypto.createCipheriv(ALGORITHM, derivedKey, iv);
		let encrypted = cipher.update(token, "utf8", "hex");
		encrypted += cipher.final("hex");
		const tag = cipher.getAuthTag();

		return `${salt.toString("hex")}:${iv.toString("hex")}:${tag.toString("hex")}:${encrypted}`;
	} catch (error) {
		console.error("Encryption failed:", error);
		return null;
	}
}

/**
 * Decrypts a bot token encrypted with AES-256-GCM.
 *
 * @param encryptedToken The encrypted token string (format: "salt:iv:authtag:encryptedToken").
 * @param secretKey Your master encryption key.
 * @returns The decrypted token string, or null if decryption fails.
 */
export function decryptBotToken(
	encryptedToken: string,
	secretKey?: string,
): string | null {
	if (!encryptedToken) {
		console.error("Encrypted token is required for decryption.");
		return null;
	}

	// Use environment variable if secretKey is not provided
	const key = secretKey || process.env.TOKEN_ENCRYPTION_KEY;
	if (!key) {
		console.error(
			"Secret key is required for decryption. Set TOKEN_ENCRYPTION_KEY environment variable.",
		);
		return null;
	}

	try {
		const parts = encryptedToken.split(":");
		if (parts.length !== 4) {
			console.error("Invalid encrypted token format.");
			return null;
		}

		const [saltHex, ivHex, authTagHex, encryptedDataHex] = parts;
		const salt = Buffer.from(saltHex, "hex");
		const iv = Buffer.from(ivHex, "hex");
		const authTag = Buffer.from(authTagHex, "hex");

		const derivedKey = deriveKey(key, salt);

		const decipher = crypto.createDecipheriv(ALGORITHM, derivedKey, iv);
		decipher.setAuthTag(authTag);
		let decrypted = decipher.update(encryptedDataHex, "hex", "utf8");
		decrypted += decipher.final("utf8");

		return decrypted;
	} catch (error) {
		console.error("Decryption failed:", error);
		return null;
	}
}
