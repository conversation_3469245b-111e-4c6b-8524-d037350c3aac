import { pgEnum } from "drizzle-orm/pg-core";

// 1. Define enum values as the single source of truth
const BILLING_METHOD_VALUES = [
	"CREDIT_CARD",
	"PAYPAL",
	"BANK_TRANSFER",
	"CRYPTO",
] as const;

// 2. Create the Drizzle ORM pgEnum for database schema definition
export const billingMethodEnum = pgEnum(
	"billing_method",
	BILLING_METHOD_VALUES,
);

// 3. Define TypeScript union type for strict type safety
export type BillingMethodType = (typeof BILLING_METHOD_VALUES)[number];

// 4. Define TypeScript string enum for application logic
export enum BillingMethod {
	CREDIT_CARD = "CREDIT_CARD",
	PAYPAL = "PAYPAL",
	BANK_TRANSFER = "BANK_TRANSFER",
	CRYPTO = "CRYPTO",
}
