import { relations } from "drizzle-orm";
import {
	boolean,
	pgTable,
	timestamp,
	uniqueIndex,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { botStatusEnum } from "../enums/bot-status.enum.js";
import { tenants } from "./tenants.schema.js";
import { userBotRoles } from "./user-bot-roles.schema.js";

/**
 * Schema for tenant bots table
 * Represents a Telegram bot configuration for a specific tenant
 */
export const tenantBots = pgTable(
	"tenant_bots",
	{
		// Unique identifier
		id: uuid("id").primaryKey().defaultRandom(),

		// Tenant ID (foreign key to tenants table)
		tenantId: uuid("tenant_id")
			.references(() => tenants.id)
			.notNull(),

		// Bot name (used for webhook path and identification)
		botName: varchar("bot_name", { length: 100 }), // e.g., "main", "support", etc.

		// Telegram bot token
		telegramBotToken: varchar("telegram_bot_token", { length: 255 }).notNull(),

		// Webhook configuration
		webhookDomain: varchar("webhook_domain", { length: 255 }),
		webhookPath: varchar("webhook_path", { length: 255 }),
		webhookSecretToken: varchar("webhook_secret_token", { length: 255 }),

		// Bot status
		status: botStatusEnum("status").default("STOPPED").notNull(),
		lastStatusUpdate: timestamp("last_status_update", { withTimezone: true })
			.defaultNow()
			.notNull(),

		// Whether the bot is active
		isActive: boolean("is_active").default(true).notNull(),

		// Timestamps
		createdAt: timestamp("created_at", { withTimezone: true })
			.defaultNow()
			.notNull(),
		updatedAt: timestamp("updated_at", { withTimezone: true })
			.defaultNow()
			.notNull(),
	},
	(table) => {
		return [uniqueIndex("telegram_bot_token_idx").on(table.telegramBotToken)];
	},
);

export const tenantBotsRelations = relations(tenantBots, ({ one, many }) => {
	// Import here to avoid circular dependencies
	// Note: For relations, we can reference by name string instead of importing

	return {
		tenant: one(tenants, {
			fields: [tenantBots.tenantId],
			references: [tenants.id],
		}),
		userBotRoles: many(userBotRoles),
	};
});

export type TenantBot = typeof tenantBots.$inferSelect;
export type NewTenantBot = typeof tenantBots.$inferInsert;
