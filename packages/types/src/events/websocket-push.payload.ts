// packages/types/src/events/websocket-push.payload.ts

/**
 * Defines the structure of a notification to be pushed via WebSocket to clients.
 * This is distinct from internal inter-service event payloads.
 */
export interface WsPushNotificationPayload<TData = any> {
	/** Unique ID for this specific push notification. */
	pushId: string;

	/** ISO 8601 timestamp of when the push notification was generated. */
	timestamp: string;

	/** Identifier for the tenant this push notification pertains to. */
	tenantId: string;

	/** Optional: ID of the original event that triggered this push, for correlation. */
	correlationId?: string;

	/** Category/type of the push, e.g., 'schedule.created', 'call.updated', 'general.notification'. */
	type: string;

	/** Optional title for UI display (e.g., for a toast header). */
	title?: string;
	/** Optional message content for UI display (e.g., for a toast body). */
	message?: string;

	/** The actual data payload relevant to this push notification, for UI consumption. */
	payload: TData;

	/** Optional: Specific room/topic to target on the WebSocket server. */
	targetRoom?: string;
	/** Optional: Array of specific user IDs to target. */
	targetUserIds?: string[];
}
