import { relations, sql } from "drizzle-orm";
import {
	doublePrecision,
	geometry,
	index,
	pgTable,
	text,
	timestamp,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { tenants } from "./tenants.schema.js";

export const addresses = pgTable(
	"addresses",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id").references(() => tenants.id, {
			onDelete: "cascade",
		}),

		// Structured Address Components
		streetLine1: varchar("street_line_1", { length: 255 }),
		streetLine2: varchar("street_line_2", { length: 255 }),
		city: varchar("city", { length: 100 }),
		stateProvince: varchar("state_province", { length: 100 }),
		postalCode: varchar("postal_code", { length: 20 }),
		countryCode: varchar("country_code", { length: 2 }).notNull(),

		// Full formatted address
		formattedAddress: text("formatted_address"),
		rawInputAddress: text("raw_input_address"),

		// Geospatial Information
		geom: geometry("geom", {
			type: "point",
			mode: "xy",
			srid: 4326,
		}),
		latitude: doublePrecision("latitude"),
		longitude: doublePrecision("longitude"),

		// Geocoding Metadata
		geocodingProvider: varchar("geocoding_provider", { length: 50 }),
		geocodingAccuracy: varchar("geocoding_accuracy", { length: 50 }),
		osmPlaceId: varchar("osm_place_id", { length: 50 }),

		createdAt: timestamp("created_at").defaultNow().notNull(),
		updatedAt: timestamp("updated_at")
			.defaultNow()
			.notNull()
			.$onUpdate(() => new Date()),
	},
	(table) => [
		index("addresses_tenant_id_idx").on(table.tenantId),
		index("addresses_postal_code_country_idx").on(
			table.postalCode,
			table.countryCode,
		),
		index("addresses_geom_idx")
			.using("gist", table.geom)
			.where(sql`${table.geom} IS NOT NULL`),
	],
);

export const addressesRelations = relations(addresses, ({ one }) => ({
	tenant: one(tenants, {
		fields: [addresses.tenantId],
		references: [tenants.id],
	}),
}));

export type Address = typeof addresses.$inferSelect;
export type NewAddress = typeof addresses.$inferInsert;
