// Generated GraphQL TypeScript definitions for FreePBX
// Generated on: 2025-06-04T03:07:28.634Z


// Query
export interface Query {
  system: system;
  fetchAsteriskDetails: system;
  fetchDBStatus: system;
  fetchGUIMode: system;
  fetchAutomaticUpdate: system;
  fetchSetupWizard: UpdatestatusConnection;
  fetchAllModuleStatus: ModuleConnection;
  fetchModuleStatus: module;
  fetchApiStatus: module;
  fetchNeedReload: module;
  fetchInstalledModules: module;
  allAnnouncements: AnnouncementConnection;
  announcement: announcement;
  allAriManagerUsers: ArimanageruserConnection;
  ariManagerUser: ariManagerUser;
  fetchAllBackups: BackupConnection;
  fetchAllBackupConfigurations: BackupConnection;
  deleteBackup: BackupConnection;
  allBlacklists: BlacklistConnection;
  blacklist: blacklist;
  blacklistSettings: blacklistsettings;
  allCallbacks: CallbackConnection;
  callback: callback;
  fetchAllCdrs: CdrConnection;
  fetchCdr: cdr;
  fetchCSRFile: certman;
  fetchAllCoreDevices: CoredeviceConnection;
  fetchCoreDevice: coredevice;
  allCoreUsers: CoreuserConnection;
  coreUser: coreuser;
  fetchAllExtensions: ExtensionConnection;
  fetchAllValidExtensions: ExtensionConnection;
  fetchExtension: extension;
  allInboundRoutes: DidConnection;
  inboundRoute: did;
  fetchAllAdvanceSettings: AdvacesettingsConnection;
  fetchAdvanceSetting: advacesettings;
  checkdiskspace: DiskspaceConnection;
  fetchFilestoreTypes: FilestoreConnection;
  fetchFilestoreLocations: FilestoreConnection;
  fetchAWSRegion: FilestoreConnection;
  fetchAllFilestores: FilestoreConnection;
  fetchFileStoreDetails: FilestoreConnection;
  fetchFollowMe: findmefollow;
  allMusiconholds: MusiconholdConnection;
  musiconhold: musiconhold;
  fetchPm2AppStatus: Pm2Connection;
  fetchAllRecordings: RecordingsConnection;
  fetchRecordingFiles: RecordingsConnection;
  fetchAllRingGroups: RinggroupConnection;
  fetchRingGroup: ringgroup;
  fetchSipNatNetworkSettings: SipsettingsConnection;
  fetchWSSettings: SipsettingsConnection;
  fetchVoiceMail: voiceMail;
  node: any;
}

// System Information
export interface system {
  id: string;
  version: string;
  engine: string;
  needReload: boolean;
  message: string;
  status: boolean;
  asteriskStatus: string;
  asteriskVersion: string;
  amiStatus: string;
  dbStatus: string;
  guiMode: string;
  systemUpdates: string;
  moduleUpdates: string;
  moduleSecurityUpdates: string;
}

// A connection to a list of items.
export interface UpdatestatusConnection {
  pageInfo: PageInfo;
  edges: UpdatestatusEdge[];
  autoupdates: updatestatus[];
  message: string;
  status: boolean;
}

// Information about pagination in a connection.
export interface PageInfo {
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  startCursor: string;
  endCursor: string;
}

// An edge in a connection
export interface UpdatestatusEdge {
  node: updatestatus;
  cursor: string;
}

// updatestatus
export interface updatestatus {
  id: string;
  modules: string;
}

// Module status
export enum ModuleStatus {
  notInstalled = "notInstalled",
  disabled = "disabled",
  enabled = "enabled",
  needUpgrade = "needUpgrade",
  broken = "broken",
}

// A connection to a list of items.
export interface ModuleConnection {
  pageInfo: PageInfo;
  edges: ModuleEdge[];
  totalCount: number;
  modules: module[];
}

// An edge in a connection
export interface ModuleEdge {
  node: module;
  cursor: string;
}

// Used to manage module specific operations
export interface module {
  id: string;
  status: boolean;
  rawname: string;
  repo: string;
  name: string;
  displayname: string;
  version: string;
  dbversion: string;
  publisher: string;
  license: string;
  licenselink: string;
  changelog: string;
  category: string;
  message: string;
  module: ModuleStatus;
  details: string;
  event_output: string;
  state: string;
  modules: module[];
}

// A connection to a list of items.
export interface AnnouncementConnection {
  pageInfo: PageInfo;
  edges: AnnouncementEdge[];
  totalCount: number;
  announcements: announcement[];
}

// An edge in a connection
export interface AnnouncementEdge {
  node: announcement;
  cursor: string;
}

// Plays back one of the system recordings (optionally allowing the user to skip it) and then goes to another destination
export interface announcement {
  id: string;
  announcement_id: number;
  description: string;
  allow_skip: boolean;
  return_ivr: boolean;
  noanswer: boolean;
  repeat_msg: number;
  destinationConnection: any;
}

// A destination that does not have a GraphQL reference
export interface unknowndestination {
  id: string;
}

// Used to tell your PBX where to route inbound calls based on the phone number or DID dialed
export interface did {
  id: string;
  extension: string;
  cidnum: string;
  description: string;
  privacyman: boolean;
  alertinfo: string;
  ringing: boolean;
  mohclass: string;
  grppre: string;
  delay_answer: number;
  pricid: boolean;
  pmmaxretries: string;
  pmminlength: string;
  reversal: boolean;
  rvolume: string;
  fanswer: boolean;
  destinationConnection: string;
}

// A connection to a list of items.
export interface ArimanageruserConnection {
  pageInfo: PageInfo;
  edges: ArimanageruserEdge[];
  totalCount: number;
  ariManagerUsers: ariManagerUser[];
}

// An edge in a connection
export interface ArimanageruserEdge {
  node: ariManagerUser;
  cursor: string;
}

// %description%
export interface ariManagerUser {
  id: string;
  arimanager_id: number;
  name: string;
  password: string;
  password_format: string;
  read_only: boolean;
}

// A connection to a list of items.
export interface BackupConnection {
  pageInfo: PageInfo;
  edges: BackupEdge[];
  message: string;
  status: boolean;
  fileDetails: backup[];
  backupConfigurations: backup[];
}

// An edge in a connection
export interface BackupEdge {
  node: backup;
  cursor: string;
}

// %description%
export interface backup {
  id: string;
  name: string;
  status: boolean;
  message: string;
  type: string;
  file: string;
  size: string;
  framework: string;
  timestamp: string;
  description: string;
  instancename: string;
}

// A connection to a list of items.
export interface BlacklistConnection {
  pageInfo: PageInfo;
  edges: BlacklistEdge[];
  totalCount: number;
  blacklists: blacklist[];
}

// An edge in a connection
export interface BlacklistEdge {
  node: blacklist;
  cursor: string;
}

// Used to manage a system wide list of blocked callers
export interface blacklist {
  id: string;
  number: string;
  description: string;
}

// Blacklist Settings
export interface blacklistsettings {
  blockUnknown: boolean;
  destination: string;
}

// A connection to a list of items.
export interface CallbackConnection {
  pageInfo: PageInfo;
  edges: CallbackEdge[];
  totalCount: number;
  callbacks: callback[];
}

// An edge in a connection
export interface CallbackEdge {
  node: callback;
  cursor: string;
}

// callback
export interface callback {
  id: string;
  callback_id: string;
  description: string;
  callbacknum: string;
  destination: string;
  sleep: number;
}

// Dispositions represent the final state of the call from the perspective of Party A
export enum cdrOrderBy {
  duration = "duration",
  date = "date",
}

// A connection to a list of items.
export interface CdrConnection {
  pageInfo: PageInfo;
  edges: CdrEdge[];
  totalCount: number;
  cdrs: cdr[];
  message: string;
  status: boolean;
}

// An edge in a connection
export interface CdrEdge {
  node: cdr;
  cursor: string;
}

// Used to manage a system wide list of blocked callers
export interface cdr {
  id: string;
  uniqueid: string;
  calldate: string;
  timestamp: number;
  clid: string;
  src: string;
  dst: string;
  dcontext: string;
  channel: string;
  dstchannel: string;
  lastapp: string;
  lastdata: string;
  duration: number;
  billsec: number;
  disposition: string;
  amaflags: string;
  accountcode: string;
  userfield: string;
  did: string;
  recordingfile: string;
  cnum: string;
  outbound_cnum: string;
  outbound_cnam: string;
  dst_cnam: string;
  linkedid: string;
  peeraccount: string;
  sequence: string;
  message: string;
  status: boolean;
}

// Generate Certificate
export interface certman {
  id: string;
  message: string;
  status: boolean;
  fileContant: string;
}

// A connection to a list of items.
export interface CoredeviceConnection {
  pageInfo: PageInfo;
  edges: CoredeviceEdge[];
  totalCount: number;
  coreDevice: coredevice[];
  status: boolean;
  message: string;
}

// An edge in a connection
export interface CoredeviceEdge {
  node: coredevice;
  cursor: string;
}

// coredevice
export interface coredevice {
  id: string;
  deviceId: string;
  tech: string;
  dial: string;
  devicetype: string;
  user: coreuser;
  description: string;
  emergencyCid: string;
  coreDevice: coredevice[];
  status: boolean;
  message: string;
}

// coreuser
export interface coreuser {
  id: string;
  extension: string;
  password: string;
  name: string;
  voicemail: string;
  ringtimer: number;
  noanswer: string;
  recording: string;
  outboundCid: string;
  sipname: string;
  extPassword: string;
  noanswerCid: string;
  busyCid: string;
  chanunavailCid: string;
  noanswerDestination: string;
  busyDestination: string;
  chanunavailDestination: string;
  mohclass: string;
  callwaiting: boolean;
  recording_in_external: string;
  recording_out_external: string;
  recording_in_internal: string;
  recording_out_internal: string;
  recording_ondemand: string;
  recording_priority: number;
  callforward_unconditional: string;
  callforward_busy: string;
  callforward_all: string;
  callforward_ringtimer: number;
  donotdisturb: boolean;
}

// A connection to a list of items.
export interface CoreuserConnection {
  pageInfo: PageInfo;
  edges: CoreuserEdge[];
  totalCount: number;
  coreUser: coreuser[];
}

// An edge in a connection
export interface CoreuserEdge {
  node: coreuser;
  cursor: string;
}

// A connection to a list of items.
export interface ExtensionConnection {
  pageInfo: PageInfo;
  edges: ExtensionEdge[];
  totalCount: number;
  count: number;
  extension: extension[];
  status: boolean;
  message: string;
}

// An edge in a connection
export interface ExtensionEdge {
  node: extension;
  cursor: string;
}

// extension
export interface extension {
  id: string;
  extensionId: string;
  tech: string;
  user: coreuser;
  coreDevice: coredevice;
  status: boolean;
  message: string;
}

// A connection to a list of items.
export interface DidConnection {
  pageInfo: PageInfo;
  edges: DidEdge[];
  totalCount: number;
  inboundRoutes: did[];
}

// An edge in a connection
export interface DidEdge {
  node: did;
  cursor: string;
}

// A connection to a list of items.
export interface AdvacesettingsConnection {
  pageInfo: PageInfo;
  edges: AdvacesettingsEdge[];
  settings: advacesettings[];
  status: boolean;
  message: string;
}

// An edge in a connection
export interface AdvacesettingsEdge {
  node: advacesettings;
  cursor: string;
}

// advacesettings
export interface advacesettings {
  id: string;
  keyword: string;
  value: string;
  name: string;
  category: string;
  description: string;
  status: boolean;
  message: string;
}

// A connection to a list of items.
export interface DiskspaceConnection {
  pageInfo: PageInfo;
  edges: DiskspaceEdge[];
  totalCount: number;
  diskspace: diskspace[];
  message: string;
  status: boolean;
}

// An edge in a connection
export interface DiskspaceEdge {
  node: diskspace;
  cursor: string;
}

// Read the System licence information
export interface diskspace {
  id: string;
  storage_path: string;
  available_space: string;
  used_space: string;
  total_size: string;
  used_percentage: string;
  message: string;
  status: boolean;
}

// A connection to a list of items.
export interface FilestoreConnection {
  pageInfo: PageInfo;
  edges: FilestoreEdge[];
  message: string;
  status: boolean;
  types: string[];
  regions: string;
  locations: string[];
  filestores: filestore[];
  serverName: string;
  hostName: string;
  description: string;
  port: string;
  userName: string;
  password: string;
  fileStoreType: string;
  path: string;
  transferMode: string;
  timeout: string;
}

// An edge in a connection
export interface FilestoreEdge {
  node: filestore;
  cursor: string;
}

// filestore
export interface filestore {
  id: string;
  status: boolean;
  message: string;
  name: string;
  description: string;
  filestoreType: string;
}

// Used to manage Follow Me
export interface findmefollow {
  id: string;
  status: boolean;
  message: string;
  enabled: boolean;
  extensionId: string;
  strategy: ringstrategiesv2;
  ringTime: number;
  followMePrefix: string;
  followMeList: string;
  callerMessage: string;
  noAnswerDestination: string;
  alertInfo: string;
  confirmCalls: boolean;
  receiverMessageConfirmCall: string;
  receiverMessageTooLate: string;
  ringingMusic: string;
  initialRingTime: number;
  voicemail: string;
  enableCalendar: boolean;
  matchCalendar: boolean;
  calendar: string;
  calendarGroup: string;
  overrideRingerVolume: number;
  externalCallerIdMode: externalcidmode;
  fixedCallerId: string;
}

// Ring Strategies (including v2)
export enum ringstrategiesv2 {
  ringallv2 = "ringallv2",
  ringallv2prim = "ringallv2prim",
  ringall = "ringall",
  ringallprim = "ringallprim",
  hunt = "hunt",
  huntprim = "huntprim",
  memoryhunt = "memoryhunt",
  memoryhuntprim = "memoryhuntprim",
  firstavailable = "firstavailable",
  firstnotonphone = "firstnotonphone",
}

// External Caller Id Modes
export enum externalcidmode {
  default = "default",
  fixed = "fixed",
  extern = "extern",
  did = "did",
  forcedid = "forcedid",
}

// A connection to a list of items.
export interface MusiconholdConnection {
  pageInfo: PageInfo;
  edges: MusiconholdEdge[];
  totalCount: number;
  musiconholds: musiconhold[];
}

// An edge in a connection
export interface MusiconholdEdge {
  node: musiconhold;
  cursor: string;
}

// Used to manage a system wide list of blocked callers
export interface musiconhold {
  id: string;
  category: string;
  type: string;
  random: boolean;
  application: string;
  format: string;
}

// A connection to a list of items.
export interface Pm2Connection {
  pageInfo: PageInfo;
  edges: Pm2Edge[];
  apps: pm2[];
  message: string;
  status: boolean;
}

// An edge in a connection
export interface Pm2Edge {
  node: pm2;
  cursor: string;
}

// Process management
export interface pm2 {
  id: string;
  status: string;
  message: string;
  name: string;
  PID: string;
  memory: string;
  uptime: string;
}

// A connection to a list of items.
export interface RecordingsConnection {
  pageInfo: PageInfo;
  edges: RecordingsEdge[];
  message: string;
  status: boolean;
  recordings: recordings[];
  recodingFiles: string[];
}

// An edge in a connection
export interface RecordingsEdge {
  node: recordings;
  cursor: string;
}

// Sytem Recordings
export interface recordings {
  id: string;
  name: string;
  description: string;
  fcode: string;
  fcode_pass: string;
  language: string;
  playback: string[];
  status: boolean;
  message: string;
  languages: string[];
}

// A connection to a list of items.
export interface RinggroupConnection {
  pageInfo: PageInfo;
  edges: RinggroupEdge[];
  totalCount: number;
  ringgroups: ringgroup[];
  message: string;
  status: boolean;
}

// An edge in a connection
export interface RinggroupEdge {
  node: ringgroup;
  cursor: string;
}

// Used to set ringgroup values
export interface ringgroup {
  id: string;
  groupNumber: number;
  description: string;
  groupList: string;
  groupTime: number;
  groupPrefix: string;
  needConf: boolean;
  overrideRingerVolume: string;
  changecid: string;
  fixedcid: string;
  callRecording: string;
  pickupCall: boolean;
  callProgress: boolean;
  answeredElseWhere: boolean;
  ignoreCallForward: boolean;
  ignoreCallWait: boolean;
  alertInfo: string;
  recevierMessageConfirmCall: string;
  recevierMessage: string;
  receiverMessageConfirmCall: string;
  receiverMessage: string;
  postAnswer: string;
  callerMessage: string;
  ringingMusic: string;
  strategy: ringstrategies;
  message: string;
  status: boolean;
}

// Ring Strategies
export enum ringstrategies {
  ringall = "ringall",
  ringallprim = "ringallprim",
  hunt = "hunt",
  huntprim = "huntprim",
  memoryhunt = "memoryhunt",
  memoryhuntprim = "memoryhuntprim",
  firstavailable = "firstavailable",
  firstnotonphone = "firstnotonphone",
}

// A connection to a list of items.
export interface SipsettingsConnection {
  pageInfo: PageInfo;
  edges: SipsettingsEdge[];
  localIP: sipsettings[];
  routes: sipsettings[];
  message: string;
  status: boolean;
  externIP: string;
  ws: sipsettings[];
  wss: sipsettings[];
}

// An edge in a connection
export interface SipsettingsEdge {
  node: sipsettings;
  cursor: string;
}

// Sipsettings management
export interface sipsettings {
  id: string;
  status: boolean;
  message: string;
  net: string;
  mask: string;
  interface: string;
  state: string;
}

// Read the Voicemail information
export interface voiceMail {
  id: string;
  status: boolean;
  message: string;
  context: string;
  password: string;
  name: string;
  email: string;
  pager: string;
  attach: string;
  saycid: string;
  envelope: string;
  delete: string;
}

// Mutation
export interface Mutation {
  addInitialSetup: updateAdminAuthPayload;
  updateSystemRPM: updateSystemRPMPayload;
  switchAstriskVersion: switchAstriskVersionPayload;
  moduleOperations: moduleOperationsPayload;
  installModule: installModulePayload;
  uninstallModule: uninstallModulePayload;
  enableModule: enableModulePayload;
  disableModule: disableModulePayload;
  upgradeModule: upgradeModulePayload;
  upgradeAllModules: upgradeAllModulePayload;
  doreload: doreloadPayload;
  fwconsoleCommand: fwconsoleCommandPayload;
  addAnnouncement: addAnnouncementPayload;
  updateAnnouncement: updateAnnouncementPayload;
  removeAnnouncement: removeAnnouncementPayload;
  addAriManagerUsers: addAriManagerUsersPayload;
  updateAriManagerUsers: updateAriManagerUsersPayload;
  removeAriManagerUsers: removeAriManagerUsersPayload;
  runWarmsparebackuprestore: RunWarmSpareRestorePayload;
  addBackup: addBackupPayload;
  updateBackup: updateBackupPayload;
  restoreBackup: restoreBackupPayload;
  runBackup: runBackupPayload;
  addBlacklist: addBlacklistPayload;
  setBlacklistSettings: setBlacklistSettingsPayload;
  removeBlacklist: removeBlacklistPayload;
  addCallback: addCallbackPayload;
  updateCallback: updateCallbackPayload;
  removeCallback: removeCallbackPayload;
  generateCSR: generateCSRPayload;
  uploadSSLCertificate: uploadSSLCertificatePayload;
  deleteCSRFile: deleteCSRFilePayload;
  deleteCertificate: deleteCertificatePayload;
  updateDefaultCertificate: updateDefaultCertificatePayload;
  addCoreDevice: addCoreDevicePayload;
  updateCoreDevice: updateCoreDevicePayload;
  deleteCoreDevice: deleteCoreDevicePayload;
  addCoreUser: addCoreUserPayload;
  updateCoreUser: updateCoreUserPayload;
  removeCoreUser: removeCoreUserPayload;
  addExtension: addExtensionPayload;
  updateExtension: updateExtensionPayload;
  deleteExtension: deleteExtensionPayload;
  createRangeofExtension: CreateRangeofExtensionPayload;
  addInboundRoute: addInboundRoutePayload;
  updateInboundRoute: updateInboundRoutePayload;
  removeInboundRoute: removeInboundRoutePayload;
  updateAdvanceSettings: updateSettingsPayload;
  addFTPInstance: addFTPInstancePayload;
  addS3Bucket: addS3BucketPayload;
  updateFTPInstance: updateFTPInstancePayload;
  deleteFTPInstance: deleteFTPInstancePayload;
  updateFollowMe: updateFollowMePayload;
  enableFollowMe: enableFollowMePayload;
  disableFollowMe: disableFollowMePayload;
  addRecording: addRecordingPayload;
  updateRecording: updateRecordingPayload;
  deleteRecording: deleteRecordingPayload;
  convertfile: convertfilePayload;
  addRingGroup: addRingGroupPayload;
  updateRingGroup: updateRingGroupPayload;
  deleteRingGroup: DeleteRingGroupPayload;
  addSipNatLocalIp: addSipNatLocalIpPayload;
  updateSipNatExternalIp: updateSipNatExternalIpPayload;
  updateWSSettings: updateWSSettingsPayload;
  enableVoiceMail: enableVoiceMailPayload;
  disableVoiceMail: disableVoiceMailPayload;
}

// updateAdminAuthInput
export interface updateAdminAuthInputInput {
  username: string;
  password: string;
  notificationEmail: string;
  systemIdentifier: string;
  autoModuleUpdate: string;
  autoModuleSecurityUpdate: string;
  securityEmailUnsignedModules: string;
  updateDay: string;
  updatePeriod: string;
  clientMutationId: string;
}

// updateAdminAuthPayload
export interface updateAdminAuthPayload {
  message: string;
  status: boolean;
  transaction_id: string;
  clientMutationId: string;
}

// updateSystemRPMInput
export interface updateSystemRPMInputInput {
  clientMutationId: string;
}

// updateSystemRPMPayload
export interface updateSystemRPMPayload {
  message: string;
  status: boolean;
  transaction_id: string;
  clientMutationId: string;
}

// switchAstriskVersionInput
export interface switchAstriskVersionInputInput {
  asteriskVersion: string;
  clientMutationId: string;
}

// switchAstriskVersionPayload
export interface switchAstriskVersionPayload {
  message: string;
  status: boolean;
  transaction_id: string;
  clientMutationId: string;
}

// moduleOperationsInput
export interface moduleOperationsInputInput {
  module: string;
  action: string;
  clientMutationId: string;
}

// moduleOperationsPayload
export interface moduleOperationsPayload {
  status: boolean;
  message: string;
  transaction_id: string;
  clientMutationId: string;
}

// installModuleInput
export interface installModuleInputInput {
  module: string;
  forceDownload: boolean;
  track: string;
  clientMutationId: string;
}

// installModulePayload
export interface installModulePayload {
  status: boolean;
  message: string;
  transaction_id: string;
  clientMutationId: string;
}

// uninstallModuleInput
export interface uninstallModuleInputInput {
  module: string;
  RemoveCompletely: boolean;
  clientMutationId: string;
}

// uninstallModulePayload
export interface uninstallModulePayload {
  status: boolean;
  message: string;
  transaction_id: string;
  clientMutationId: string;
}

// enableModuleInput
export interface enableModuleInputInput {
  module: string;
  track: string;
  clientMutationId: string;
}

// enableModulePayload
export interface enableModulePayload {
  status: boolean;
  message: string;
  transaction_id: string;
  clientMutationId: string;
}

// disableModuleInput
export interface disableModuleInputInput {
  module: string;
  track: string;
  clientMutationId: string;
}

// disableModulePayload
export interface disableModulePayload {
  status: boolean;
  message: string;
  transaction_id: string;
  clientMutationId: string;
}

// upgradeModuleInput
export interface upgradeModuleInputInput {
  module: string;
  clientMutationId: string;
}

// upgradeModulePayload
export interface upgradeModulePayload {
  status: boolean;
  message: string;
  transaction_id: string;
  clientMutationId: string;
}

// upgradeAllModuleInput
export interface upgradeAllModuleInputInput {
  runReloadCommand: boolean;
  runChownCommand: boolean;
  clientMutationId: string;
}

// upgradeAllModulePayload
export interface upgradeAllModulePayload {
  status: boolean;
  message: string;
  transaction_id: string;
  clientMutationId: string;
}

// doreloadInput
export interface doreloadInputInput {
  clientMutationId: string;
}

// doreloadPayload
export interface doreloadPayload {
  status: boolean;
  message: string;
  transaction_id: string;
  clientMutationId: string;
}

// fwconsoleCommandInput
export interface fwconsoleCommandInputInput {
  command: command;
  clientMutationId: string;
}

// command
export enum command {
  r = "r",
  restart = "restart",
  reload = "reload",
  chown = "chown",
}

// fwconsoleCommandPayload
export interface fwconsoleCommandPayload {
  status: boolean;
  message: string;
  transaction_id: string;
  clientMutationId: string;
}

// addAnnouncementInput
export interface addAnnouncementInputInput {
  description: string;
  allow_skip: boolean;
  return_ivr: boolean;
  noanswer: boolean;
  repeat_msg: number;
  recording_id: number;
  post_dest: string;
  clientMutationId: string;
}

// addAnnouncementPayload
export interface addAnnouncementPayload {
  announcement: announcement;
  clientMutationId: string;
}

// updateAnnouncementInput
export interface updateAnnouncementInputInput {
  announcement_id: string;
  description: string;
  allow_skip: boolean;
  return_ivr: boolean;
  noanswer: boolean;
  repeat_msg: number;
  recording_id: number;
  post_dest: string;
  clientMutationId: string;
}

// updateAnnouncementPayload
export interface updateAnnouncementPayload {
  announcement: announcement;
  clientMutationId: string;
}

// removeAnnouncementInput
export interface removeAnnouncementInputInput {
  id: number;
  clientMutationId: string;
}

// removeAnnouncementPayload
export interface removeAnnouncementPayload {
  deletedId: string;
  clientMutationId: string;
}

// addAriManagerUsersInput
export interface addAriManagerUsersInputInput {
  id: string;
  name: string;
  password: string;
  password_format: string;
  read_only: boolean;
  clientMutationId: string;
}

// addAriManagerUsersPayload
export interface addAriManagerUsersPayload {
  ariManagerUser: ariManagerUser;
  clientMutationId: string;
}

// updateAriManagerUsersInput
export interface updateAriManagerUsersInputInput {
  id: string;
  name: string;
  password: string;
  password_format: string;
  read_only: boolean;
  clientMutationId: string;
}

// updateAriManagerUsersPayload
export interface updateAriManagerUsersPayload {
  ariManagerUser: ariManagerUser;
  clientMutationId: string;
}

// removeAriManagerUsersInput
export interface removeAriManagerUsersInputInput {
  id: string;
  clientMutationId: string;
}

// removeAriManagerUsersPayload
export interface removeAriManagerUsersPayload {
  deletedId: string;
  clientMutationId: string;
}

// RunWarmSpareRestoreInput
export interface RunWarmSpareRestoreInputInput {
  backupfilename: string;
  clientMutationId: string;
}

// RunWarmSpareRestorePayload
export interface RunWarmSpareRestorePayload {
  restorestatus: string;
  clientMutationId: string;
}

// addBackupInput
export interface addBackupInputInput {
  name: string;
  description: string;
  backupModules: string[];
  notificationEmail: string;
  inlineLogs: boolean;
  emailType: string;
  storageLocation: string[];
  appendBackupName: boolean;
  enableBackupSchedule: boolean;
  scheduleBackup: string;
  updatesToKeep: string;
  backupDaysToKeep: string;
  warmSpace: boolean;
  clientMutationId: string;
}

// addBackupPayload
export interface addBackupPayload {
  status: boolean;
  message: string;
  id: string;
  clientMutationId: string;
}

// updateBackupInput
export interface updateBackupInputInput {
  id: string;
  name: string;
  description: string;
  backupModules: string[];
  notificationEmail: string;
  inlineLogs: boolean;
  emailType: string;
  storageLocation: string[];
  appendBackupName: boolean;
  enableBackupSchedule: boolean;
  scheduleBackup: string;
  updatesToKeep: string;
  backupDaysToKeep: string;
  warmSpace: boolean;
  clientMutationId: string;
}

// updateBackupPayload
export interface updateBackupPayload {
  status: boolean;
  message: string;
  id: string;
  clientMutationId: string;
}

// restoreBackupInput
export interface restoreBackupInputInput {
  name: string;
  clientMutationId: string;
}

// restoreBackupPayload
export interface restoreBackupPayload {
  status: boolean;
  message: string;
  transaction_id: string;
  clientMutationId: string;
}

// runBackupInput
export interface runBackupInputInput {
  id: string;
  clientMutationId: string;
}

// runBackupPayload
export interface runBackupPayload {
  status: boolean;
  message: string;
  transaction_id: string;
  clientMutationId: string;
}

// addBlacklistInput
export interface addBlacklistInputInput {
  number: string;
  description: string;
  clientMutationId: string;
}

// addBlacklistPayload
export interface addBlacklistPayload {
  blacklist: blacklist;
  clientMutationId: string;
}

// setBlacklistSettingsInput
export interface setBlacklistSettingsInputInput {
  blockUnknown: boolean;
  destination: string;
  clientMutationId: string;
}

// setBlacklistSettingsPayload
export interface setBlacklistSettingsPayload {
  blockUnknown: boolean;
  destination: string;
  clientMutationId: string;
}

// removeBlacklistInput
export interface removeBlacklistInputInput {
  number: string;
  clientMutationId: string;
}

// removeBlacklistPayload
export interface removeBlacklistPayload {
  deletedId: string;
  clientMutationId: string;
}

// addCallbackInput
export interface addCallbackInputInput {
  id: string;
  description: string;
  callbacknum: string;
  destination: string;
  sleep: number;
  clientMutationId: string;
}

// addCallbackPayload
export interface addCallbackPayload {
  callback: callback;
  clientMutationId: string;
}

// updateCallbackInput
export interface updateCallbackInputInput {
  id: string;
  description: string;
  callbacknum: string;
  destination: string;
  sleep: number;
  clientMutationId: string;
}

// updateCallbackPayload
export interface updateCallbackPayload {
  callback: callback;
  clientMutationId: string;
}

// removeCallbackInput
export interface removeCallbackInputInput {
  id: string;
  clientMutationId: string;
}

// removeCallbackPayload
export interface removeCallbackPayload {
  deletedId: string;
  clientMutationId: string;
}

// generateCSRInput
export interface generateCSRInputInput {
  name: string;
  hostName: string;
  organizationName: string;
  organizationUnit: string;
  country: string;
  state: string;
  city: string;
  clientMutationId: string;
}

// generateCSRPayload
export interface generateCSRPayload {
  status: boolean;
  message: string;
  clientMutationId: string;
}

// uploadSSLCertificateInput
export interface uploadSSLCertificateInputInput {
  name: string;
  description: string;
  passPhrase: string;
  privateKey: string;
  CSRReference: string;
  signedCertificate: string;
  trustedChain: string;
  default: boolean;
  clientMutationId: string;
}

// uploadSSLCertificatePayload
export interface uploadSSLCertificatePayload {
  status: boolean;
  message: string;
  clientMutationId: string;
}

// deleteCSRFileInput
export interface deleteCSRFileInputInput {
  clientMutationId: string;
}

// deleteCSRFilePayload
export interface deleteCSRFilePayload {
  status: boolean;
  message: string;
  clientMutationId: string;
}

// deleteCertificateInput
export interface deleteCertificateInputInput {
  cid: string;
  clientMutationId: string;
}

// deleteCertificatePayload
export interface deleteCertificatePayload {
  status: boolean;
  message: string;
  clientMutationId: string;
}

// updateDefaultCertificateInput
export interface updateDefaultCertificateInputInput {
  cid: string;
  clientMutationId: string;
}

// updateDefaultCertificatePayload
export interface updateDefaultCertificatePayload {
  status: boolean;
  message: string;
  clientMutationId: string;
}

// addCoreDeviceInput
export interface addCoreDeviceInputInput {
  id: string;
  tech: string;
  dial: string;
  devicetype: string;
  user: string;
  description: string;
  emergency_cid: string;
  clientMutationId: string;
}

// addCoreDevicePayload
export interface addCoreDevicePayload {
  status: boolean;
  message: string;
  coreDevice: coredevice;
  clientMutationId: string;
}

// updateCoreDeviceInput
export interface updateCoreDeviceInputInput {
  id: string;
  tech: string;
  dial: string;
  devicetype: string;
  user: string;
  description: string;
  emergency_cid: string;
  clientMutationId: string;
}

// updateCoreDevicePayload
export interface updateCoreDevicePayload {
  status: boolean;
  message: string;
  coreDevice: coredevice;
  clientMutationId: string;
}

// deleteCoreDeviceInput
export interface deleteCoreDeviceInputInput {
  id: string;
  clientMutationId: string;
}

// deleteCoreDevicePayload
export interface deleteCoreDevicePayload {
  deletedId: string;
  status: boolean;
  message: string;
  clientMutationId: string;
}

// addCoreUserInput
export interface addCoreUserInputInput {
  extension: string;
  password: string;
  name: string;
  voicemail: string;
  ringtimer: number;
  noanswer: string;
  recording: string;
  outboundcid: string;
  sipname: string;
  noanswer_cid: string;
  busy_cid: string;
  chanunavail_cid: string;
  noanswer_dest: string;
  busy_dest: string;
  chanunavail_dest: string;
  mohclass: string;
  callwaiting: string;
  recording_in_external: string;
  recording_out_external: string;
  recording_in_internal: string;
  recording_out_internal: string;
  recording_ondemand: string;
  recording_priority: number;
  clientMutationId: string;
}

// addCoreUserPayload
export interface addCoreUserPayload {
  coreUser: coreuser;
  clientMutationId: string;
}

// updateCoreUserInput
export interface updateCoreUserInputInput {
  extension: string;
  password: string;
  name: string;
  voicemail: string;
  ringtimer: number;
  noanswer: string;
  recording: string;
  outboundcid: string;
  sipname: string;
  noanswer_cid: string;
  busy_cid: string;
  chanunavail_cid: string;
  noanswer_dest: string;
  busy_dest: string;
  chanunavail_dest: string;
  mohclass: string;
  callwaiting: string;
  recording_in_external: string;
  recording_out_external: string;
  recording_in_internal: string;
  recording_out_internal: string;
  recording_ondemand: string;
  recording_priority: number;
  clientMutationId: string;
}

// updateCoreUserPayload
export interface updateCoreUserPayload {
  coreuser: coreuser;
  clientMutationId: string;
}

// removeCoreUserInput
export interface removeCoreUserInputInput {
  extension: string;
  clientMutationId: string;
}

// removeCoreUserPayload
export interface removeCoreUserPayload {
  deletedId: string;
  clientMutationId: string;
}

// addExtensionInput
export interface addExtensionInputInput {
  extensionId: string;
  tech: string;
  channelName: string;
  name: string;
  outboundCid: string;
  emergencyCid: string;
  email: string;
  umEnable: boolean;
  umGroups: string;
  vmEnable: boolean;
  vmPassword: string;
  callerID: string;
  umPassword: string;
  maxContacts: string;
  clientMutationId: string;
}

// addExtensionPayload
export interface addExtensionPayload {
  status: boolean;
  message: string;
  clientMutationId: string;
}

// updateExtensionInput
export interface updateExtensionInputInput {
  extensionId: string;
  tech: string;
  channelName: string;
  name: string;
  outboundCid: string;
  emergencyCid: string;
  email: string;
  umEnable: boolean;
  umGroups: string;
  vmEnable: boolean;
  vmPassword: string;
  callerID: string;
  extPassword: string;
  umPassword: string;
  maxContacts: string;
  clientMutationId: string;
}

// updateExtensionPayload
export interface updateExtensionPayload {
  status: boolean;
  message: string;
  clientMutationId: string;
}

// deleteExtensionInput
export interface deleteExtensionInputInput {
  extensionId: string;
  clientMutationId: string;
}

// deleteExtensionPayload
export interface deleteExtensionPayload {
  status: boolean;
  message: string;
  clientMutationId: string;
}

// CreateRangeofExtensionInput
export interface CreateRangeofExtensionInputInput {
  startExtension: string;
  name: string;
  tech: string;
  numberOfExtensions: string;
  umEnable: boolean;
  outboundCid: string;
  umGroups: string;
  emergencyCid: string;
  email: string;
  vmEnable: boolean;
  vmPassword: string;
  callerID: string;
  channelName: string;
  clientMutationId: string;
}

// CreateRangeofExtensionPayload
export interface CreateRangeofExtensionPayload {
  status: boolean;
  message: string;
  clientMutationId: string;
}

// addInboundRouteInput
export interface addInboundRouteInputInput {
  extension: string;
  cidnum: string;
  description: string;
  privacyman: boolean;
  alertinfo: string;
  ringing: boolean;
  mohclass: string;
  grppre: string;
  delay_answer: number;
  pricid: boolean;
  pmmaxretries: string;
  pmminlength: string;
  reversal: boolean;
  rvolume: string;
  fanswer: boolean;
  destination: string;
  clientMutationId: string;
}

// addInboundRoutePayload
export interface addInboundRoutePayload {
  inboundRoute: did;
  status: boolean;
  message: string;
  clientMutationId: string;
}

// updateInboundRouteInput
export interface updateInboundRouteInputInput {
  extension: string;
  cidnum: string;
  oldExtension: string;
  oldCidnum: string;
  description: string;
  privacyman: boolean;
  alertinfo: string;
  ringing: boolean;
  mohclass: string;
  grppre: string;
  delay_answer: number;
  pricid: boolean;
  pmmaxretries: string;
  pmminlength: string;
  reversal: boolean;
  rvolume: string;
  fanswer: boolean;
  destination: string;
  clientMutationId: string;
}

// updateInboundRoutePayload
export interface updateInboundRoutePayload {
  inboundRoute: did;
  status: boolean;
  message: string;
  clientMutationId: string;
}

// removeInboundRouteInput
export interface removeInboundRouteInputInput {
  id: string;
  clientMutationId: string;
}

// removeInboundRoutePayload
export interface removeInboundRoutePayload {
  deletedId: string;
  status: boolean;
  message: string;
  clientMutationId: string;
}

// updateSettingsInput
export interface updateSettingsInputInput {
  keyword: keywordsList;
  value: string;
  clientMutationId: string;
}

// keywordsList
export enum keywordsList {
  AS_DISPLAY_FRIENDLY_NAME = "AS_DISPLAY_FRIENDLY_NAME",
  AS_DISPLAY_READONLY_SETTINGS = "AS_DISPLAY_READONLY_SETTINGS",
  AS_OVERRIDE_READONLY = "AS_OVERRIDE_READONLY",
  HTTPENABLESTATIC = "HTTPENABLESTATIC",
  HTTPENABLED = "HTTPENABLED",
  HTTPTLSENABLE = "HTTPTLSENABLE",
  HTTPWEBSOCKETMODE = "HTTPWEBSOCKETMODE",
  HTTPBINDADDRESS = "HTTPBINDADDRESS",
  HTTPBINDPORT = "HTTPBINDPORT",
  HTTPPREFIX = "HTTPPREFIX",
  HTTPTLSBINDADDRESS = "HTTPTLSBINDADDRESS",
  HTTPTLSBINDPORT = "HTTPTLSBINDPORT",
  TLSDISABLEV1 = "TLSDISABLEV1",
  TLSDISABLEV11 = "TLSDISABLEV11",
  TLSDISABLEV12 = "TLSDISABLEV12",
  HTTPTLSCERTFILE = "HTTPTLSCERTFILE",
  HTTPTLSPRIVATEKEY = "HTTPTLSPRIVATEKEY",
  HTTPSESSIONINACTIVITY = "HTTPSESSIONINACTIVITY",
  HTTPSESSIONKEEPALIVE = "HTTPSESSIONKEEPALIVE",
  HTTPSESSIONLIMIT = "HTTPSESSIONLIMIT",
  ASTMANAGERHOST = "ASTMANAGERHOST",
  AMPMGRPASS = "AMPMGRPASS",
  ASTMANAGERPORT = "ASTMANAGERPORT",
  ASTMANAGERPROXYPORT = "ASTMANAGERPROXYPORT",
  AMPMGRUSER = "AMPMGRUSER",
  ASTMGRWRITETIMEOUT = "ASTMGRWRITETIMEOUT",
  ARI_ALLOWED_ORIGINS = "ARI_ALLOWED_ORIGINS",
  ENABLE_ARI = "ENABLE_ARI",
  ENABLE_ARI_PP = "ENABLE_ARI_PP",
  ARI_WS_WRITE_TIMEOUT = "ARI_WS_WRITE_TIMEOUT",
  FPBX_ARI_USER = "FPBX_ARI_USER",
  FPBX_ARI_PASSWORD = "FPBX_ARI_PASSWORD",
  DAYNIGHTTCHOOK = "DAYNIGHTTCHOOK",
  CALLREC_BEEP_PERIOD = "CALLREC_BEEP_PERIOD",
  CALL_REC_OPTION = "CALL_REC_OPTION",
  CDR_BATCH_ENABLE = "CDR_BATCH_ENABLE",
  CDR_BATCH = "CDR_BATCH",
  CDR_BATCH_SIZE = "CDR_BATCH_SIZE",
  CDR_BATCH_TIME = "CDR_BATCH_TIME",
  CDR_BATCH_SCHEDULE_ONLY = "CDR_BATCH_SCHEDULE_ONLY",
  CDR_BATCH_SAFE_SHUT_DOWN = "CDR_BATCH_SAFE_SHUT_DOWN",
  TRANSIENTCDR = "TRANSIENTCDR",
  TRANSIENTCDRDATA = "TRANSIENTCDRDATA",
  CEL_ENABLED = "CEL_ENABLED",
  CELDBNAME = "CELDBNAME",
  CELDBTABLENAME = "CELDBTABLENAME",
  CERT_DAYS_EXPIRATION_ALERT = "CERT_DAYS_EXPIRATION_ALERT",
  CERT_DAYS_VAL = "CERT_DAYS_VAL",
  FORCEALLOWCONFRECORDING = "FORCEALLOWCONFRECORDING",
  ENABLE_FAVORITE_CONTACTS = "ENABLE_FAVORITE_CONTACTS",
  CONTACTMANLOOKUPLENGTH = "CONTACTMANLOOKUPLENGTH",
  JOBSRANDOMSLEEP = "JOBSRANDOMSLEEP",
  FWJOBS_LOGS = "FWJOBS_LOGS",
  SYS_STATS_DISABLE = "SYS_STATS_DISABLE",
  VIEW_FW_STATUS = "VIEW_FW_STATUS",
  SYS_STATS_MAXAGE = "SYS_STATS_MAXAGE",
  FORCE_JS_CSS_IMG_DOWNLOAD = "FORCE_JS_CSS_IMG_DOWNLOAD",
  AMPLOCALBIN = "AMPLOCALBIN",
  FPBXDBUGFILE = "FPBXDBUGFILE",
  DEVEL = "DEVEL",
  FPBXDBUGDISABLE = "FPBXDBUGDISABLE",
  DISABLE_CSS_AUTOGEN = "DISABLE_CSS_AUTOGEN",
  MODULEADMIN_SKIP_CACHE = "MODULEADMIN_SKIP_CACHE",
  DISPLAY_MONITOR_TRUNK_FAILURES_FIELD = "DISPLAY_MONITOR_TRUNK_FAILURES_FIELD",
  JQMIGRATE = "JQMIGRATE",
  FPBXPERFLOGGING = "FPBXPERFLOGGING",
  DEVELRELOAD = "DEVELRELOAD",
  MIXMON_POST = "MIXMON_POST",
  POST_RELOAD_DEBUG = "POST_RELOAD_DEBUG",
  POST_RELOAD = "POST_RELOAD",
  PRE_RELOAD = "PRE_RELOAD",
  DIE_FREEPBX_VERBOSE = "DIE_FREEPBX_VERBOSE",
  USE_PACKAGED_JS = "USE_PACKAGED_JS",
  DEVICE_STRONG_SECRETS = "DEVICE_STRONG_SECRETS",
  DEVICE_REMOVE_MAILBOX = "DEVICE_REMOVE_MAILBOX",
  DEVICE_SIP_CANREINVITE = "DEVICE_SIP_CANREINVITE",
  DEVICE_SIP_DTMF = "DEVICE_SIP_DTMF",
  DEVICE_SIP_TRUSTRPID = "DEVICE_SIP_TRUSTRPID",
  DEVICE_SIP_SENDRPID = "DEVICE_SIP_SENDRPID",
  DEVICE_SIP_NAT = "DEVICE_SIP_NAT",
  DEVICE_SIP_ENCRYPTION = "DEVICE_SIP_ENCRYPTION",
  DEVICE_SIP_QUALIFYFREQ = "DEVICE_SIP_QUALIFYFREQ",
  DEVICE_QUALIFY = "DEVICE_QUALIFY",
  DEVICE_ALLOW = "DEVICE_ALLOW",
  DEVICE_DISALLOW = "DEVICE_DISALLOW",
  DEVICE_CALLGROUP = "DEVICE_CALLGROUP",
  DEVICE_PICKUPGROUP = "DEVICE_PICKUPGROUP",
  DIAL_OPTIONS = "DIAL_OPTIONS",
  TRUNK_OPTIONS = "TRUNK_OPTIONS",
  ATTTRANSALERTINFO = "ATTTRANSALERTINFO",
  BLINDTRANSALERTINFO = "BLINDTRANSALERTINFO",
  BLOCK_OUTBOUND_TRUNK_CNAM = "BLOCK_OUTBOUND_TRUNK_CNAM",
  CFRINGTIMERDEFAULT = "CFRINGTIMERDEFAULT",
  REC_POLICY = "REC_POLICY",
  CLEARGLOBALVARS = "CLEARGLOBALVARS",
  ASTCONFAPP = "ASTCONFAPP",
  ZAP2DAHDICOMPAT = "ZAP2DAHDICOMPAT",
  TONEZONE = "TONEZONE",
  ENABLECW = "ENABLECW",
  DISABLECUSTOMCONTEXTS = "DISABLECUSTOMCONTEXTS",
  INBOUND_NOTRANS = "INBOUND_NOTRANS",
  OUTBOUND_CID_UPDATE = "OUTBOUND_CID_UPDATE",
  OUTBOUND_DIAL_UPDATE = "OUTBOUND_DIAL_UPDATE",
  CONNECTEDLINE_PRESENCESTATE = "CONNECTEDLINE_PRESENCESTATE",
  DITECH_VQA_INBOUND = "DITECH_VQA_INBOUND",
  DITECH_VQA_OUTBOUND = "DITECH_VQA_OUTBOUND",
  DYNAMICHINTS = "DYNAMICHINTS",
  RFC7462 = "RFC7462",
  CONCURRENCYLIMITDEFAULT = "CONCURRENCYLIMITDEFAULT",
  FCBEEPONLY = "FCBEEPONLY",
  FORCE_INTERNAL_AUTO_ANSWER_ALL = "FORCE_INTERNAL_AUTO_ANSWER_ALL",
  DIVERSIONHEADER = "DIVERSIONHEADER",
  INTERNALALERTINFO = "INTERNALALERTINFO",
  DEFAULT_INTERNAL_AUTO_ANSWER = "DEFAULT_INTERNAL_AUTO_ANSWER",
  LAUNCH_AGI_AS_FASTAGI = "LAUNCH_AGI_AS_FASTAGI",
  NOOPTRACE = "NOOPTRACE",
  CWINUSEBUSY = "CWINUSEBUSY",
  CID_PREPEND_REPLACE = "CID_PREPEND_REPLACE",
  ASTSTOPPOLLINT = "ASTSTOPPOLLINT",
  RINGTIMER = "RINGTIMER",
  ASTSIPDRIVER = "ASTSIPDRIVER",
  TIMEFORMAT = "TIMEFORMAT",
  TRUNK_RING_TIMER = "TRUNK_RING_TIMER",
  AMPBADNUMBER = "AMPBADNUMBER",
  DIALPARTIESDIALPLAN = "DIALPARTIESDIALPLAN",
  USEGOOGLEDNSFORENUM = "USEGOOGLEDNSFORENUM",
  ASTSTOPTIMEOUT = "ASTSTOPTIMEOUT",
  ASTAGIDIR = "ASTAGIDIR",
  ASTVARLIBDIR = "ASTVARLIBDIR",
  ASTETCDIR = "ASTETCDIR",
  ASTLOGDIR = "ASTLOGDIR",
  ASTMODDIR = "ASTMODDIR",
  ASTRUNDIR = "ASTRUNDIR",
  ASTSPOOLDIR = "ASTSPOOLDIR",
  AMPPLAYBACK = "AMPPLAYBACK",
  CERTKEYLOC = "CERTKEYLOC",
  AMPCGIBIN = "AMPCGIBIN",
  AMPBIN = "AMPBIN",
  AMPSBIN = "AMPSBIN",
  AMPWEBROOT = "AMPWEBROOT",
  MOHDIR = "MOHDIR",
  MIXMON_DIR = "MIXMON_DIR",
  FOPWEBROOT = "FOPWEBROOT",
  FOLLOWME_AUTO_CREATE = "FOLLOWME_AUTO_CREATE",
  FOLLOWME_DISABLED = "FOLLOWME_DISABLED",
  FOLLOWME_TIME = "FOLLOWME_TIME",
  FOLLOWME_PRERING = "FOLLOWME_PRERING",
  FOLLOWME_RG_STRATEGY = "FOLLOWME_RG_STRATEGY",
  BADDESTABORT = "BADDESTABORT",
  XTNCONFLICTABORT = "XTNCONFLICTABORT",
  CHECKREFERER = "CHECKREFERER",
  ENABLEOLDDIALPATTERNS = "ENABLEOLDDIALPATTERNS",
  SERVERINTITLE = "SERVERINTITLE",
  CUSTOMASERROR = "CUSTOMASERROR",
  RELOADCONFIRM = "RELOADCONFIRM",
  MODULEADMINEDGE = "MODULEADMINEDGE",
  SHOWLANGUAGE = "SHOWLANGUAGE",
  SIPSECRETSIZE = "SIPSECRETSIZE",
  USE_FREEPBX_MENU_CONF = "USE_FREEPBX_MENU_CONF",
  MODULEADMINWGET = "MODULEADMINWGET",
  CACHE_CLEANUP_DAYS = "CACHE_CLEANUP_DAYS",
  SESSION_TIMEOUT = "SESSION_TIMEOUT",
  BLACKLIST_DISABLE_GRID_COUNT = "BLACKLIST_DISABLE_GRID_COUNT",
  ALLOW_MODULE_HOOK_IN = "ALLOW_MODULE_HOOK_IN",
  PAGING_D_P_AlertInfo = "PAGING_D_P_AlertInfo",
  PM2DISABLELOG = "PM2DISABLELOG",
  PM2PROXY = "PM2PROXY",
  PM2SHELL = "PM2SHELL",
  PM2USECACHE = "PM2USECACHE",
  PM2USEPROXY = "PM2USEPROXY",
  PROXY_ENABLED = "PROXY_ENABLED",
  PROXY_ADDRESS = "PROXY_ADDRESS",
  PROXY_USERNAME = "PROXY_USERNAME",
  PROXY_PASSWORD = "PROXY_PASSWORD",
  QUEUES_PESISTENTMEMBERS = "QUEUES_PESISTENTMEMBERS",
  QUEUES_SHARED_LASTCALL = "QUEUES_SHARED_LASTCALL",
  QUEUES_UPDATECDR = "QUEUES_UPDATECDR",
  QUEUES_MIX_MONITOR = "QUEUES_MIX_MONITOR",
  QUEUES_EVENTS_WHEN_CALLED_DEFAULT = "QUEUES_EVENTS_WHEN_CALLED_DEFAULT",
  QUEUES_EVENTS_MEMEBER_STATUS_DEFAULT = "QUEUES_EVENTS_MEMEBER_STATUS_DEFAULT",
  CDRDBHOST = "CDRDBHOST",
  CDRDBNAME = "CDRDBNAME",
  CDRDBPASS = "CDRDBPASS",
  CDRDBPORT = "CDRDBPORT",
  CDRDBTABLENAME = "CDRDBTABLENAME",
  CDRDBTYPE = "CDRDBTYPE",
  CDRDBUSER = "CDRDBUSER",
  CDRUSEGMT = "CDRUSEGMT",
  EXTENSION_LIST_RINGGROUPS = "EXTENSION_LIST_RINGGROUPS",
  BRAND_IMAGE_TANGO_LEFT = "BRAND_IMAGE_TANGO_LEFT",
  BRAND_IMAGE_FREEPBX_FOOT = "BRAND_IMAGE_FREEPBX_FOOT",
  BRAND_IMAGE_SPONSOR_FOOT = "BRAND_IMAGE_SPONSOR_FOOT",
  BRAND_FREEPBX_ALT_LEFT = "BRAND_FREEPBX_ALT_LEFT",
  BRAND_FREEPBX_ALT_FOOT = "BRAND_FREEPBX_ALT_FOOT",
  BRAND_SPONSOR_ALT_FOOT = "BRAND_SPONSOR_ALT_FOOT",
  BRAND_IMAGE_FREEPBX_LINK_LEFT = "BRAND_IMAGE_FREEPBX_LINK_LEFT",
  BRAND_IMAGE_FREEPBX_LINK_FOOT = "BRAND_IMAGE_FREEPBX_LINK_FOOT",
  BRAND_IMAGE_SPONSOR_LINK_FOOT = "BRAND_IMAGE_SPONSOR_LINK_FOOT",
  BRAND_CSS_ALT_MAINSTYLE = "BRAND_CSS_ALT_MAINSTYLE",
  BRAND_CSS_ALT_POPOVER = "BRAND_CSS_ALT_POPOVER",
  BRAND_CSS_CUSTOM = "BRAND_CSS_CUSTOM",
  WHICH_asterisk = "WHICH_asterisk",
  WHICH_ffmpeg = "WHICH_ffmpeg",
  WHICH_gs = "WHICH_gs",
  WHICH_lame = "WHICH_lame",
  WHICH_mailq = "WHICH_mailq",
  WHICH_mpg123 = "WHICH_mpg123",
  WHICH_mysqldump = "WHICH_mysqldump",
  WHICH_openssl = "WHICH_openssl",
  WHICH_route = "WHICH_route",
  WHICH_rsync = "WHICH_rsync",
  WHICH_sox = "WHICH_sox",
  WHICH_tail = "WHICH_tail",
  WHICH_tiff2pdf = "WHICH_tiff2pdf",
  WHICH_tiffinfo = "WHICH_tiffinfo",
  AMPSYSLOGLEVEL = "AMPSYSLOGLEVEL",
  AMPDISABLELOG = "AMPDISABLELOG",
  LOG_OUT_MESSAGES = "LOG_OUT_MESSAGES",
  AUTOMODULEUPDATESANDRELOAD = "AUTOMODULEUPDATESANDRELOAD",
  AMPSHOWUPDATESONSSH = "AMPSHOWUPDATESONSSH",
  LOG_NOTIFICATIONS = "LOG_NOTIFICATIONS",
  MDATETIMEFORMAT = "MDATETIMEFORMAT",
  MDATEFORMAT = "MDATEFORMAT",
  FPBX_LOG_FILE = "FPBX_LOG_FILE",
  MTIMEFORMAT = "MTIMEFORMAT",
  PHP_CONSOLE = "PHP_CONSOLE",
  PHP_CONSOLE_PASSWORD = "PHP_CONSOLE_PASSWORD",
  PHP_ERROR_HANDLER_OUTPUT = "PHP_ERROR_HANDLER_OUTPUT",
  PHP_ERROR_LEVEL = "PHP_ERROR_LEVEL",
  AGGRESSIVE_DUPLICATE_CHECK = "AGGRESSIVE_DUPLICATE_CHECK",
  AMPEXTENSIONS = "AMPEXTENSIONS",
  EXPOSE_ALL_FEATURE_CODES = "EXPOSE_ALL_FEATURE_CODES",
  AUTHTYPE = "AUTHTYPE",
  AMP_ACCESS_DB_CREDS = "AMP_ACCESS_DB_CREDS",
  FORCED_ASTVERSION = "FORCED_ASTVERSION",
  AMPVMUMASK = "AMPVMUMASK",
  BROWSER_STATS = "BROWSER_STATS",
  CACHERTCLASSES = "CACHERTCLASSES",
  MIXMON_FORMAT = "MIXMON_FORMAT",
  UIDEFAULTLANG = "UIDEFAULTLANG",
  SIGNATURECHECK = "SIGNATURECHECK",
  AMPTRACKENABLE = "AMPTRACKENABLE",
  AMPWEBADDRESS = "AMPWEBADDRESS",
  PHPTIMEZONE = "PHPTIMEZONE",
  RSSFEEDS = "RSSFEEDS",
  AMPASTERISKGROUP = "AMPASTERISKGROUP",
  AMPASTERISKUSER = "AMPASTERISKUSER",
  AMPDEVGROUP = "AMPDEVGROUP",
  AMPDEVUSER = "AMPDEVUSER",
  FREEPBX_SYSTEM_IDENT = "FREEPBX_SYSTEM_IDENT",
  AMPASTERISKWEBGROUP = "AMPASTERISKWEBGROUP",
  AMPASTERISKWEBUSER = "AMPASTERISKWEBUSER",
  AMPENGINE = "AMPENGINE",
  USE_GOOGLE_CDN_JS = "USE_GOOGLE_CDN_JS",
  TCMAINT = "TCMAINT",
  TCINTERVAL = "TCINTERVAL",
  NODEJSENABLED = "NODEJSENABLED",
  NODEJSTLSENABLED = "NODEJSTLSENABLED",
  NODEJSBINDADDRESS = "NODEJSBINDADDRESS",
  NODEJSBINDPORT = "NODEJSBINDPORT",
  NODEJSHTTPSBINDADDRESS = "NODEJSHTTPSBINDADDRESS",
  NODEJSHTTPSBINDPORT = "NODEJSHTTPSBINDPORT",
  NODEJSTLSCERTFILE = "NODEJSTLSCERTFILE",
  NODEJSTLSPRIVATEKEY = "NODEJSTLSPRIVATEKEY",
  UCPCHANGEPASSWORD = "UCPCHANGEPASSWORD",
  UCPCHANGEUSERNAME = "UCPCHANGEUSERNAME",
  UCPRSSFEEDS = "UCPRSSFEEDS",
  UCPSESSIONTIMEOUT = "UCPSESSIONTIMEOUT",
  AMPUSERMANEMAILFROM = "AMPUSERMANEMAILFROM",
  USERMAN_ACCOUNT_CODE = "USERMAN_ACCOUNT_CODE",
  USERMAN_ENABLE_CALL_ACTIVITY_GROUPS = "USERMAN_ENABLE_CALL_ACTIVITY_GROUPS",
  USERMAN_CALL_ACTIVITY_GRP_USER_LIMIT = "USERMAN_CALL_ACTIVITY_GRP_USER_LIMIT",
  VMX_CONTEXT = "VMX_CONTEXT",
  VMX_LOOPDEST_CONTEXT = "VMX_LOOPDEST_CONTEXT",
  VMX_LOOPDEST_EXT = "VMX_LOOPDEST_EXT",
  VMX_LOOPDEST_PRI = "VMX_LOOPDEST_PRI",
  VMX_PRI = "VMX_PRI",
  VMX_TIMEDEST_CONTEXT = "VMX_TIMEDEST_CONTEXT",
  VMX_TIMEDEST_EXT = "VMX_TIMEDEST_EXT",
  VMX_TIMEDEST_PRI = "VMX_TIMEDEST_PRI",
  USERESMWIBLF = "USERESMWIBLF",
  VM_SHOW_IMAP = "VM_SHOW_IMAP",
  UCP_MESSAGE_LIMIT = "UCP_MESSAGE_LIMIT",
}

// updateSettingsPayload
export interface updateSettingsPayload {
  status: boolean;
  message: string;
  clientMutationId: string;
}

// addFTPInstanceInput
export interface addFTPInstanceInputInput {
  enabled: boolean;
  serverName: string;
  hostName: string;
  description: string;
  port: string;
  userName: string;
  password: string;
  fileStoreType: string;
  path: string;
  transferMode: string;
  timeout: string;
  clientMutationId: string;
}

// addFTPInstancePayload
export interface addFTPInstancePayload {
  status: boolean;
  message: string;
  id: string;
  clientMutationId: string;
}

// addS3BucketInput
export interface addS3BucketInputInput {
  name: string;
  bucketName: string;
  description: string;
  AWSRegion: string;
  AWSAccessKey: string;
  AWSSecret: string;
  path: string;
  clientMutationId: string;
}

// addS3BucketPayload
export interface addS3BucketPayload {
  status: boolean;
  message: string;
  id: string;
  clientMutationId: string;
}

// updateFTPInstanceInput
export interface updateFTPInstanceInputInput {
  id: string;
  enabled: boolean;
  serverName: string;
  hostName: string;
  description: string;
  port: string;
  userName: string;
  password: string;
  fileStoreType: string;
  path: string;
  transferMode: string;
  timeout: string;
  clientMutationId: string;
}

// updateFTPInstancePayload
export interface updateFTPInstancePayload {
  status: boolean;
  message: string;
  id: string;
  clientMutationId: string;
}

// deleteFTPInstanceInput
export interface deleteFTPInstanceInputInput {
  id: string;
  clientMutationId: string;
}

// deleteFTPInstancePayload
export interface deleteFTPInstancePayload {
  status: boolean;
  message: string;
  id: string;
  clientMutationId: string;
}

// updateFollowMeInput
export interface updateFollowMeInputInput {
  extensionId: string;
  enabled: boolean;
  strategy: ringstrategiesv2;
  ringTime: number;
  followMePrefix: string;
  followMeList: string;
  callerMessage: string;
  noAnswerDestination: string;
  alertInfo: string;
  confirmCalls: boolean;
  receiverMessageConfirmCall: string;
  receiverMessageTooLate: string;
  ringingMusic: string;
  initialRingTime: number;
  enableCalendar: boolean;
  matchCalendar: boolean;
  calendar: string;
  calendarGroup: string;
  overrideRingerVolume: number;
  externalCallerIdMode: externalcidmode;
  fixedCallerId: string;
  clientMutationId: string;
}

// updateFollowMePayload
export interface updateFollowMePayload {
  status: boolean;
  message: string;
  clientMutationId: string;
}

// enableFollowMeInput
export interface enableFollowMeInputInput {
  extensionId: string;
  clientMutationId: string;
}

// enableFollowMePayload
export interface enableFollowMePayload {
  status: boolean;
  message: string;
  clientMutationId: string;
}

// disableFollowMeInput
export interface disableFollowMeInputInput {
  extensionId: string;
  clientMutationId: string;
}

// disableFollowMePayload
export interface disableFollowMePayload {
  status: boolean;
  message: string;
  clientMutationId: string;
}

// addRecordingInput
export interface addRecordingInputInput {
  id: string;
  name: string;
  description: string;
  fcode: string;
  fcode_pass: string;
  language: string;
  playback: string[];
  file: string;
  codec: string;
  codecs: string[];
  lang: string;
  temporary: string;
  clientMutationId: string;
}

// addRecordingPayload
export interface addRecordingPayload {
  status: boolean;
  message: string;
  id: string;
  clientMutationId: string;
}

// updateRecordingInput
export interface updateRecordingInputInput {
  id: string;
  name: string;
  description: string;
  fcode: string;
  fcode_pass: string;
  language: string;
  playback: string[];
  file: string;
  codec: string;
  codecs: string[];
  lang: string;
  temporary: string;
  clientMutationId: string;
}

// updateRecordingPayload
export interface updateRecordingPayload {
  status: boolean;
  message: string;
  id: string;
  clientMutationId: string;
}

// deleteRecordingInput
export interface deleteRecordingInputInput {
  id: string;
  clientMutationId: string;
}

// deleteRecordingPayload
export interface deleteRecordingPayload {
  status: boolean;
  message: string;
  id: string;
  clientMutationId: string;
}

// convertfileInput
export interface convertfileInputInput {
  id: string;
  name: string;
  description: string;
  fcode: string;
  fcode_pass: string;
  language: string;
  playback: string[];
  file: string;
  codec: string;
  codecs: string[];
  lang: string;
  temporary: string;
  clientMutationId: string;
}

// convertfilePayload
export interface convertfilePayload {
  status: boolean;
  message: string;
  id: string;
  clientMutationId: string;
}

// addRingGroupInput
export interface addRingGroupInputInput {
  groupNumber: string;
  description: string;
  strategy: string;
  extensionList: string;
  ringTime: string;
  groupPrefix: string;
  callerMessage: string;
  postAnswer: string;
  alertInfo: string;
  needConf: boolean;
  recevierMessageConfirmCall: string;
  recevierMessage: string;
  receiverMessageConfirmCall: string;
  receiverMessage: string;
  ringingMusic: string;
  ignoreCallForward: boolean;
  ignoreCallWait: boolean;
  pickupCall: boolean;
  callRecording: string;
  callProgress: boolean;
  answeredElseWhere: boolean;
  overrideRingerVolume: string;
  changecid: string;
  fixedcid: string;
  clientMutationId: string;
}

// addRingGroupPayload
export interface addRingGroupPayload {
  status: boolean;
  message: string;
  response: ringgroup;
  clientMutationId: string;
}

// updateRingGroupInput
export interface updateRingGroupInputInput {
  groupNumber: string;
  description: string;
  strategy: string;
  extensionList: string;
  ringTime: string;
  groupPrefix: string;
  callerMessage: string;
  postAnswer: string;
  alertInfo: string;
  needConf: boolean;
  recevierMessageConfirmCall: string;
  recevierMessage: string;
  receiverMessageConfirmCall: string;
  receiverMessage: string;
  ringingMusic: string;
  ignoreCallForward: boolean;
  ignoreCallWait: boolean;
  pickupCall: boolean;
  callRecording: string;
  callProgress: boolean;
  answeredElseWhere: boolean;
  overrideRingerVolume: string;
  changecid: string;
  fixedcid: string;
  clientMutationId: string;
}

// updateRingGroupPayload
export interface updateRingGroupPayload {
  status: boolean;
  message: string;
  response: ringgroup;
  clientMutationId: string;
}

// DeleteRingGroupInput
export interface DeleteRingGroupInputInput {
  groupNumber: number;
  clientMutationId: string;
}

// DeleteRingGroupPayload
export interface DeleteRingGroupPayload {
  status: boolean;
  message: string;
  response: ringgroup;
  clientMutationId: string;
}

// addSipNatLocalIpInput
export interface addSipNatLocalIpInputInput {
  net: string;
  mask: string;
  clientMutationId: string;
}

// addSipNatLocalIpPayload
export interface addSipNatLocalIpPayload {
  status: boolean;
  message: string;
  clientMutationId: string;
}

// updateSipNatExternalIpInput
export interface updateSipNatExternalIpInputInput {
  net: string;
  clientMutationId: string;
}

// updateSipNatExternalIpPayload
export interface updateSipNatExternalIpPayload {
  status: boolean;
  message: string;
  clientMutationId: string;
}

// updateWSSettingsInput
export interface updateWSSettingsInputInput {
  ws: string;
  wss: string;
  clientMutationId: string;
}

// updateWSSettingsPayload
export interface updateWSSettingsPayload {
  status: boolean;
  message: string;
  clientMutationId: string;
}

// enableVoiceMailInput
export interface enableVoiceMailInputInput {
  extensionId: string;
  password: string;
  name: string;
  email: string;
  pager: string;
  saycid: boolean;
  envelope: boolean;
  attach: boolean;
  delete: boolean;
  clientMutationId: string;
}

// enableVoiceMailPayload
export interface enableVoiceMailPayload {
  status: boolean;
  message: string;
  clientMutationId: string;
}

// disableVoiceMailInput
export interface disableVoiceMailInputInput {
  extensionId: string;
  clientMutationId: string;
}

// disableVoiceMailPayload
export interface disableVoiceMailPayload {
  status: boolean;
  message: string;
  clientMutationId: string;
}

export interface GraphQLClient {
  query<T = any>(query: string, variables?: Record<string, any>): Promise<{ data?: T; errors?: any[] }>;
}

export interface FreePBXGraphQLSchema {
  Query: Query;
  Mutation: Mutation;
  Subscription: any;
}
