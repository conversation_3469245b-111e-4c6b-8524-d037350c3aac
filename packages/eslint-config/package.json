{"name": "@repo/eslint-config", "version": "0.1.0", "private": true, "main": "library.js", "files": ["library.js", "next.js", "react-internal.js"], "scripts": {"lint": "echo 'Skipping lint for @repo/eslint-config'", "typecheck": "bunx tsc --noEmit -p ../../tsconfig.json"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.33.1", "@typescript-eslint/parser": "^8.33.1", "eslint": "^9.28.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-solid": "^0.14.5", "typescript": "^5.8.3"}, "dependencies": {"eslint-config-turbo": "^2.5.4", "eslint-plugin-only-warn": "^1.1.0"}}