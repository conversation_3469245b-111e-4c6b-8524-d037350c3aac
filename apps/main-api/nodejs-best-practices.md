# NestJS: Best Practices & Robust Backend Architecture Guide

---

## 1. Single Responsibility Principle (SRP)

**Principle:**
Every class or function should have one responsibility only. Keep controllers, services, and data access layers focused on one concern.

**Bad Example (Doing Too Much):**

```typescript
// controllers/user.controller.ts (Bad)
// ... imports ...

@Controller('users')
export class UserController {
  @Post()
  async create(@Body() dto: CreateUserDto): Promise<User> {
    // Validate, create user, log event, and send email in the same method
    // ...
    // Log event
    console.log(`User ${user.username} created`);
    // Send email (business logic should not be here)
    await this.sendWelcomeEmail(user);
    return user;
  }

  // Mixed concerns!
  private async sendWelcomeEmail(user: User) {
    // ...
  }
}
```

**Good Example (One Responsibility per Class/Method):**

```typescript
// controllers/user.controller.ts (Good)
// ... imports ...

@Controller('users')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Post()
  async create(@Body() dto: CreateUserDto): Promise<User> {
    return this.userService.createUser(dto);
  }
}
```

And in the service:

```typescript
// services/user.service.ts
// ... imports ...

@Injectable()
export class UserService {
  constructor(private readonly userRepository: UserRepository) {} // Assuming a UserRepository

  async createUser(dto: CreateUserDto): Promise<User> {
    // Validate & sanitize already handled by DTO & pipes
    const user = await this.userRepository.create(dto); // Delegate DB logic
    this.logUserCreation(user);
    // Potentially delegate email sending to another service
    return user;
  }

  private logUserCreation(user: User): void {
    // Log only responsibility
    console.log(`User ${user.username} created.`);
  }
}

```

## 2. Methods Should Do Just One Thing

Break complex logic into small, well-named helper methods to improve readability and testability.

**Bad Example:**

```typescript
// models/user.entity.ts (Bad)
export class User {
  getDisplayName(): string {
    if (this.isClient() && this.isVerified()) {
      return `Mr. ${this.firstName} ${this.middleName} ${this.lastName}`;
    } else {
      return `${this.firstName[0]}. ${this.lastName}`;
    }
  }
}
```

**Good Example:**

```typescript
// models/user.entity.ts (Good)
export class User {
  getDisplayName(): string {
    return this.isVerifiedClient() ? this.getLongName() : this.getShortName();
  }

  private isVerifiedClient(): boolean {
    // Assume a method to check current request user context or property flags
    return this.role === 'client' && this.verified;
  }

  private getLongName(): string {
    return `Mr. ${this.firstName} ${this.middleName} ${this.lastName}`;
  }

  private getShortName(): string {
    return `${this.firstName[0]}. ${this.lastName}`;
  }
}
```

---

## 3. Thin Controllers & Business Logic in Services

**Principle:**  
Controllers should be lean—handle HTTP requests/responses only. All business logic belongs in service classes.

**Bad Example (Controller with Business Logic):**

```typescript
// controllers/article.controller.ts (Bad)
import { Controller, Post, UploadedFile, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';

@Controller('articles')
export class ArticleController {
  @Post()
  @UseInterceptors(FileInterceptor('image'))
  async create(@UploadedFile() image: Express.Multer.File) {
    if (image) {
      // Business logic for handling file upload inside controller
      // Not ideal!
      await image.mv('/path/to/temp');
    }
    // ... other logic
  }
}
```

**Good Example:**

```typescript
// controllers/article.controller.ts (Good)
import { Controller, Post, UploadedFile, UseInterceptors } from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ArticleService } from './article.service';

@Controller('articles')
export class ArticleController {
  constructor(private readonly articleService: ArticleService) {}

  @Post()
  @UseInterceptors(FileInterceptor('image'))
  async create(@UploadedFile() image: Express.Multer.File) {
    await this.articleService.handleUploadedImage(image);
    // ... other HTTP concerns
  }
}
```

And in the service:

```typescript
// services/article.service.ts
import { Injectable } from '@nestjs/common';

@Injectable()
export class ArticleService {
  async handleUploadedImage(image: Express.Multer.File): Promise<void> {
    if (image) {
      // Perform file system operation or delegate to a dedicated file service
      await image.mv('/path/to/temp');
    }
  }
}
```

---

## 4. Use DTOs, Validation, & Data Sanitization

**Principle:**  
Define explicit DTOs for input and output. Use Pipes with `class-validator` and `class-transformer` to enforce validation and sanitization.

**DTO Example:**

```typescript
// dtos/create-user.dto.ts
import { IsEmail, IsNotEmpty, MinLength } from 'class-validator';
import { Transform } from 'class-transformer';

export class CreateUserDto {
  @IsNotEmpty()
  @Transform(({ value }) => value.trim())
  username: string;

  @IsEmail()
  @Transform(({ value }) => value.toLowerCase().trim())
  email: string;

  @MinLength(6)
  password: string;
}
```

**Global Validation Pipe Setup (main.ts):**

```typescript
// main.ts
import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe } from '@nestjs/common';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,        // Strip properties that have no decorators
      forbidNonWhitelisted: true,
      transform: true,        // Automatically convert payloads to DTO instances
    }),
  );
  await app.listen(3000);
}
bootstrap();
```

---

## 5. Avoid Repeating Yourself (DRY)

**Principle:**
Abstract shared logic into helper functions, services, or custom pipes to avoid duplication.

**Bad Example:**

```typescript
// services/sample.service.ts (Bad)
import { Injectable } from '@nestjs/common';
import { DrizzleService } from '../drizzle/drizzle.service';
import { User } from '../entities/user.entity';
import { eq, and, isNull } from 'drizzle-orm';

@Injectable()
export class SampleService {
  constructor(private readonly drizzle: DrizzleService) {}

  async getActiveUsers() {
    return this.drizzle.db.query.users.findMany({
      where: and(eq(User.active, true), isNull(User.deletedAt)),
    });
  }

  async getVerifiedUsers() {
    return this.drizzle.db.query.users.findMany({
      where: and(eq(User.verified, true), isNull(User.deletedAt)),
    });
  }
}
```

**Good Example (Using Reusable Query Functions or a Data Access Layer):**

```typescript
// services/user.service.ts (Good)
import { Injectable } from '@nestjs/common';
import { UserRepository } from '../repositories/user.repository'; // Assuming a UserRepository

@Injectable()
export class UserService {
  constructor(private readonly userRepository: UserRepository) {}

  async getActiveUsers() {
    return this.userRepository.findActive();
  }

  async getVerifiedUsers() {
    return this.userRepository.findVerified();
  }
}

// repositories/user.repository.ts
import { Injectable } from '@nestjs/common';
import { DrizzleService } from '../drizzle/drizzle.service';
import { UserTable, User } from '../entities/user.schema'; // Assume a schema file
import { eq, and, isNull } from 'drizzle-orm';

@Injectable()
export class UserRepository {
  constructor(private readonly drizzle: DrizzleService) {}

  findActive() {
    return this.drizzle.db.query.UserTable.findMany({
      where: and(eq(UserTable.active, true), isNull(UserTable.deletedAt)),
    });
  }

  findVerified() {
    return this.drizzle.db.query.UserTable.findMany({
      where: and(eq(UserTable.verified, true), isNull(UserTable.deletedAt)),
    });
  }

  // ... other generic user data access methods
}
```

---

## 6. Leverage DrizzleORM Features Over Raw SQL

**Principle:**
Use DrizzleORM’s expressive methods for querying relation management instead of raw SQL queries wherever possible.

**Bad Example (Raw SQL):**

```typescript
// Using raw SQL queries (Bad)
await drizzle.db.execute(`
  SELECT * FROM users
  WHERE id IN (
    SELECT user_id FROM posts WHERE published = true
  )
`);
```

**Good Example (Using DrizzleORM with Relations):**

```typescript
// Assuming relations are defined in your Drizzle schema
const usersWithPublishedPosts = await this.drizzle.db.query.UserTable.findMany({
  with: {
    posts: {
      where: (posts, { eq }) => eq(posts.published, true),
    },
  },
});
```

---

Below is a dedicated section on how to avoid and protect against SQL injection in your NestJS application when using DrizzleORM. The focus is on leveraging safe practices such as parameterized queries, and the Drizzle query builder rather than concatenating strings. These techniques ensure that your application never directly interpolates user input into SQL statements.

---

## 7 Protecting Against SQL Injection

### a. Use Parameterized Queries & Drizzle Query Builder

When you need to construct queries with user input, always use Drizzle’s built-in methods or the query builder. This ensures that parameter values are properly escaped.

**Bad Example (Vulnerable to SQL Injection):**

```typescript
// Do not do this!
const userInput = "some user input'; DROP TABLE users; --";
await drizzle.db.execute(`SELECT * FROM users WHERE username = '${userInput}'`);
```

**Good Example (Using the Drizzle Query Builder):**

```typescript
import { eq } from 'drizzle-orm';
import { UserTable } from '../entities/user.schema'; // Assume your schema file

const userInput = "some user input";
const user = await drizzle.db.query.UserTable.findFirst({
  where: eq(UserTable.username, userInput),
});
```

### b. Rely on Drizzle ORM Methods

DrizzleORM’s methods for querying (e.g., `findMany`, `findFirst`), inserts (`insert`), updates (`update`), and deletes (`delete`) automatically handle parameter binding, making them safe against typical SQL injection attacks.

**Example:**

```typescript
// Instead of writing raw SQL, use Drizzle methods:
import { eq } from 'drizzle-orm';
import { UserTable } from '../entities/user.schema';

const user = await drizzle.db.query.UserTable.findFirst({
  where: eq(UserTable.username, userInput),
});
```

### c. Avoid Dynamic SQL Construction with String Concatenation

If you must build parts of a query dynamically (e.g., sorting or filtering based on user input), validate and sanitize inputs rigorously. The preferred approach is to use Drizzle’s dynamic query building capabilities or restrict allowed values.

**Bad Example:**

```typescript
// Dynamic SQL creation using user input directly (bad)
const sortBy = req.query.sortBy; // e.g., "username desc"
const query = `SELECT * FROM users ORDER BY ${sortBy}`;
await drizzle.db.execute(query);
```

**Good Example:**

```typescript
import { sql } from 'drizzle-orm';
import { UserTable } from '../entities/user.schema'; // Assume your schema file

// Restrict dynamic parts to a whitelist
const validSortFields = ['username', 'createdAt'];
const sortBy = validSortFields.includes(req.query.sortBy as string) ? req.query.sortBy : 'username';
const sortOrder = req.query.sortOrder === 'desc' ? sql`desc` : sql`asc`;

const users = await drizzle.db.query.UserTable.findMany({
  orderBy: sql`${UserTable[sortBy]} ${sortOrder}` as any, // Use sql tagged template literal for dynamic parts
});
```

Note: Using `sql` tagged template literals for dynamic parts like column names or order can be safe when combined with input validation against a whitelist.

### d. Use Drizzle-Provided Methods for Data Manipulation

When working with inserts, updates, or deletes, always use methods provided by DrizzleORM (e.g., `insert`, `update`, `delete`). These methods safely handle the underlying SQL.

**Example:**

```typescript
import { UserTable } from '../entities/user.schema'; // Assume your schema file

// Creating a new user safely using Drizzle insert method
const [insertedUser] = await drizzle.db.insert(UserTable).values({
  username: dto.username,
  email: dto.email,
  password: dto.password, // Ensure password is hashed before inserting
}).returning();
```

---

## 8. Mass Assignment & Entity Creation

**Principle:**
When inserting or updating records, use Drizzle’s methods directly with validated DTO data.

**Bad Example:**

```typescript
// Manually mapping properties (Less ideal for Drizzle)
const newUser = {
  username: dto.username,
  email: dto.email,
  password: dto.password,
  // Missing protection against unexpected fields if not careful
};
await drizzle.db.insert(UserTable).values(newUser);
```

**Good Example:**

```typescript
import { UserTable } from '../entities/user.schema';

// Directly use the DTO (after validation via Pipes) with Drizzle's methods
const [insertedUser] = await drizzle.db.insert(UserTable).values({
  username: dto.username,
  email: dto.email,
  password: dto.password, // Ensure password is hashed before inserting
  // Only map the necessary fields
}).returning();
```

Or within a dedicated repository:

```typescript
// repositories/user.repository.ts
import { Injectable } from '@nestjs/common';
import { DrizzleService } from '../drizzle/drizzle.service';
import { UserTable, User } from '../entities/user.schema';
import { CreateUserDto } from '../dtos/create-user.dto'; // Assume CreateUserDto

@Injectable()
export class UserRepository {
  constructor(private readonly drizzle: DrizzleService) {}

  async create(dto: CreateUserDto): Promise<User> {
    // Assuming you hash the password here or before this
    const [newUser] = await this.drizzle.db.insert(UserTable).values({
      username: dto.username,
      email: dto.email,
      password: dto.password, // Hashed password
    }).returning(); // Assuming returning() is used to get the created entity
    return newUser;
  }

  // ... other data access methods
}

```

---

## 9. Eager Loading & Avoiding N+1 Queries

**Principle:**
Always load related data eagerly using Drizzle’s `with` clause when needed to avoid multiple queries.

**Bad Example (N+1 problem):**

```typescript
// In a service, iterating without eager loading (Bad)
const users = await this.drizzle.db.query.UserTable.findMany();
for (const user of users) {
  const profile = await this.drizzle.db.query.ProfileTable.findFirst({
    where: (profile, { eq }) => eq(profile.userId, user.id),
  }); // Triggers extra query per user if profile is not joined
  console.log(profile.name);
}
```

**Good Example:**

```typescript
import { UserTable } from '../entities/user.schema';

// Eager loading the relation using 'with' (Good)
const usersWithProfiles = await this.drizzle.db.query.UserTable.findMany({
  with: {
    profile: true, // Assuming the relation is named 'profile' in your schema
  },
});
for (const user of usersWithProfiles) {
  console.log(user.profile.name);
}
```

---

## 10. Chunking Data for Heavy Tasks

**Principle:**
For operations on large datasets, process records in chunks to avoid memory issues. Drizzle itself doesn’t have a direct `stream` or `chunk` like TypeORM, but you can implement manual pagination or use database-specific streaming features if available through the underlying driver.

**Example (Manual Pagination):**

```typescript
import { UserTable } from '../entities/user.schema';

const batchSize = 1000;
let offset = 0;
let usersBatch;

do {
  usersBatch = await this.drizzle.db.query.UserTable.findMany({
    limit: batchSize,
    offset: offset,
  });

  for (const user of usersBatch) {
    // Process each user record
    console.log(`Processing user: ${user.username}`);
    // Await processing if necessary
    await this.processUserRecord(user);
  }

  offset += batchSize;
} while (usersBatch.length === batchSize);

// Define a helper method to process individual records
async function processUserRecord(user: any): Promise<void> {
  // Your processing logic
  // await someAsyncOperation(user);
}

---

## 10. Chunking Data for Heavy Tasks

**Principle:**  
For operations on large datasets, process records in chunks to avoid memory issues.

**Bad Example:**

```typescript
// Loading all records at once (Bad)
const users = await this.userRepository.find();
users.forEach(user => {
  // Process user...
});
```

**Good Example:**

```typescript
// Using query builder with chunking (Good)
await this.userRepository
  .createQueryBuilder('user')
  .stream(async stream => {
    for await (const user of stream) {
      // Process each user record in a memory-efficient way
    }
  });
```

---

## 11. Use Dependency Injection (IoC) Instead of New Class

**Principle:**  
Always use NestJS’s DI container to inject dependencies. This makes testing and maintenance easier.

**Bad Example:**

```typescript
// Direct instantiation (Bad)
const service = new SomeService();
service.doWork();
```

**Good Example:**

```typescript
// Using constructor injection (Good)
import { Injectable } from '@nestjs/common';

@Injectable()
export class SomeConsumerService {
  constructor(private readonly someService: SomeService) {}

  doSomething() {
    this.someService.doWork();
  }
}
```

---

## 12. Avoid Direct Access to Environment Variables

**Principle:**  
Never use `process.env` directly in your application code. Instead, load these into configuration files and access them via the `ConfigService`.

**Bad Example:**

```typescript
const apiKey = process.env.API_KEY;
```

**Good Example:**

```typescript
// config/api.config.ts
export default () => ({
  apiKey: process.env.API_KEY,
});

// app.module.ts
import { ConfigModule } from '@nestjs/config';
import apiConfig from './config/api.config';

@Module({
  imports: [
    ConfigModule.forRoot({
      load: [apiConfig],
      isGlobal: true,
    }),
    // ...
  ],
})
export class AppModule {}

// Anywhere in your code:
import { ConfigService } from '@nestjs/config';

constructor(private configService: ConfigService) {
  const apiKey = this.configService.get<string>('apiKey');
}
```

---

## 13. Configuration & Constants Instead of Hard-Coded Text

**Principle:**  
Keep strings, dates, or keys in dedicated configuration or constants files instead of scattering them in your code.

**Bad Example:**

```typescript
if (user.role === 'admin') {
  // ...
}
```

**Good Example:**

```typescript
// entities/user.entity.ts
export enum UserRole {
  ADMIN = 'admin',
  USER = 'user',
}

// usage
if (user.role === UserRole.ADMIN) {
  // ...
}
```

---

## 14. Naming Conventions & File Organization

**Principle:**  
Follow NestJS (and broader TypeScript) naming conventions for files, classes, methods, and variables. For example:

- **Controllers:** Use singular names (e.g. `UserController`)
- **Services:** Use descriptive names (e.g. `AuthService`)
- **DTOs & Entities:** Use PascalCase for classes
- **Files & Folders:** Use kebab-case or camelCase consistently

**Good Example:**

```
src/
 ├── modules/
 │    ├── user/
 │    │    ├── controllers/user.controller.ts
 │    │    ├── dtos/create-user.dto.ts
 │    │    ├── entities/user.entity.ts
 │    │    ├── services/user.service.ts
 │    │    └── user.module.ts
```

---

## 15. Convention Over Configuration

**Principle:**  
Stick to NestJS defaults and conventions (like naming, module structure, and decorators) to minimize extra configuration and keep your codebase consistent.

**Example:**  
Define your module, controller, and service using built-in decorators without over-configuring unless necessary.

```typescript
// user.module.ts
import { Module } from '@nestjs/common';
import { UserController } from './controllers/user.controller';
import { UserService } from './services/user.service';

@Module({
  controllers: [UserController],
  providers: [UserService],
})
export class UserModule {}
```

---

## 16. Use Shorter, Readable Syntax Where Possible

**Principle:**  
Leverage TypeScript and NestJS shorthand to make your code cleaner. For example, use destructuring, concise arrow functions, and built-in helper functions.

**Examples:**

```typescript
// Instead of:
const username = request.body.username ? request.body.username.trim() : null;

// Use:
const { username = '' } = request.body;
const cleanedUsername = username.trim();
```

And in dependency injection, use parameter properties:

```typescript
constructor(private readonly userService: UserService) {}
```

---

## 17. Use Built-In NestJS Tools Over Third-Party Ones

**Principle:**  
Favor NestJS’s standard modules and widely adopted community packages rather than exotic third-party solutions. This ensures maintainability and easier onboarding for new developers.

**Example:**  

- Use `@nestjs/jwt` for JWT authentication.
- Use `@nestjs/config` for configuration management.
- Use `@nestjs/swagger` for API documentation.

---

## 18. Testing Best Practices

**Principle:**
Write unit and end-to-end tests for your modules. Use NestJS’s testing utilities to inject dependencies and create isolated test environments. Mock your DrizzleORM interactions in unit tests.

**Example (Unit Test for a Service with Mocked Drizzle):**

```typescript
// services/user.service.spec.ts
import { Test, TestingModule } from '@nestjs/testing';
import { UserService } from './user.service';
import { DrizzleService } from '../drizzle/drizzle.service';
import { UserTable } from '../entities/user.schema';
import { eq } from 'drizzle-orm';

const mockUsers = [{ id: '1', username: 'testuser', email: '<EMAIL>' }];

const mockDrizzleService = {
  db: {
    insert: jest.fn(() => ({
      values: jest.fn(() => ({
        returning: jest.fn().mockResolvedValue([{ id: 'new-id', username: 'newuser', email: '<EMAIL>' }]),
      })),
    })),
    query: {
        UserTable: {
            findFirst: jest.fn().mockResolvedValue(mockUsers[0]),
            findMany: jest.fn().mockResolvedValue(mockUsers),
        }
    }
  },
};

describe('UserService', () => {
  let service: UserService;
  let drizzleService: DrizzleService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UserService,
        {
          provide: DrizzleService,
          useValue: mockDrizzleService,
        },
      ],
    }).compile();

    service = module.get<UserService>(UserService);
    drizzleService = module.get<DrizzleService>(DrizzleService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should find a userbased on username', async () => {
    const username = 'testuser';
    const user = await service.findByUsername(username); // Assuming service has this method
    expect(drizzleService.db.query.UserTable.findFirst).toHaveBeenCalledWith({
        where: eq(UserTable.username, username),
    });
    expect(user).toEqual(mockUsers[0]);
  });

  // Additional tests mocking other Drizzle interactions...
});
```

---

## 19. Database Best Practices with DrizzleORM

### a. Define Your Schema Clearly & Use Explicit Types

**Principle:**
Define your database schema using DrizzleORM’s schema definition capabilities. Use appropriate data types and constraints.

**Example Schema File (`entities/user.schema.ts`):**

```typescript
import { pgTable, text, uuid, timestamp, boolean } from 'drizzle-orm/pg-core'; // or other database drivers
import { relations } from 'drizzle-orm';

export const UserTable = pgTable('users', {
  id: uuid('id').defaultRandom().primaryKey(), // Use uuid or other primary key
  username: text('username').unique().notNull(),
  email: text('email').unique().notNull(),
  password: text('password').notNull(), // Ensure passwords are hashed
  role: text('role').$type<'admin' | 'user'>().default('user').notNull(), // Use .$type for enum-like behavior
  active: boolean('active').default(true).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  deletedAt: timestamp('deleted_at'), // Soft delete field
});

// Example relations (if applicable)
export const usersRelations = relations(UserTable, ({ many }) => ({
  posts: many(PostTable), // Assuming a PostTable exists
}));

// Type inferred from schema
export type User = typeof UserTable.$inferSelect;
export type InsertUser = typeof UserTable.$inferInsert;

// Assuming a PostTable for the relation example
export const PostTable = pgTable('posts', {
  id: uuid('id').defaultRandom().primaryKey(),
  title: text('title').notNull(),
  content: text('content'),
  published: boolean('published').default(false).notNull(),
  userId: uuid('user_id').notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

export const postsRelations = relations(PostTable, ({ one }) => ({
    author: one(UserTable, {
        fields: [PostTable.userId],
        references: [UserTable.id],
    }),
}));

```

### b. Use Transactions for Multi-Step Operations

**Principle:**
When performing multiple database operations that must succeed or fail together, use Drizzle’s transaction capabilities.

**Example:**

```typescript
// services/user.service.ts
import { Injectable } from '@nestjs/common';
import { DrizzleService } from '../drizzle/drizzle.service';
import { UserTable, User } from '../entities/user.schema';
import { CreateUserDto } from '../dtos/create-user.dto';
import { hash } from 'bcrypt'; // Assuming bcrypt for password hashing

@Injectable()
export class UserService {
  constructor(private readonly drizzle: DrizzleService) {}

  async createUserWithProfile(dto: CreateUserDto, profileData: any): Promise<User> {
    // Use Drizzle's transaction function
    return this.drizzle.db.transaction(async (tx) => {
      // Hash the password before inserting
      const hashedPassword = await hash(dto.password, 10);

      // Insert the user
      const [newUser] = await tx.insert(UserTable).values({
        username: dto.username,
        email: dto.email,
        password: hashedPassword,
        // ... other user fields from DTO
      }).returning(); // Assuming returning() is supported and used

      if (!newUser) {
          throw new Error('Failed to create user'); // Or a more specific exception
      }

      // Insert associated profile data (assuming ProfileTable exists)
      // Example:
      // await tx.insert(ProfileTable).values({
      //     userId: newUser.id,
      //     ...profileData,
      // });

      // Perform additional operations within the transaction if necessary...

      return newUser; // The transaction commits if all operations succeed
    });
  }
}
```

### c. Manage Schema Changes with Drizzle Migrations

**Principle:**
Keep your database schema under version control using DrizzleORM migration tools.

**Example Migration File Structure:**

Drizzle migrations typically involve defining your schema in TypeScript and then using a CLI tool (`drizzle-kit`) to generate the migration SQL or code based on the differences.

1. **Define your schema:** (`entities/user.schema.ts` as shown above)
2. **Configure Drizzle Kit:** Create a `drizzle.config.ts` file in your project root.

    ```typescript
    // drizzle.config.ts
    import type { Config } from 'drizzle-kit';

    export default {
      schema: './src/entities/*.schema.ts', // Point to your schema files
      out: './drizzle', // Directory for generated migrations
      driver: 'pg', // or 'mysql', 'sqlite' etc.
      dbCredentials: {
        connectionString: process.env.DATABASE_URL!, // Get connection string from env
      },
    } satisfies Config;

    ```

3. **Generate Migrations:** Use the Drizzle Kit CLI.

    ```bash
    npx drizzle-kit generate:pg --config=drizzle.config.ts
    # Or the command specific to your DB driver
    ```

    This will create migration files in your `out` directory (`./drizzle`).

4. **Apply Migrations:** Write a script or use a tool within your application to apply these migrations. You'll typically use the DrizzleORM `migrate` function.

    ```typescript
    // Example script or service to run migrations
    import { migrate } from 'drizzle-orm/pg/node-postgres/migrator'; // Adjust import based on your driver
    import { DrizzleService } from '../src/drizzle/drizzle.service'; // Assuming DrizzleService injects the client
    import { join } from 'path';

    async function runMigrations() {
        const drizzleService = new DrizzleService(/* pass client instance */); // Get your Drizzle client

        console.log("Running migrations...");
        await migrate(drizzleService.db, { migrationsFolder: join(__dirname, './drizzle') }); // Path to your migration files
        console.log("Migrations finished.");

        // Close connection if necessary
        // await drizzleService.close();
    }

    runMigrations().catch((err) => {
        console.error('Migration failed:', err);
        process.exit(1);
    });
    ```

    *Tip:* Run migrations during application startup in production or via a separate CI/CD step.

---

## 20. Follow Following naming conventions

| **What**               | **How**                                   | **Good Example**             | **Bad Example**                 |
|------------------------|-------------------------------------------|------------------------------|---------------------------------|
| **Controller**         | Singular name                             | `UserController`             | `UsersController`               |
| **Route**              | Plural in URL                             | `/users/1`                   | `/user/1`                       |
| **Route Name**         | Snake_case with dot notation              | `users.show_active`          | `users.show-active` or `show-active-users` |
| **Service**            | Singular name                             | `UserService`                | `UsersService`                  |
| **Module**             | Singular name                             | `UserModule`                 | `UsersModule`                   |
| **DTO**                | PascalCase with 'Dto' suffix              | `CreateUserDto`              | `create_user_dto`               |
| **Entity/Model**       | Singular name                             | `User`                       | `Users`                         |
| **Enum**               | Singular name                             | `UserRole`                   | `UserRoles`                     |
| **Repository**         | Singular name                             | `UserRepository`             | `UsersRepository`               |
| **Interceptor**        | Singular name                             | `LoggingInterceptor`         | `LoggingInterceptors`           |
| **Guard**              | Singular name                             | `AuthGuard`                  | `AuthGuards`                    |
| **Pipe**               | Singular name                             | `ValidationPipe`             | `ValidationPipes`               |
| **Exception Filter**   | Singular name                             | `HttpExceptionFilter`        | `HttpExceptionFilters`          |
| **Middleware**         | Singular name                             | `LoggerMiddleware`           | `LoggerMiddlewares`             |
| **Custom Decorator**   | camelCase for function names              | `@CurrentUser()`             | `@current_user()`               |
| **Test File**          | Use `.spec.ts` suffix                     | `user.controller.spec.ts`    | `UserController.test.ts`        |
| **File Naming**        | kebab-case (all lowercase with hyphens)   | `user.controller.ts`         | `UserController.ts`             |

---

## 21. Other Good Practices

- **Comment Wisely:** Use descriptive names for methods and variables to reduce the need for comments.
- **Avoid Inline HTML/JS in Server Code:** Keep front-end concerns separate from backend logic.
- **Use Standard NestJS Tools:** Stick to Nest’s ecosystem (e.g., Pipes, Guards, Interceptors) rather than mixing in non-standard solutions.
- **Leverage Language Files:** When internationalizing messages, use a dedicated translation module rather than hardcoding strings.
- **Do Not Override Framework Defaults:** Use NestJS’s built-in features unless you have a compelling reason not to.
- **Use Modern TypeScript Syntax:** Embrace async/await, decorators, and other modern patterns for clarity and conciseness.

---

Enjoy building your NestJS application with these guidelines in mind!
