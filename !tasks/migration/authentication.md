# Authentication

## Operator Login Flow

1. **Fetch Operators:**
    * <PERSON><PERSON> (Ang<PERSON> `AuthComponent`) makes an HTTP GET request to `/api/getOperators`.
    * Server (`routes.js` -> `websocket.js` -> `core.js`) responds with a list of `OperatorInfo` objects (id, name, disabled status) from the `operators` PostgreSQL table.
    * Client displays these operators in a dropdown/modal.

2. **Select Operator & Connect WebSocket:**
    * User selects an operator.
    * <PERSON><PERSON> (Angular `AuthenticationService`) initiates a WebSocket connection to `ws://<host>:<port>/?v=<client_version>&operator_id=<selected_operator_id>`.
    * The `operator_id` is passed as a query parameter.

3. **Server-Side WebSocket Authentication:**
    * Server (`websocket.js` `verifyClient` function) receives the connection request.
    * It parses the `operator_id` from the URL.
    * It validates the `operator_id` against the list of known (and not disabled) operators (from `core.taxiSettings.operators`, which was loaded from DB).
    * If valid:
        * The WebSocket connection is accepted and associated with this `operator_id`.
        * The server checks if this `operator_id` is already connected from another WebSocket.
            * If yes, it may terminate the old connection or reject the new one (logic in `OperatorServer` constructor in `operatorServer.js`).
            * If no conflict, the connection is established.
        * The server sends a `client:init` event (unicast to this client) with initial application settings and configurations.
    * If invalid (unknown operator, disabled, etc.):
        * The WebSocket connection is rejected (e.g., HTTP 401 during handshake).

4. **Client-Side Session Management:**
    * Upon successful WebSocket connection and receipt of `client:init`, the client (Angular `AuthenticationService`) stores the selected operator's details (id, name, timestamp of login) in `localStorage`.
    * The application UI updates to reflect the authenticated state.

5. **Subsequent Requests:**
    * All subsequent WebSocket messages from this client are implicitly associated with the authenticated `operator_id` on the server side.
    * HTTP requests (if any require auth in the future) would need a separate token-based mechanism (e.g., JWT), which is not detailed in the current backend.

## Logout Flow

1. **Client Initiates Logout:**
    * User clicks logout.
    * Client (Angular `AuthenticationService`) clears the operator details from `localStorage`.
    * It then re-initializes the WebSocket connection *without* an `operator_id` (or signals the existing connection to change state to anonymous).

2. **Server Handles Logout/Anonymous Connection:**
    * Server (`websocket.js`) processes the disconnection of the authenticated session or the transition to an anonymous one.
    * The `client:exit` event is triggered internally on the backend for the logged-out operator, which can perform cleanup like unlocking any calls locked by that operator (`eventHandlers.js`).
    * A new `client:init` may be sent for the anonymous session.

## Visual Flow (Sequence Diagram)

```mermaid
sequenceDiagram
  participant ClientApp as Client Angular App
  participant BrowserLocalStorage as Local Storage
  participant BackendHttp as Backend HTTP API
  participant BackendWebSocket as Backend WebSocket Server
  participant PostgreSQLDB as PostgreSQL DB

  ClientApp->>BackendHttp: GET /api/getOperators
  BackendHttp->>PostgreSQLDB: SELECT id, name, disabled FROM operators
  PostgreSQLDB-->>BackendHttp: Operator List
  BackendHttp-->>ClientApp: Operator List JSON

  activate ClientApp
  ClientApp->>ClientApp: User selects Operator (e.g., ID: 123)
  ClientApp->>BrowserLocalStorage: Store OperatorInfo (id: 123, name, timestamp)
  ClientApp->>BackendWebSocket: WS Connect (url: ...&operator_id=123)
  deactivate ClientApp

  activate BackendWebSocket
  BackendWebSocket->>BackendWebSocket: verifyClient(operator_id=123)
  alt Operator Valid & Not Already Connected
    BackendWebSocket->>ClientApp: client:init (config, settings)
    ClientApp->>ClientApp: Update UI to authenticated state
  else Operator Invalid or Conflict
    BackendWebSocket-->>ClientApp: WS Connection Rejected / Old one terminated
  end
  deactivate BackendWebSocket

  loop Authenticated Operations
    ClientApp->>BackendWebSocket: WebSocket Event (e.g., call:update)
    BackendWebSocket->>ClientApp: WebSocket Event (e.g., call:update broadcast)
  end

  ClientApp->>ClientApp: User clicks Logout
  activate ClientApp
  ClientApp->>BrowserLocalStorage: Remove OperatorInfo
  ClientApp->>BackendWebSocket: WS Connect (url: ... no operator_id) or signal change
  deactivate ClientApp

  activate BackendWebSocket
  BackendWebSocket->>BackendWebSocket: Process client:exit for old operator_id
  BackendWebSocket->>ClientApp: client:init (for anonymous session, optional)
  deactivate BackendWebSocket
  ClientApp->>ClientApp: Update UI to logged-out state
```
