import { relations } from "drizzle-orm";
import { sql } from "drizzle-orm";
import {
	boolean,
	jsonb,
	pgTable,
	timestamp,
	uniqueIndex, // Moved jsonb import
	uuid,
	varchar, // Moved varchar import
} from "drizzle-orm/pg-core"; // Group imports

import { chatbotInstances } from "./chatbot-instances.schema.js";
import { users } from "./users.schema.js";

export const chatbotUsers = pgTable(
	"chatbot_users",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		userId: uuid("user_id")
			.references(() => users.id)
			.notNull(),
		chatbotInstanceId: uuid("chatbot_instance_id")
			.references(() => chatbotInstances.id)
			.notNull(),
		// Could be "set null" or a more specific action if chatbot instance is deleted
		providerUserId: varchar("provider_user_id", { length: 255 }),
		phoneVerified: boolean("phone_verified").default(false).notNull(),
		verificationDate: timestamp("verification_date").notNull(),
		consent: boolean("consent").notNull().default(false),
		consentDate: timestamp("consent_date"),
		consentRevokedAt: timestamp("consent_revoked_at"),
		blocked: boolean("blocked").notNull().default(false), // Consider renaming to is_blocked
		verifiedAt: timestamp("verified_at"), // Consider renaming to verified_at
		locale: varchar("locale", { length: 10 }),
		joinedAt: timestamp("joined_at").defaultNow().notNull(),
		lastSeenAt: timestamp("last_seen_at"),
		metadata: jsonb("metadata").$type<{
			provider_user_id?: string;
			sms_verification_status?: string;
			sms_verification_timestamp?: Date;
		}>(),
	},
	(table) => [
		uniqueIndex("provider_user_id_chatbot_instance_idx")
			.on(table.providerUserId, table.chatbotInstanceId)
			.where(sql`${table.providerUserId} IS NOT NULL`),
		uniqueIndex("user_id_chatbot_instance_idx").on(
			table.userId,
			table.chatbotInstanceId,
		),
	],
);

export const chatbotUsersRelations = relations(chatbotUsers, ({ one }) => ({
	user: one(users, {
		fields: [chatbotUsers.userId],
		references: [users.id],
	}),
	chatbotInstance: one(chatbotInstances, {
		fields: [chatbotUsers.chatbotInstanceId],
		references: [chatbotInstances.id],
	}),
}));
