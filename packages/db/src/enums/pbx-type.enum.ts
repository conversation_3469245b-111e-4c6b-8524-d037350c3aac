import { pgEnum } from "drizzle-orm/pg-core";

// 1. Define enum values as the single source of truth
const PBX_TYPE_VALUES = ["ASTERISK_ARI", "FREEPBX_GRAPHQL"] as const;

// 2. Create the Drizzle ORM pgEnum for database schema definition
export const pbxTypeEnum = pgEnum("pbx_type", PBX_TYPE_VALUES)

// 3. Define TypeScript union type for strict type safety
export type PbxTypeType = (typeof PBX_TYPE_VALUES)[number];

// 4. Define TypeScript string enum for application logic
export enum PbxType {
	ASTERISK_ARI = "ASTERISK_ARI",
	FREEPBX_GRAPHQL = "FREEPBX_GRAPHQL",
}
