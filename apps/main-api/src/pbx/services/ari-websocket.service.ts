// apps/main-api/src/pbx/services/ari-websocket.service.ts
import { Injectable, Logger, type OnModuleDestroy } from '@nestjs/common';
import WebSocket from 'ws'; // Standard 'ws' library
import { EventEmitter } from 'node:events'; // Use node: prefix
import { backOff } from 'exponential-backoff';
import type { AriConnectionDetails } from '@repo/types'; // Your type from @repo/types
import type { PbxEventRouterService } from './pbx-event-router.service'; // To route events

export interface AriWebSocketClient extends EventEmitter {
  connect: () => Promise<void>;
  close: () => Promise<void>;
  isConnected: () => boolean;
  getPbxInstanceId: () => string;
}

// This class is an internal implementation detail, not directly injected elsewhere
class AriWebSocketClientImpl extends EventEmitter implements AriWebSocketClient {
  private ws?: WebSocket;
  private isReconnecting = false;
  private shouldReconnect = true;
  private readonly MAX_RECONNECT_ATTEMPTS = 30;
  private readonly RECONNECT_BASE_DELAY = 1000;
  private readonly ARI_APP_NAME: string;
  private readonly WS_URL: string;
  private heartbeatInterval?: NodeJS.Timeout;


  constructor(
    private readonly pbxInstanceId: string,
    connDetails: AriConnectionDetails, // Type from @repo/types
    private readonly logger: Logger,
    private readonly eventRouter: PbxEventRouterService,
  ) {
    super();
    this.ARI_APP_NAME = connDetails.appName;
    const protocol = connDetails.url.startsWith("https") ? "wss" : "ws";
    // connDetails.url is the base HTTP URL like http://host:port
    // We need to construct the WebSocket URL for events
    const baseHttpUrl = connDetails.url.replace(/\/ari$/, ''); // Remove /ari if present
    const hostAndPort = new URL(baseHttpUrl).host; // Extracts host:port

    const queryParams = new URLSearchParams({ app: this.ARI_APP_NAME, subscribeAll: 'true' });
    // ARI WebSocket authentication is typically done via username/password in the URL for wscat,
    // but the 'ws' library might need it in options or you might handle it via initial HTTP handshake if ARI supports it.
    // For basic auth over WS, it's often part of the URL.
    // If your ARI setup uses API keys for WS, that's different.
    // Assuming username/password in URL for now, which is common for basic ARI setups.
    this.WS_URL = `${protocol}://${connDetails.username}:${connDetails.password}@${hostAndPort}/ari/events?${queryParams.toString()}`;
  }

  getPbxInstanceId(): string {
    return this.pbxInstanceId;
  }

  async connect(): Promise<void> {
    if (this.isConnected() || this.isReconnecting) {
      this.logger.debug(`PBX ${this.pbxInstanceId}: WebSocket already connected or connecting.`);
      return;
    }
    this.shouldReconnect = true;
    this.isReconnecting = true;

    this.logger.log(`PBX ${this.pbxInstanceId}: Attempting WebSocket connection to ARI app '${this.ARI_APP_NAME}'`);
    this.logger.verbose(`PBX ${this.pbxInstanceId}: WS URL: ${this.WS_URL.replace(/:[^@]*@/, ':<REDACTED_PASS>@')}`);


    try {
      await backOff(async () => {
        return new Promise<void>((resolve, reject) => {
          if (this.ws && this.ws.readyState !== WebSocket.CLOSED && this.ws.readyState !== WebSocket.CLOSING) {
            this.logger.warn(`PBX ${this.pbxInstanceId}: Previous WebSocket instance not fully closed (state: ${this.ws.readyState}). Terminating.`);
            this.ws.terminate(); // Force close
            this.ws.removeAllListeners(); // Clean up listeners from old instance
          }

          this.ws = new WebSocket(this.WS_URL, {
            // Add any necessary WebSocket options here, e.g., for self-signed certs if using WSS locally:
            // rejectUnauthorized: process.env.NODE_ENV === 'production',
          });

          this.ws.on('open', () => {
            this.logger.log(`PBX ${this.pbxInstanceId}: WebSocket connected to ARI app '${this.ARI_APP_NAME}'.`);
            this.isReconnecting = false;
            this.emit('connected'); // Emit internal event
            this.setupHeartbeat();
            resolve();
          });

          this.ws.on('message', (data: WebSocket.RawData, _isBinary: boolean) => {
            try {
              const event = JSON.parse(data.toString());
              // Route the raw ARI event
              this.eventRouter.routeAriEvent(this.pbxInstanceId, event.type, event);
            } catch (e) {
              this.logger.error(`PBX ${this.pbxInstanceId}: Error parsing ARI event JSON:`, e);
            }
          });

          this.ws.on('close', (code, reason) => {
            const reasonStr = reason ? reason.toString() : 'No reason provided';
            this.logger.warn(`PBX ${this.pbxInstanceId}: WebSocket disconnected. Code: ${code}, Reason: ${reasonStr}`);
            this.clearHeartbeat();
            this.emit('disconnected'); // Emit internal event
            if (this.shouldReconnect) {
              this.isReconnecting = false; // Allow reconnect to start
              this.reconnect();
            }
            // Important: For backOff to retry, the promise from this attempt must reject.
            // If 'close' means a failed connection attempt, reject.
            // If 'close' is an expected closure (e.g., server shutdown), maybe resolve.
            // For now, assume any close during initial connection phase is a failure for backOff.
            if (!this.isConnected()) { // Only reject if we weren't truly connected before close
                reject(new Error(`WebSocket closed with code ${code}: ${reasonStr}`));
            }
          });

          this.ws.on('error', (err) => {
            this.logger.error(`PBX ${this.pbxInstanceId}: WebSocket error: ${err.message}`, err.stack);
            // 'error' event is often followed by 'close'. Let 'close' handle reconnection logic.
            // backOff will retry if the promise from this attempt rejects.
            reject(err);
          });
        });
      }, {
        numOfAttempts: this.MAX_RECONNECT_ATTEMPTS,
        startingDelay: this.RECONNECT_BASE_DELAY,
        retry: (e: any, attemptNumber: number) => {
          this.logger.warn(`PBX ${this.pbxInstanceId}: WebSocket connection attempt ${attemptNumber} failed. Retrying... Error: ${e.message}`);
          return this.shouldReconnect; // Only retry if shouldReconnect is true
        }
      });
    } catch (error) {
        this.logger.error(`PBX ${this.pbxInstanceId}: All WebSocket (re)connection attempts failed. Error:`, error);
        this.isReconnecting = false;
        // Consider emitting a final failure event for monitoring
    }
  }

  private setupHeartbeat(): void {
    this.clearHeartbeat(); // Clear any existing heartbeat
    this.heartbeatInterval = setInterval(() => {
      if (this.ws?.readyState === WebSocket.OPEN) {
        this.ws.ping((err: Error | undefined) => {
          if (err) {
            this.logger.error(`PBX ${this.pbxInstanceId}: Ping failed, closing connection:`, err);
            this.ws?.terminate(); // Terminate on ping failure, 'close' event will trigger reconnect
          } else {
            this.logger.verbose(`PBX ${this.pbxInstanceId}: Ping sent`);
          }
        });
      }
    }, 30000); // Send ping every 30 seconds

    this.ws?.on('pong', () => {
      this.logger.verbose(`PBX ${this.pbxInstanceId}: Pong received`);
    });
  }

  private clearHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = undefined;
    }
  }

  private reconnect() {
    if (!this.shouldReconnect || this.isReconnecting || this.isConnected()) {
      return;
    }
    this.logger.log(`PBX ${this.pbxInstanceId}: Scheduling reconnect...`);
    this.connect().catch(err => { // connect() now handles backOff
        this.logger.error(`PBX ${this.pbxInstanceId}: Unhandled error during reconnect initiation: `, err);
    });
  }

  async close(): Promise<void> {
    this.logger.log(`PBX ${this.pbxInstanceId}: Closing WebSocket connection intentionally.`);
    this.shouldReconnect = false;
    this.clearHeartbeat();
    if (this.ws) {
      if (this.ws.readyState === WebSocket.OPEN || this.ws.readyState === WebSocket.CONNECTING) {
        return new Promise((resolve) => {
          this.ws?.removeAllListeners(); // Clean up listeners to prevent issues on re-connect
          this.ws?.once('close', () => {
            this.logger.log(`PBX ${this.pbxInstanceId}: WebSocket connection closed.`);
            this.ws = undefined;
            resolve();
          });
          this.ws?.close(1000, "Client initiated graceful disconnect");
        });
      }
      this.ws = undefined;
    }
  }

  isConnected(): boolean {
    return !!this.ws && this.ws.readyState === WebSocket.OPEN;
  }
}

// AriWebSocketManagerService remains the same as previously defined,
// it will create instances of AriWebSocketClientImpl.
@Injectable()
export class AriWebSocketManagerService implements OnModuleDestroy {
  private readonly logger = new Logger(AriWebSocketManagerService.name);
  private clients = new Map<string, AriWebSocketClientImpl>(); // pbxInstanceId -> client

  constructor(
    // No need to inject PbxConfigService here if createClient takes full connDetails
    private readonly eventRouter: PbxEventRouterService, // For AriWebSocketClientImpl
  ) {}

  createClient(pbxInstanceId: string, connDetails: AriConnectionDetails): AriWebSocketClient {
    if (this.clients.has(pbxInstanceId)) {
      this.logger.warn(`WebSocket client for PBX ${pbxInstanceId} already exists.`);
 return this.clients.get(pbxInstanceId) as AriWebSocketClientImpl; // Cast is safe here because has() check passed
    }

    // Pass the logger to AriWebSocketClientImpl
    const client = new AriWebSocketClientImpl(pbxInstanceId, connDetails, new Logger(`${AriWebSocketClientImpl.name}-${pbxInstanceId.substring(0,8)}`), this.eventRouter);
    this.clients.set(pbxInstanceId, client);
    return client;
  }

  getClient(pbxInstanceId: string): AriWebSocketClient | undefined {
    return this.clients.get(pbxInstanceId);
  }

  async onModuleDestroy() {
    this.logger.log('Destroying AriWebSocketManagerService, closing all connections...');
    for (const client of this.clients.values()) {
      await client.close();
    }
    this.clients.clear();
  }
}