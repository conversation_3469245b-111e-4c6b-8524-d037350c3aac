{"bot.passenger.rateDriver.enteredLog": "Корисникот {{userId}} влезе во сцената за оценување на возачот", "bot.passenger.rateDriver.prompt": "Како би го оцениле вашиот возач?", "bot.passenger.rateDriver.star1": "⭐", "bot.passenger.rateDriver.star2": "⭐⭐", "bot.passenger.rateDriver.star3": "⭐⭐⭐", "bot.passenger.rateDriver.star4": "⭐⭐⭐⭐", "bot.passenger.rateDriver.star5": "⭐⭐⭐⭐⭐", "bot.passenger.rateDriver.leftLog": "Кори<PERSON>никот {{userId}} ја напушти сцената за оценување", "bot.passenger.rateDriver.thankYou": "Ви благодариме за вашата повратна информација!", "bot.passenger.rateDriver.callbackLog": "Корисникот {{userId}} активираше callback: {{data}}", "bot.passenger.rateDriver.ratedLog": "Корисникот {{userId}} го оцени возачот со {{rating}} ѕвезди", "bot.passenger.rateDriver.ratedCb": "Го оценивте возачот со {{rating}} ѕвезди", "bot.passenger.rateDriver.additionalFeedbackPrompt": "Дали сакате да дадете дополнителна повратна информација?", "bot.passenger.rateDriver.yes": "Да", "bot.passenger.rateDriver.no": "Не", "bot.passenger.rateDriver.thankYou5Star": "Ви благодариме за вашата оценка од 5 ѕвезди!", "bot.passenger.rateDriver.provideFeedbackCb": "Ве молиме внесете ваша повратна информација", "bot.passenger.rateDriver.typeFeedbackPrompt": "Ве молиме напишете ја вашата повратна информација подолу:", "bot.passenger.rateDriver.thankYouCb": "Ви благодариме за вашата оценка", "bot.passenger.rateDriver.feedbackLog": "Корисникот {{userId}} испрати повратна информација: {{feedback}}", "bot.passenger.rideStatus.enteredLog": "Корисникот {{userId}} влезе во сцената за статус на возење", "bot.passenger.rideStatus.statusPrompt": "Статус на вашето возење:", "bot.passenger.rideStatus.refreshStatus": "Освежи статус", "bot.passenger.rideStatus.cancelRide": "Откажи возење", "bot.passenger.rideStatus.contactDriver": "Контактира<PERSON> возач", "bot.passenger.rideStatus.backToMenu": "Назад во мени", "bot.passenger.rideStatus.driverFound": "Пронајден е возач! Возачот е на пат кон вашата локација.", "bot.passenger.rideStatus.statusUpdateErrorLog": "Грешка при испраќање на статус: {{error}}", "bot.passenger.rideStatus.leftLog": "Корисникот {{userId}} ја напушти сцената за статус на возење", "bot.passenger.rideStatus.returningToMenu": "Се враќаме во главното мени", "bot.passenger.rideStatus.callbackLog": "Корисникот {{userId}} активираше callback: {{data}}", "bot.passenger.rideStatus.refreshingStatusCb": "Се освежува статусот...", "bot.passenger.rideStatus.driver5minAway": "Вашиот возач е оддалечен 5 минути.", "bot.passenger.rideStatus.cancellingRideCb": "Се откажува возењето...", "bot.passenger.rideStatus.rideCancelled": "Вашето возење е откажано.", "bot.passenger.rideStatus.contactingDriverCb": "Се контактира возачот...", "bot.passenger.rideStatus.connectingWithDriver": "Ве поврзуваме со возачот...", "bot.passenger.rideStatus.returningToMenuCb": "Се враќаме во мени...", "bot.passenger.rideStatus.unknownActionCb": "Непозната акција", "bot.passenger.rideStatus.textLog": "Корисникот {{userId}} испрати порака во сцената за статус: {{text}}", "bot.passenger.bookRideWizard.rideTypePrompt": "Ве молиме изберете тип на возење:", "bot.passenger.bookRideWizard.providedDropoffAddressLog": "Корисникот {{userId}} внесе адреса за дестинација: {{address}}", "bot.passenger.bookRideWizard.selectedRideTypeLog": "Корисникот {{userId}} избра тип на возење: {{rideType}}", "bot.passenger.bookRideWizard.confirmPrompt": "Ве молиме потврдете ги деталите за возењето:\n\nПоаѓање: {{pickup}}\nДестинација: {{dropoff}}\nТип на возење: {{rideType}}\nПроценета цена: {{estimatedFare}} МКД\n\nДали сакате да го потврдите ова возење? (Да/Не)", "bot.passenger.bookRideWizard.confirmedRideLog": "Корисникот {{userId}} го потврди возењето", "bot.passenger.bookRideWizard.creatingRideLog": "Креирање возење: {{rideData}}", "bot.passenger.bookRideWizard.lookingForDrivers": "Се бараат возачи...", "bot.passenger.bookRideWizard.cancelRideButton": "Откажи возење", "bot.passenger.bookRideWizard.rideBooked": "Вашето возење е резервирано! Бараме возачи во близина.", "bot.passenger.bookRideWizard.rejectedRideLog": "Корисникот {{userId}} го одби возењето", "bot.passenger.bookRideWizard.bookingCancelled": "Резервацијата е откажана. Што сакате да направите следно?", "bot.passenger.bookRideWizard.enteredLog": "Корисникот {{userId}} започна со резервирање возење", "bot.passenger.bookRideWizard.pickupPrompt": "Ве молиме споделете ја вашата локација за поаѓање:\n1. Испратете ја вашата моментална локација преку копчето подолу\n2. Внесете адреса\n3. Испратете пин со локација", "bot.passenger.bookRideWizard.locationButtonPrompt": "Ве молиме споделете ја вашата локација преку копчето подолу.", "bot.passenger.bookRideWizard.sharedPickupLocationLog": "Корисникот {{userId}} сподели локација за поаѓање: {{latitude}}, {{longitude}}", "bot.passenger.bookRideWizard.dropoffPrompt": "Одлично! Сега споделете ја вашата дестинација:", "bot.passenger.bookRideWizard.providedPickupAddressLog": "Корисникот {{userId}} внесе адреса за поаѓање: {{address}}", "bot.passenger.confirmYes": "Да", "bot.passenger.confirmYesShort": "Д", "bot.passenger.confirmedLog": "Корисникот {{userId}} ги потврди информациите за регистрација", "bot.passenger.savingUserDataLog": "Се зачувуваат податоците за корисникот: {{userData}}", "bot.passenger.registrationCompleteMenu": "Регистрацијата е завршена! Што сакате да направите следно?", "bot.passenger.bookRide": "Резервирај возење", "bot.passenger.myRides": "Мои возења", "bot.passenger.settings": "Поставки", "bot.passenger.help": "Помош", "bot.passenger.registrationSuccess": "Сега сте регистрирани како патник!", "bot.passenger.rejectedLog": "Корисникот {{userId}} ги одби информациите за регистрација", "bot.passenger.restartPrompt": "Да започнеме одново. Ве молиме изберете го вашиот јазик:", "bot.passenger.registrationWelcome": "Добредојдовте во регистрација на патник, {{firstName}}! Ве молиме изберете го вашиот јазик:", "bot.passenger.selectedLanguageLog": "Корисникот {{userId}} избра јазик: {{language}}", "bot.passenger.phonePrompt": "Ве молиме внесете го вашиот телефонски број за да можеме да ве контактираме ако е потребно:", "bot.passenger.contactButtonPrompt": "Ве молиме споделете го вашиот контакт преку копчето подолу.", "bot.passenger.sharedPhoneLog": "Корисникот {{userId}} сподели телефонски број: {{phone}}", "bot.passenger.emailPrompt": "Ве молиме внесете ја вашата е-пошта:", "bot.passenger.providedEmailLog": "Корисникот {{userId}} внесе е-пошта: {{email}}", "bot.passenger.confirmPrompt": "Ве молиме потврдете ги вашите информации:\nИме: {{name}}\nТелефон: {{phone}}\nЕ-пошта: {{email}}\nЈазик: {{language}}\nДали е ова точно? (Да/Не)", "passengers": {"registerPrompt": "За да резервирате возење, мора да се регистрирате како патник. Користете /passenger за регистрација.", "alreadyRegistered": "Веќе сте регистрирани како патник.", "registrationSuccess": "Регистрацијата е успешна! Сега можете да резервирате возења.", "bookingStarted": "Да започнеме со резервацијата на вашето возење.", "bookingCancelled": "Резервацијата е откажана. Што сакате следно?", "notFound": "Патникот не е пронајден. Ве молиме прво регистрирајте се."}}