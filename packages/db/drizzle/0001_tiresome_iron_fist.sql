CREATE TYPE "public"."bot_status_enum" AS ENUM('RUNNING', 'STOPPED', 'ERROR');--> statement-breakpoint
CREATE TYPE "public"."call_source_enum" AS ENUM('PBX_INCOMING', 'PBX_OUTGOING', 'MANUAL', 'CHATBOT_TELEGRAM', 'CHATBOT_VIBER', 'CHATBOT_WHATSAPP', 'SCHEDULED_ACTIVATION', 'APP_PASSENGER', 'APP_DRIVER', 'API', 'DUPLICATE');--> statement-breakpoint
CREATE TYPE "public"."fuel_type_enum" AS ENUM('GASOLINE', 'DIESEL', 'ELECTRIC', 'HYBRID', 'LPG', 'CNG', 'HYDROGEN', 'OTHER');--> statement-breakpoint
CREATE TYPE "public"."promotion_type_enum" AS ENUM('PERCENTAGE', 'VALUE', 'FREE_RIDE');--> statement-breakpoint
CREATE TYPE "public"."reminder_urgency_enum" AS ENUM('NOT_URGENT', 'URGENT', 'VERY_URGENT', 'PAST_DUE');--> statement-breakpoint
CREATE TYPE "public"."scheduled_call_status_enum" AS ENUM('PENDING', 'ACTIVE_ONCE', 'ACTIVE_RECURRING', 'COMPLETED', 'PAUSED', 'DISABLED');--> statement-breakpoint
CREATE TYPE "public"."sms_log_direction_enum" AS ENUM('OUTBOUND_OPERATOR', 'INBOUND_REPLY');--> statement-breakpoint
CREATE TYPE "public"."sms_status_enum" AS ENUM('PENDING', 'QUEUED', 'SENT', 'FAILED', 'DELIVERED', 'UNDELIVERED', 'RECEIVED', 'READ');--> statement-breakpoint
CREATE TYPE "public"."vehicle_expense_type_enum" AS ENUM('TAX', 'INSURANCE', 'REGISTRATION', 'MAINTENANCE', 'FUEL', 'OTHER');--> statement-breakpoint
CREATE TYPE "public"."verification_method_enum" AS ENUM('SMS', 'EMAIL', 'PHONE_CALL', 'OTHER');--> statement-breakpoint
CREATE TYPE "public"."verification_status_enum" AS ENUM('INITIATED', 'CODE_SENT', 'VERIFIED', 'FAILED', 'EXPIRED');--> statement-breakpoint
ALTER TYPE "public"."calls_status_enum" ADD VALUE 'SCHEDULED' BEFORE 'RINGING';--> statement-breakpoint
CREATE TABLE "addresses" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid,
	"street_line_1" varchar(255),
	"street_line_2" varchar(255),
	"city" varchar(100),
	"state_province" varchar(100),
	"postal_code" varchar(20),
	"country_code" varchar(2) NOT NULL,
	"formatted_address" text,
	"raw_input_address" text,
	"geom" geometry(point),
	"latitude" double precision,
	"longitude" double precision,
	"geocoding_provider" varchar(50),
	"geocoding_accuracy" varchar(50),
	"osm_place_id" varchar(50),
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "operator_performance_stats" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"operator_user_id" uuid NOT NULL,
	"date" timestamp with time zone NOT NULL,
	"shift_id" uuid,
	"answered_calls" integer DEFAULT 0 NOT NULL,
	"missed_calls" integer DEFAULT 0 NOT NULL,
	"outbound_calls" integer DEFAULT 0 NOT NULL,
	"total_call_duration_seconds" integer DEFAULT 0 NOT NULL,
	"avg_handle_time_seconds" integer DEFAULT 0 NOT NULL,
	"rides_dispatched" integer DEFAULT 0 NOT NULL,
	"sms_sent" integer DEFAULT 0 NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "pbx_call_additional_operators" (
	"pbx_call_id" uuid NOT NULL,
	"operator_id" uuid NOT NULL,
	"assigned_at" timestamp with time zone DEFAULT now() NOT NULL,
	CONSTRAINT "pbx_call_additional_operators_pbx_call_id_operator_id_pk" PRIMARY KEY("pbx_call_id","operator_id")
);
--> statement-breakpoint
CREATE TABLE "scheduled_calls" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"created_by_operator_id" uuid,
	"customer_user_id" uuid,
	"phone_number" varchar(50),
	"base_call_details" jsonb,
	"scheduled_timestamp" timestamp with time zone,
	"arrival_time_offset_minutes" integer,
	"repeat_pattern" jsonb,
	"next_activation_timestamp" timestamp with time zone,
	"last_activated_timestamp" timestamp with time zone,
	"is_active" boolean DEFAULT true NOT NULL,
	"status" "scheduled_call_status_enum" DEFAULT 'PENDING' NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "sms_log" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"direction" "sms_log_direction_enum" NOT NULL,
	"from_number" varchar(50) NOT NULL,
	"to_number" varchar(50) NOT NULL,
	"content" text NOT NULL,
	"status" "sms_status_enum",
	"gateway_reference_id" varchar(255),
	"operator_user_id" uuid,
	"related_pbx_call_id" uuid,
	"logged_at" timestamp with time zone DEFAULT now() NOT NULL,
	"error_message" text
);
--> statement-breakpoint
CREATE TABLE "vehicle_expense" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"vehicle_id" uuid NOT NULL,
	"expense_type" "vehicle_expense_type_enum" NOT NULL,
	"date" date NOT NULL,
	"description" text NOT NULL,
	"cost" numeric(10, 2) NOT NULL,
	"notes" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "vehicle_note" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"vehicle_id" uuid NOT NULL,
	"description" varchar(255),
	"note" text NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "vehicle_odometer_log" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"vehicle_id" uuid NOT NULL,
	"date" date NOT NULL,
	"initial_value" integer,
	"odometer_value" integer NOT NULL,
	"notes" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "vehicle_parts_log" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"vehicle_id" uuid NOT NULL,
	"date" date NOT NULL,
	"part_number" varchar(100),
	"supplier" varchar(255),
	"description" text NOT NULL,
	"quantity" integer DEFAULT 1 NOT NULL,
	"cost_per_unit" numeric(10, 2),
	"total_cost" numeric(12, 2),
	"notes" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "vehicle_reminder" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"vehicle_id" uuid NOT NULL,
	"urgency" "reminder_urgency_enum" DEFAULT 'NOT_URGENT' NOT NULL,
	"trigger_odometer" integer,
	"trigger_date" date,
	"description" text NOT NULL,
	"notes" text,
	"is_dismissed" boolean DEFAULT false NOT NULL,
	"dismissed_at" timestamp with time zone,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "vehicle_repair" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"vehicle_id" uuid NOT NULL,
	"date" date NOT NULL,
	"odometer" integer,
	"description" text NOT NULL,
	"cost" numeric(10, 2),
	"notes" text,
	"repair_shop" varchar(255),
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "vehicle_service_record" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"vehicle_id" uuid NOT NULL,
	"date" date NOT NULL,
	"odometer" integer,
	"description" text NOT NULL,
	"cost" numeric(10, 2),
	"notes" text,
	"service_provider" varchar(255),
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "vehicle_upgrade" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"tenant_id" uuid NOT NULL,
	"vehicle_id" uuid NOT NULL,
	"date" date NOT NULL,
	"odometer" integer,
	"description" text NOT NULL,
	"cost" numeric(10, 2),
	"notes" text,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "payment" ALTER COLUMN "status" SET DATA TYPE text;--> statement-breakpoint
ALTER TABLE "payment" ALTER COLUMN "status" SET DEFAULT 'PENDING'::text;--> statement-breakpoint
DROP TYPE "public"."payment_status_enum";--> statement-breakpoint
CREATE TYPE "public"."payment_status_enum" AS ENUM('PENDING', 'SUCCESS', 'FAILED');--> statement-breakpoint
ALTER TABLE "payment" ALTER COLUMN "status" SET DEFAULT 'PENDING'::"public"."payment_status_enum";--> statement-breakpoint
ALTER TABLE "payment" ALTER COLUMN "status" SET DATA TYPE "public"."payment_status_enum" USING "status"::"public"."payment_status_enum";--> statement-breakpoint
DROP INDEX "messages_phone_verified_idx";--> statement-breakpoint
DROP INDEX "chatbot_session_geolocation_idx";--> statement-breakpoint
DROP INDEX "geodata_entries_geom_idx";--> statement-breakpoint
ALTER TABLE "user_bot_roles" DROP CONSTRAINT "user_bot_roles_telegram_user_id_bot_id_role_id_pk";--> statement-breakpoint
ALTER TABLE "pbx_call" ALTER COLUMN "started_at" SET DATA TYPE timestamp with time zone;--> statement-breakpoint
ALTER TABLE "pbx_call" ALTER COLUMN "ended_at" SET DATA TYPE timestamp with time zone;--> statement-breakpoint
ALTER TABLE "promotion" ALTER COLUMN "type" SET DATA TYPE "public"."promotion_type_enum" USING "type"::text::"public"."promotion_type_enum";--> statement-breakpoint
ALTER TABLE "tenant_bots" ALTER COLUMN "status" DROP DEFAULT;--> statement-breakpoint
ALTER TABLE "tenant_bots" ALTER COLUMN "status" SET DATA TYPE "public"."bot_status_enum" USING "status"::text::"public"."bot_status_enum";--> statement-breakpoint
ALTER TABLE "tenant_bots" ALTER COLUMN "status" SET DEFAULT 'STOPPED';--> statement-breakpoint
ALTER TABLE "verification_event" ALTER COLUMN "method" SET DATA TYPE "public"."verification_method_enum" USING "method"::text::"public"."verification_method_enum";--> statement-breakpoint
ALTER TABLE "verification_event" ALTER COLUMN "status" SET DATA TYPE "public"."verification_status_enum" USING "status"::text::"public"."verification_status_enum";--> statement-breakpoint
ALTER TABLE "user_bot_roles" ADD CONSTRAINT "pk_user_bot_role" PRIMARY KEY("telegram_user_id","bot_id","role_id");--> statement-breakpoint
ALTER TABLE "chatbot_session" ADD COLUMN "accuracy" double precision;--> statement-breakpoint
ALTER TABLE "geodata_entries" ADD COLUMN "address_id" uuid;--> statement-breakpoint
ALTER TABLE "messages" ADD COLUMN "accuracy" double precision;--> statement-breakpoint
ALTER TABLE "messages" ADD COLUMN "sms_status" "sms_status_enum";--> statement-breakpoint
ALTER TABLE "messages" ADD COLUMN "related_pbx_call_id" uuid;--> statement-breakpoint
ALTER TABLE "pbx_call" ADD COLUMN "answered_at" timestamp with time zone;--> statement-breakpoint
ALTER TABLE "pbx_call" ADD COLUMN "assigned_at" timestamp with time zone;--> statement-breakpoint
ALTER TABLE "pbx_call" ADD COLUMN "external_pbx_id" varchar(255);--> statement-breakpoint
ALTER TABLE "pbx_call" ADD COLUMN "from_phone_number" varchar(50);--> statement-breakpoint
ALTER TABLE "pbx_call" ADD COLUMN "to_phone_number" varchar(50);--> statement-breakpoint
ALTER TABLE "pbx_call" ADD COLUMN "caller_name" varchar(255);--> statement-breakpoint
ALTER TABLE "pbx_call" ADD COLUMN "numeric_extension" integer;--> statement-breakpoint
ALTER TABLE "pbx_call" ADD COLUMN "termination_reason" varchar(100);--> statement-breakpoint
ALTER TABLE "pbx_call" ADD COLUMN "disposition_code" varchar(100);--> statement-breakpoint
ALTER TABLE "pbx_call" ADD COLUMN "pbx_raw_details" jsonb;--> statement-breakpoint
ALTER TABLE "pbx_call" ADD COLUMN "source" "call_source_enum";--> statement-breakpoint
ALTER TABLE "pbx_call" ADD COLUMN "customer_phone_number" varchar(50);--> statement-breakpoint
ALTER TABLE "pbx_call" ADD COLUMN "location_text" text;--> statement-breakpoint
ALTER TABLE "pbx_call" ADD COLUMN "destination_text" text;--> statement-breakpoint
ALTER TABLE "pbx_call" ADD COLUMN "area_id" uuid;--> statement-breakpoint
ALTER TABLE "pbx_call" ADD COLUMN "area_name" varchar(255);--> statement-breakpoint
ALTER TABLE "pbx_call" ADD COLUMN "eta_minutes" integer;--> statement-breakpoint
ALTER TABLE "pbx_call" ADD COLUMN "gis_data" jsonb;--> statement-breakpoint
ALTER TABLE "pbx_call" ADD COLUMN "vehicle_identifiers" json;--> statement-breakpoint
ALTER TABLE "pbx_call" ADD COLUMN "comment" text;--> statement-breakpoint
ALTER TABLE "pbx_call" ADD COLUMN "is_locked" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "pbx_call" ADD COLUMN "language_code" varchar(10);--> statement-breakpoint
ALTER TABLE "pbx_call" ADD COLUMN "call_metadata" jsonb;--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "current_location" geometry(point);--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "location_accuracy" double precision;--> statement-breakpoint
ALTER TABLE "users" ADD COLUMN "location_updated_at" timestamp;--> statement-breakpoint
ALTER TABLE "vehicle" ADD COLUMN "fuel_type" "fuel_type_enum";--> statement-breakpoint
ALTER TABLE "vehicle" ADD COLUMN "purchase_date" date;--> statement-breakpoint
ALTER TABLE "vehicle" ADD COLUMN "purchase_price" numeric(12, 2);--> statement-breakpoint
ALTER TABLE "vehicle" ADD COLUMN "sold_date" date;--> statement-breakpoint
ALTER TABLE "vehicle" ADD COLUMN "sold_price" numeric(12, 2);--> statement-breakpoint
ALTER TABLE "addresses" ADD CONSTRAINT "addresses_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "operator_performance_stats" ADD CONSTRAINT "operator_performance_stats_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "operator_performance_stats" ADD CONSTRAINT "operator_performance_stats_operator_user_id_users_id_fk" FOREIGN KEY ("operator_user_id") REFERENCES "public"."users"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pbx_call_additional_operators" ADD CONSTRAINT "pbx_call_additional_operators_pbx_call_id_pbx_call_id_fk" FOREIGN KEY ("pbx_call_id") REFERENCES "public"."pbx_call"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pbx_call_additional_operators" ADD CONSTRAINT "pbx_call_additional_operators_operator_id_operators_id_fk" FOREIGN KEY ("operator_id") REFERENCES "public"."operators"("id") ON DELETE restrict ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "scheduled_calls" ADD CONSTRAINT "scheduled_calls_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "scheduled_calls" ADD CONSTRAINT "scheduled_calls_created_by_operator_id_users_id_fk" FOREIGN KEY ("created_by_operator_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "scheduled_calls" ADD CONSTRAINT "scheduled_calls_customer_user_id_users_id_fk" FOREIGN KEY ("customer_user_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sms_log" ADD CONSTRAINT "sms_log_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sms_log" ADD CONSTRAINT "sms_log_operator_user_id_users_id_fk" FOREIGN KEY ("operator_user_id") REFERENCES "public"."users"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "sms_log" ADD CONSTRAINT "sms_log_related_pbx_call_id_pbx_call_id_fk" FOREIGN KEY ("related_pbx_call_id") REFERENCES "public"."pbx_call"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_expense" ADD CONSTRAINT "vehicle_expense_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_expense" ADD CONSTRAINT "vehicle_expense_vehicle_id_vehicle_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicle"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_note" ADD CONSTRAINT "vehicle_note_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_note" ADD CONSTRAINT "vehicle_note_vehicle_id_vehicle_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicle"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_odometer_log" ADD CONSTRAINT "vehicle_odometer_log_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_odometer_log" ADD CONSTRAINT "vehicle_odometer_log_vehicle_id_vehicle_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicle"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_parts_log" ADD CONSTRAINT "vehicle_parts_log_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_parts_log" ADD CONSTRAINT "vehicle_parts_log_vehicle_id_vehicle_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicle"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_reminder" ADD CONSTRAINT "vehicle_reminder_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_reminder" ADD CONSTRAINT "vehicle_reminder_vehicle_id_vehicle_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicle"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_repair" ADD CONSTRAINT "vehicle_repair_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_repair" ADD CONSTRAINT "vehicle_repair_vehicle_id_vehicle_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicle"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_service_record" ADD CONSTRAINT "vehicle_service_record_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_service_record" ADD CONSTRAINT "vehicle_service_record_vehicle_id_vehicle_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicle"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_upgrade" ADD CONSTRAINT "vehicle_upgrade_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "vehicle_upgrade" ADD CONSTRAINT "vehicle_upgrade_vehicle_id_vehicle_id_fk" FOREIGN KEY ("vehicle_id") REFERENCES "public"."vehicle"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "addresses_tenant_id_idx" ON "addresses" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "addresses_postal_code_country_idx" ON "addresses" USING btree ("postal_code","country_code");--> statement-breakpoint
CREATE INDEX "addresses_geom_idx" ON "addresses" USING gist ("geom") WHERE "addresses"."geom" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "operator_performance_stats_tenant_id_idx" ON "operator_performance_stats" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "operator_performance_stats_operator_user_id_idx" ON "operator_performance_stats" USING btree ("operator_user_id");--> statement-breakpoint
CREATE INDEX "operator_performance_stats_date_idx" ON "operator_performance_stats" USING btree ("date");--> statement-breakpoint
CREATE INDEX "operator_performance_stats_shift_id_idx" ON "operator_performance_stats" USING btree ("shift_id") WHERE "operator_performance_stats"."shift_id" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "operator_performance_stats_tenant_operator_date_idx" ON "operator_performance_stats" USING btree ("tenant_id","operator_user_id","date");--> statement-breakpoint
CREATE INDEX "scheduled_calls_tenant_id_idx" ON "scheduled_calls" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "scheduled_calls_created_by_operator_id_idx" ON "scheduled_calls" USING btree ("created_by_operator_id");--> statement-breakpoint
CREATE INDEX "scheduled_calls_customer_user_id_idx" ON "scheduled_calls" USING btree ("customer_user_id");--> statement-breakpoint
CREATE INDEX "scheduled_calls_phone_number_idx" ON "scheduled_calls" USING btree ("phone_number");--> statement-breakpoint
CREATE INDEX "scheduled_calls_next_activation_timestamp_idx" ON "scheduled_calls" USING btree ("next_activation_timestamp");--> statement-breakpoint
CREATE INDEX "scheduled_calls_is_active_idx" ON "scheduled_calls" USING btree ("is_active");--> statement-breakpoint
CREATE INDEX "scheduled_calls_status_idx" ON "scheduled_calls" USING btree ("status");--> statement-breakpoint
CREATE INDEX "sms_log_tenant_id_idx" ON "sms_log" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "sms_log_direction_idx" ON "sms_log" USING btree ("direction");--> statement-breakpoint
CREATE INDEX "sms_log_from_number_idx" ON "sms_log" USING btree ("from_number");--> statement-breakpoint
CREATE INDEX "sms_log_to_number_idx" ON "sms_log" USING btree ("to_number");--> statement-breakpoint
CREATE INDEX "sms_log_status_idx" ON "sms_log" USING btree ("status") WHERE "sms_log"."status" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "sms_log_operator_user_id_idx" ON "sms_log" USING btree ("operator_user_id") WHERE "sms_log"."operator_user_id" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "sms_log_related_pbx_call_id_idx" ON "sms_log" USING btree ("related_pbx_call_id") WHERE "sms_log"."related_pbx_call_id" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "sms_log_logged_at_idx" ON "sms_log" USING btree ("logged_at");--> statement-breakpoint
CREATE INDEX "vehicle_expense_tenant_id_idx" ON "vehicle_expense" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "vehicle_expense_vehicle_id_idx" ON "vehicle_expense" USING btree ("vehicle_id");--> statement-breakpoint
CREATE INDEX "vehicle_expense_type_idx" ON "vehicle_expense" USING btree ("expense_type");--> statement-breakpoint
CREATE INDEX "vehicle_expense_date_idx" ON "vehicle_expense" USING btree ("date");--> statement-breakpoint
CREATE INDEX "vehicle_note_tenant_id_idx" ON "vehicle_note" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "vehicle_note_vehicle_id_idx" ON "vehicle_note" USING btree ("vehicle_id");--> statement-breakpoint
CREATE INDEX "vehicle_note_created_at_idx" ON "vehicle_note" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "vehicle_odometer_log_tenant_id_idx" ON "vehicle_odometer_log" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "vehicle_odometer_log_vehicle_id_idx" ON "vehicle_odometer_log" USING btree ("vehicle_id");--> statement-breakpoint
CREATE INDEX "vehicle_odometer_log_date_idx" ON "vehicle_odometer_log" USING btree ("date");--> statement-breakpoint
CREATE INDEX "vehicle_parts_log_tenant_id_idx" ON "vehicle_parts_log" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "vehicle_parts_log_vehicle_id_idx" ON "vehicle_parts_log" USING btree ("vehicle_id");--> statement-breakpoint
CREATE INDEX "vehicle_parts_log_date_idx" ON "vehicle_parts_log" USING btree ("date");--> statement-breakpoint
CREATE INDEX "vehicle_parts_log_part_number_idx" ON "vehicle_parts_log" USING btree ("part_number");--> statement-breakpoint
CREATE INDEX "vehicle_parts_log_supplier_idx" ON "vehicle_parts_log" USING btree ("supplier");--> statement-breakpoint
CREATE INDEX "vehicle_reminder_tenant_id_idx" ON "vehicle_reminder" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "vehicle_reminder_vehicle_id_idx" ON "vehicle_reminder" USING btree ("vehicle_id");--> statement-breakpoint
CREATE INDEX "vehicle_reminder_urgency_idx" ON "vehicle_reminder" USING btree ("urgency");--> statement-breakpoint
CREATE INDEX "vehicle_reminder_trigger_date_idx" ON "vehicle_reminder" USING btree ("trigger_date");--> statement-breakpoint
CREATE INDEX "vehicle_reminder_is_dismissed_idx" ON "vehicle_reminder" USING btree ("is_dismissed");--> statement-breakpoint
CREATE INDEX "vehicle_repair_tenant_id_idx" ON "vehicle_repair" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "vehicle_repair_vehicle_id_idx" ON "vehicle_repair" USING btree ("vehicle_id");--> statement-breakpoint
CREATE INDEX "vehicle_repair_date_idx" ON "vehicle_repair" USING btree ("date");--> statement-breakpoint
CREATE INDEX "vehicle_service_record_tenant_id_idx" ON "vehicle_service_record" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "vehicle_service_record_vehicle_id_idx" ON "vehicle_service_record" USING btree ("vehicle_id");--> statement-breakpoint
CREATE INDEX "vehicle_service_record_date_idx" ON "vehicle_service_record" USING btree ("date");--> statement-breakpoint
CREATE INDEX "vehicle_upgrade_tenant_id_idx" ON "vehicle_upgrade" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "vehicle_upgrade_vehicle_id_idx" ON "vehicle_upgrade" USING btree ("vehicle_id");--> statement-breakpoint
CREATE INDEX "vehicle_upgrade_date_idx" ON "vehicle_upgrade" USING btree ("date");--> statement-breakpoint
ALTER TABLE "areas" ADD CONSTRAINT "areas_tenant_id_tenants_id_fk" FOREIGN KEY ("tenant_id") REFERENCES "public"."tenants"("id") ON DELETE cascade ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "geodata_entries" ADD CONSTRAINT "geodata_entries_address_id_addresses_id_fk" FOREIGN KEY ("address_id") REFERENCES "public"."addresses"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "messages" ADD CONSTRAINT "messages_related_pbx_call_id_pbx_call_id_fk" FOREIGN KEY ("related_pbx_call_id") REFERENCES "public"."pbx_call"("id") ON DELETE set null ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "pbx_call" ADD CONSTRAINT "pbx_call_area_id_areas_id_fk" FOREIGN KEY ("area_id") REFERENCES "public"."areas"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "areas_geom_idx" ON "areas" USING gist ("geom");--> statement-breakpoint
CREATE INDEX "geodata_entries_address_id_idx" ON "geodata_entries" USING btree ("address_id");--> statement-breakpoint
CREATE UNIQUE INDEX "map_provider_tenant_provider_idx" ON "map_provider" USING btree ("tenant_id","provider_name");--> statement-breakpoint
CREATE UNIQUE INDEX "map_provider_access_token_idx" ON "map_provider" USING btree ("access_token");--> statement-breakpoint
CREATE INDEX "messages_via_channel_idx" ON "messages" USING btree ("via_channel");--> statement-breakpoint
CREATE INDEX "messages_sms_status_idx" ON "messages" USING btree ("sms_status") WHERE "messages"."sms_status" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "messages_related_pbx_call_id_idx" ON "messages" USING btree ("related_pbx_call_id") WHERE "messages"."related_pbx_call_id" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "pbx_call_tenant_id_idx" ON "pbx_call" USING btree ("tenant_id");--> statement-breakpoint
CREATE INDEX "pbx_call_user_id_idx" ON "pbx_call" USING btree ("user_id") WHERE "pbx_call"."user_id" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "pbx_call_operator_id_idx" ON "pbx_call" USING btree ("operator_id") WHERE "pbx_call"."operator_id" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "pbx_call_ride_id_idx" ON "pbx_call" USING btree ("ride_id") WHERE "pbx_call"."ride_id" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "pbx_call_status_idx" ON "pbx_call" USING btree ("status");--> statement-breakpoint
CREATE INDEX "pbx_call_started_at_idx" ON "pbx_call" USING btree ("started_at");--> statement-breakpoint
CREATE INDEX "pbx_call_from_phone_number_idx" ON "pbx_call" USING btree ("from_phone_number") WHERE "pbx_call"."from_phone_number" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "pbx_call_to_phone_number_idx" ON "pbx_call" USING btree ("to_phone_number") WHERE "pbx_call"."to_phone_number" IS NOT NULL;--> statement-breakpoint
CREATE UNIQUE INDEX "pbx_call_tenant_external_pbx_id_uk" ON "pbx_call" USING btree ("tenant_id","external_pbx_id") WHERE "pbx_call"."external_pbx_id" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "pbx_call_source_idx" ON "pbx_call" USING btree ("source") WHERE "pbx_call"."source" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "pbx_call_assigned_at_idx" ON "pbx_call" USING btree ("assigned_at") WHERE "pbx_call"."assigned_at" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "pbx_call_language_code_idx" ON "pbx_call" USING btree ("language_code") WHERE "pbx_call"."language_code" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "pbx_call_customer_phone_idx" ON "pbx_call" USING btree ("customer_phone_number") WHERE "pbx_call"."customer_phone_number" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "pbx_call_area_id_idx" ON "pbx_call" USING btree ("area_id") WHERE "pbx_call"."area_id" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "pbx_call_numeric_extension_idx" ON "pbx_call" USING btree ("numeric_extension") WHERE "pbx_call"."numeric_extension" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "ride_event_tenant_id_idx" ON "ride_event" USING btree ("tenant_id");--> statement-breakpoint
CREATE UNIQUE INDEX "ride_event_ride_idx" ON "ride_event" USING btree ("ride_id");--> statement-breakpoint
CREATE UNIQUE INDEX "ride_event_type_idx" ON "ride_event" USING btree ("event_type");--> statement-breakpoint
CREATE UNIQUE INDEX "ride_order_tenant_idx" ON "ride_order" USING btree ("tenant_id");--> statement-breakpoint
CREATE UNIQUE INDEX "ride_order_passenger_idx" ON "ride_order" USING btree ("passenger_id");--> statement-breakpoint
CREATE UNIQUE INDEX "ride_order_driver_idx" ON "ride_order" USING btree ("driver_id");--> statement-breakpoint
CREATE UNIQUE INDEX "user_identity_provider_external_id_idx" ON "user_identity" USING btree ("provider","external_id");--> statement-breakpoint
CREATE UNIQUE INDEX "user_identity_user_id_idx" ON "user_identity" USING btree ("user_id");--> statement-breakpoint
CREATE UNIQUE INDEX "user_identity_provider_idx" ON "user_identity" USING btree ("provider");--> statement-breakpoint
CREATE INDEX "user_kyc_tenant_id_idx" ON "user_kyc" USING btree ("tenant_id");--> statement-breakpoint
CREATE UNIQUE INDEX "user_kyc_user_id_idx" ON "user_kyc" USING btree ("user_id");--> statement-breakpoint
CREATE UNIQUE INDEX "user_onboarding_user_tenant_idx" ON "user_onboarding" USING btree ("user_id","tenant_id");--> statement-breakpoint
CREATE INDEX "user_onboarding_deleted_at_idx" ON "user_onboarding" USING btree ("deleted_at");--> statement-breakpoint
CREATE UNIQUE INDEX "user_profile_history_user_id_changed_at_idx" ON "user_profile_history" USING btree ("user_id","changed_at");--> statement-breakpoint
CREATE UNIQUE INDEX "user_profile_history_deleted_at_idx" ON "user_profile_history" USING btree ("deleted_at") WHERE "user_profile_history"."deleted_at" is not null;--> statement-breakpoint
CREATE UNIQUE INDEX "user_tenant_user_id_tenant_id_idx" ON "user_tenant" USING btree ("user_id","tenant_id");--> statement-breakpoint
CREATE UNIQUE INDEX "user_tenant_invited_by_id_idx" ON "user_tenant" USING btree ("invited_by_id");--> statement-breakpoint
CREATE UNIQUE INDEX "users_phone_idx" ON "users" USING btree ("phone");--> statement-breakpoint
CREATE UNIQUE INDEX "users_email_idx" ON "users" USING btree ("email");--> statement-breakpoint
CREATE UNIQUE INDEX "users_external_id_idx" ON "users" USING btree ("external_id");--> statement-breakpoint
CREATE INDEX "users_deleted_at_idx" ON "users" USING btree ("deleted_at") WHERE "users"."deleted_at" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "users_current_location_idx" ON "users" USING gist ("current_location") WHERE "users"."current_location" IS NOT NULL;--> statement-breakpoint
CREATE UNIQUE INDEX "license_plate_tenant_id_idx" ON "vehicle" USING btree ("license_plate","tenant_id");--> statement-breakpoint
CREATE UNIQUE INDEX "wallet_user_tenant_currency_idx" ON "wallet" USING btree ("user_id","tenant_id","currency");--> statement-breakpoint
CREATE INDEX "chatbot_session_geolocation_idx" ON "chatbot_session" USING gist ("geolocation") WHERE "chatbot_session"."geolocation" IS NOT NULL;--> statement-breakpoint
CREATE INDEX "geodata_entries_geom_idx" ON "geodata_entries" USING gist ("geom") WHERE "geodata_entries"."geom" IS NOT NULL;--> statement-breakpoint
ALTER TABLE "geodata_entries" DROP COLUMN "latitude";--> statement-breakpoint
ALTER TABLE "geodata_entries" DROP COLUMN "longitude";--> statement-breakpoint
DROP TYPE "public"."promotion_type";--> statement-breakpoint
DROP TYPE "public"."status";--> statement-breakpoint
DROP TYPE "public"."verification_method";--> statement-breakpoint
DROP TYPE "public"."verification_status";