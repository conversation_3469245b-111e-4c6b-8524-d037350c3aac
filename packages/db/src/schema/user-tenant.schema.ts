import { relations } from "drizzle-orm";
import { boolean, json, pgTable, timestamp, uuid } from "drizzle-orm/pg-core";
import { uniqueIndex } from "drizzle-orm/pg-core"; // Moved uniqueIndex import
import { userStatusEnum } from "../enums/user-status.enum.js";
import { roles } from "./role.schema.js"; // Import the roles table
import { tenants } from "./tenants.schema.js";
import { users } from "./users.schema.js"; // Assuming users.schema.ts is the correct file

export const userTenants = pgTable(
	"user_tenant",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		userId: uuid("user_id")
			.references(() => users.id)
			.notNull(),
		tenantId: uuid("tenant_id")
			.references(() => tenants.id)
			.notNull(),
		roleId: uuid("role_id")
			.references(() => roles.id)
			.notNull(), // Changed from enum to FK
		status: userStatusEnum("status").notNull().default("ACTIVE"),
		isPrimary: boolean("is_primary").notNull().default(false),
		lastUsed: timestamp("last_used"),
		registeredAt: timestamp("registered_at").notNull().defaultNow(),
		invitedById: uuid("invited_by_id").references(() => users.id),
		metadata: json("metadata").notNull().default({}),
	},
	(table) => [
		uniqueIndex("user_tenant_user_id_tenant_id_idx").on(
			table.userId,
			table.tenantId,
		),
		uniqueIndex("user_tenant_invited_by_id_idx").on(table.invitedById),
	],
);

export const userTenantsRelations = relations(userTenants, ({ one }) => ({
	user: one(users, {
		fields: [userTenants.userId],
		references: [users.id],
	}),
	tenant: one(tenants, {
		fields: [userTenants.tenantId],
		references: [tenants.id],
	}),
	role: one(roles, {
		// Added relation to roles table
		fields: [userTenants.roleId],
		references: [roles.id],
	}),
	inviter: one(users, {
		fields: [userTenants.invitedById],
		references: [users.id],
	}),
}));
