{"service": {"initialized": "CallbackService initialized", "initiating": "Initiating callback for number: {{number}}", "invalid_number": "Invalid phone number format: {{number}}. Expected format: {{format}}", "published": "Callback initiation message published for number: {{number}}", "success": "Success", "publish_failed": "Failed to publish call initiation message: {{error}}"}, "errors": {"invalid_phone_number": "Invalid phone number format", "publish_failed": "Failed to publish call initiation message: {{error}}"}}