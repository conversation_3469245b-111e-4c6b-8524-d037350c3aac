import type {
	AppBot,
	AppChosenInlineResultContext,
} from "@/types/bot-context.js";
import { Injectable, Logger } from "@nestjs/common";

/**
 * Injectable DI handler service for GramIO "chosen_inline_result" events.
 *
 * - Registers its handler on `registerHandlers()`, for lifecycle-friendly DI.
 * - All handler logic and processing/response in strict, typed methods.
 * - Localized responses use `.t()` on context.
 * - Structured logging with NestJS Logger.
 *
 * @see AppBotContext for mandatory shape of injected GramIO context.
 */
@Injectable()
export class ChosenInlineResultHandlersService {
	private readonly logger = new Logger(ChosenInlineResultHandlersService.name);

	private botInstance!: AppBot;

	public initialize(bot: AppBot): void {
		this.botInstance = bot;
	}

	/**
	 * Registers the chosen inline result update handler.
	 * Should be called in a module's `onModuleInit` or DI startup lifecycle.
	 */
	registerHandlers(): void {
		this.botInstance.on("chosen_inline_result", (ctx: any, next: any) =>
			this.handleChosenInlineResult(ctx as AppChosenInlineResultContext, next),
		);
	}

	/**
	 * Handles a "chosen_inline_result" update. All processing and reply logic for this event type lives here.
	 *
	 * @param context  {AppChosenInlineResultContext} - strictly typed GramIO context with `.t()` for localization.
	 * @param next     {() => Promise<void>} - middleware continuation function
	 */
	async handleChosenInlineResult(
		context: AppChosenInlineResultContext,
		next: () => Promise<void>,
	): Promise<void> {
		const chosen = context.update?.chosen_inline_result;
		const userId = chosen?.from?.id ?? undefined;
		const userLang = chosen?.from?.language_code ?? "unknown";
		const tenantId = context.tenantId;
		const resultId = chosen?.result_id ?? undefined;

		this.logger.log({
			event: "chosen_inline_result",
			userId,
			tenantId,
			resultId,
			inlineResult: chosen,
		});

		// Demo: issue localized log using an existing i18n key.
		// Always use canonical param-object t(key, params) pattern
		const greetingText = `${context.t?.("greeting", chosen?.from?.first_name ?? "user") ?? "Hello"}, ${chosen?.from?.first_name ?? "user"}`;
		this.logger.debug(`[i18n:${userLang}] ${String(greetingText)}`);

		// Note: "chosen_inline_result" context usually does not support replying directly.
		// Add further business logic as needed.

		// Call next to continue middleware chain
		await next();
	}
}
