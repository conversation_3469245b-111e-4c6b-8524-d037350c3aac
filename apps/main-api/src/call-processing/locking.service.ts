import { Inject, Injectable, Logger } from "@nestjs/common";
import type { IRedisService } from "@/redis/redis.interface";
import { RedisServiceToken } from "@/redis/redis.token";

@Injectable()
export class LockingService {
	private readonly logger = new Logger(LockingService.name);
	private readonly LOCK_TTL_SECONDS = 300; // 5 minutes, adjust as needed

	constructor(
		@Inject(RedisServiceToken) private readonly redisService: IRedisService,
	) {}

	private getCallLockKey(callId: string): string {
		return `lock:call:${callId}`;
	}

	async lockCall(
		tenantId: string,
		callId: string,
		operatorId: string,
		ttlSeconds: number = this.LOCK_TTL_SECONDS,
	): Promise<boolean> {
		const key = this.getCallLockKey(callId);
		const currentLockHolder = await this.redisService.get(tenantId, key);

		if (currentLockHolder && currentLockHolder !== operatorId) {
			this.logger.warn(
				`Call ${callId} for tenant ${tenantId} is already locked by operator ${currentLockHolder}. Operator ${operatorId} failed to acquire lock.`,
			);
			return false; // Already locked by someone else
		}

		// Set the lock with operatorId and TTL
		// For true atomicity (set if not exists), RedisDataService.set would need an 'NX' option.
		// This current implementation will overwrite if currentLockHolder === operatorId or if it's null.
		await this.redisService.set(tenantId, key, operatorId, ttlSeconds);
		this.logger.log(
			`Call ${callId} for tenant ${tenantId} locked by operator ${operatorId} for ${ttlSeconds}s.`,
		);
		return true;
	}

	async unlockCall(tenantId: string, callId: string, operatorId: string): Promise<boolean> {
		const key = this.getCallLockKey(callId);
		const currentLockHolder = await this.redisService.get(tenantId, key);

		if (currentLockHolder && currentLockHolder !== operatorId) {
			this.logger.warn(
				`Operator ${operatorId} attempted to unlock call ${callId} (tenant ${tenantId}) but it's locked by ${currentLockHolder}.`,
			);
			return false; // Locked by someone else, cannot unlock
		}

		await this.redisService.del(tenantId, key);
		this.logger.log(`Call ${callId} for tenant ${tenantId} unlocked by operator ${operatorId}.`);
		return true;
	}

	async getCallLockHolder(tenantId: string, callId: string): Promise<string | null> {
		const key = this.getCallLockKey(callId);
		return this.redisService.get(tenantId, key);
	}
}