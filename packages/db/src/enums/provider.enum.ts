import { pgEnum } from "drizzle-orm/pg-core";

// 1. Define enum values as the single source of truth
const PROVIDER_VALUES = [
	"TELEGRAM",
	"VIBER",
	"FACEBOOK",
	"GOOGLE",
	"APPLE",
	"PHONE",
] as const;

// 2. Create the Drizzle ORM pgEnum for database schema definition
export const providerEnum = pgEnum("provider", PROVIDER_VALUES);

// 3. Define TypeScript union type for strict type safety
export type ProviderType = (typeof PROVIDER_VALUES)[number];

// 4. Define TypeScript string enum for application logic
export enum Provider {
	TELEGRAM = "TELEGRAM",
	VIBER = "VIBER",
	FACEBOOK = "FACEBOOK",
	GOOGLE = "GOOGLE",
	APPLE = "APPLE",
	PHONE = "PHONE",
}
