{"help": {"welcome": "Welcome to the Help Scene! You have visited this scene {{visits}} times. Bot: {{botName}}, Tenant: {{tenantId}}, Session ID: {{sessionId}}", "commands": "Commands", "about": "About", "exit": "Exit", "commands_list": "Available commands:\n/start - Start the bot\n/help - Show this help message\n/language - Change language\n/cancel - Cancel current operation", "about_text": "This is a multi-tenant Telegram bot example that demonstrates how to use session data, tenant ID, and bot name injection.", "unknown_command": "I don't understand that command. Please use the buttons below.", "goodbye": "Thank you for using the help system! Bot: {{botName}}, Tenant: {{tenantId}}"}, "registration": {"ask_name": "Please enter your name:", "ask_email": "Please enter your email address:", "ask_age": "Please enter your age:", "invalid_email": "That doesn't look like a valid email address. Please try again.", "invalid_age": "Please enter a valid age between 1 and 120.", "completed": "Registration completed successfully!\n\nName: {{name}}\nEmail: {{email}}\nAge: {{age}}"}}