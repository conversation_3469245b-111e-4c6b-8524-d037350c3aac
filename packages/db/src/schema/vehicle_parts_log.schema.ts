import { relations } from "drizzle-orm";
import {
	date,
	index,
	integer,
	numeric,
	pgTable,
	text,
	timestamp,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { tenants } from "./tenants.schema.js";
import { vehicles } from "./vehicle.schema.js";

export const vehiclePartsLogs = pgTable(
	"vehicle_parts_log",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id")
			.references(() => tenants.id, { onDelete: "cascade" })
			.notNull(),
		vehicleId: uuid("vehicle_id")
			.references(() => vehicles.id, { onDelete: "cascade" })
			.notNull(),
		date: date("date").notNull(),
		partNumber: varchar("part_number", { length: 100 }),
		supplier: varchar("supplier", { length: 255 }),
		description: text("description").notNull(),
		quantity: integer("quantity").default(1).notNull(),
		costPerUnit: numeric("cost_per_unit", { precision: 10, scale: 2 }),
		totalCost: numeric("total_cost", { precision: 12, scale: 2 }), // Can be calculated: quantity * costPerUnit
		notes: text("notes"),
		createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
	},
	(table) => [
		index("vehicle_parts_log_tenant_id_idx").on(table.tenantId),
		index("vehicle_parts_log_vehicle_id_idx").on(table.vehicleId),
		index("vehicle_parts_log_date_idx").on(table.date),
		index("vehicle_parts_log_part_number_idx").on(table.partNumber),
		index("vehicle_parts_log_supplier_idx").on(table.supplier),
	],
);

export const vehiclePartsLogsRelations = relations(vehiclePartsLogs, ({ one }) => ({
	tenant: one(tenants, { fields: [vehiclePartsLogs.tenantId], references: [tenants.id] }),
	vehicle: one(vehicles, { fields: [vehiclePartsLogs.vehicleId], references: [vehicles.id] }),
}));

export type VehiclePartsLog = typeof vehiclePartsLogs.$inferSelect;
export type NewVehiclePartsLog = typeof vehiclePartsLogs.$inferInsert;