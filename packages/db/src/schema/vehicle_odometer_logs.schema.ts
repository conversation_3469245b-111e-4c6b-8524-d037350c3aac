import { relations } from "drizzle-orm";
import {
	date,
	index,
	integer,
	pgTable,
	text,
	timestamp,
	uuid,
} from "drizzle-orm/pg-core";
import { tenants } from "./tenants.schema.js";
import { vehicles } from "./vehicle.schema.js";

export const vehicleOdometerLogs = pgTable(
	"vehicle_odometer_log",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id")
			.references(() => tenants.id, { onDelete: "cascade" })
			.notNull(),
		vehicleId: uuid("vehicle_id")
			.references(() => vehicles.id, { onDelete: "cascade" })
			.notNull(),
		date: date("date").notNull(),
		initialValue: integer("initial_value"), // Odometer reading at the start of this log period or purchase
		odometerValue: integer("odometer_value").notNull(), // Current odometer reading for this log entry
		// distance can be calculated: odometerValue - initialValue (or previous log's odometerValue)
		notes: text("notes"),
		createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
	},
	(table) => [
		index("vehicle_odometer_log_tenant_id_idx").on(table.tenantId),
		index("vehicle_odometer_log_vehicle_id_idx").on(table.vehicleId),
		index("vehicle_odometer_log_date_idx").on(table.date),
	],
);

export const vehicleOdometerLogsRelations = relations(vehicleOdometerLogs, ({ one }) => ({
	tenant: one(tenants, { fields: [vehicleOdometerLogs.tenantId], references: [tenants.id] }),
	vehicle: one(vehicles, { fields: [vehicleOdometerLogs.vehicleId], references: [vehicles.id] }),
}));

export type VehicleOdometerLog = typeof vehicleOdometerLogs.$inferSelect;
export type NewVehicleOdometerLog = typeof vehicleOdometerLogs.$inferInsert;