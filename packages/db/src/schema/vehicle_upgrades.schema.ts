import { relations } from "drizzle-orm";
import {
	date,
	index,
	integer,
	numeric,
	pgTable,
	text,
	timestamp,
	uuid,
} from "drizzle-orm/pg-core";
import { tenants } from "./tenants.schema.js";
import { vehicles } from "./vehicle.schema.js";

export const vehicleUpgrades = pgTable(
	"vehicle_upgrade",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id")
			.references(() => tenants.id, { onDelete: "cascade" })
			.notNull(),
		vehicleId: uuid("vehicle_id")
			.references(() => vehicles.id, { onDelete: "cascade" })
			.notNull(),
		date: date("date").notNull(),
		odometer: integer("odometer"),
		description: text("description").notNull(),
		cost: numeric("cost", { precision: 10, scale: 2 }),
		notes: text("notes"),
		createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
	},
	(table) => [
		index("vehicle_upgrade_tenant_id_idx").on(table.tenantId),
		index("vehicle_upgrade_vehicle_id_idx").on(table.vehicleId),
		index("vehicle_upgrade_date_idx").on(table.date),
	],
);

export const vehicleUpgradesRelations = relations(vehicleUpgrades, ({ one }) => ({
	tenant: one(tenants, { fields: [vehicleUpgrades.tenantId], references: [tenants.id] }),
	vehicle: one(vehicles, { fields: [vehicleUpgrades.vehicleId], references: [vehicles.id] }),
}));

export type VehicleUpgrade = typeof vehicleUpgrades.$inferSelect;
export type NewVehicleUpgrade = typeof vehicleUpgrades.$inferInsert;