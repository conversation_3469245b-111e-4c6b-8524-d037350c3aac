# WebSocket Protocol

This document outlines the WebSocket events used for real-time communication between the client and server.

**Base URL:** `ws://<host>:<port>/?v=<client_version>[&operator_id=<operatorId>]`

## General Event Structure

All messages are JSON strings with the following base structure:

```json
{
  "event": "event_name_string",
  "data": {
    // Payload specific to the event
  }
}
```

---

## Client → Server Events

### `client:init`

- **Direction:** Implicitly triggered by client on WebSocket connection.
- **Description:** Client signals its readiness and provides initial parameters. The server responds with its own `client:init` containing configurations.
- **Payload (`data`):**

  ```json
  // Client sends options in URL query params (e.g., operator_id, v (version), from (timestamp for initial call load))
  // No explicit JSON payload from client for this event name.
  ```

### `client:settings`

- **Direction:** Client → Server
- **Description:** Client informs the server about its current settings.
- **Payload (`data`):**

  ```json
  {
    "lang": "string (e.g., 'en', 'mk')",
    "resolution": {
      "width": "number",
      "height": "number"
    }
  }
  ```

### `call:add`

- **Direction:** Client → Server
- **Description:** Operator manually adds a new call.
- **Payload (`data`):**

  ```json
  {
    "extension": "number (optional)",
    "phone": "string",
    "location": "string (optional)",
    "area": "string (optional)",
    "timestamp": "number (epoch ms, optional)",
    "timeanswered": "number (epoch ms, optional)",
    "comment": "string (optional)",
    "source": "string (e.g., 'manual', 'multiple')",
    "destination": "string (optional)",
    "vehicles": "Array<string> (optional)",
    "arrivaltime": "number (minutes, optional)",
    "gisdata": "object (optional)"
  }
  ```

### `call:update`

- **Direction:** Client → Server
- **Description:** Operator updates an existing call. Requires call to be locked.
- **Payload (`data`):** (Call object with `id` and fields to update)

  ```json
  {
    "id": "string (uuid)",
    "location": "string (updated)",
    // ... other fields to update
  }
  ```

### `call:remove`

- **Direction:** Client → Server
- **Description:** Operator removes a call. Requires call to be locked.
- **Payload (`data`):**

  ```json
  {
    "id": "string (uuid)",
    "source": "string (must be 'multiple', 'manual', or 'schedule')"
  }
  ```

### `call:cancel`

- **Direction:** Client → Server
- **Description:** Operator cancels a vehicle assignment for a call. Requires call to be locked.
- **Payload (`data`):** (Call object with `id`)

  ```json
  {
    "id": "string (uuid)",
    "vehicles": "Array<string> (vehicles that were assigned)"
    // ... other relevant call fields
  }
  ```

### `call:edit` (Lock Request)

- **Direction:** Client → Server
- **Description:** Operator requests to lock a call for editing.
- **Payload (`data`):**

  ```json
  "string (call_id)"
  ```

### `call:unlocked` (Unlock Request)

- **Direction:** Client → Server
- **Description:** Operator releases the lock on a call.
- **Payload (`data`):**

  ```json
  "string (call_id)"
  ```

### `call:new` (Dial)

- **Direction:** Client → Server
- **Description:** Operator initiates an outgoing call.
- **Payload (`data`):**

  ```json
  {
    "phone": "string (customer's number)",
    "localNumber": "number (operator's extension/line, optional)",
    "id": "string (call_id if related to an existing call, optional)"
  }
  ```

### `call:answer`

- **Direction:** Client → Server
- **Description:** Operator answers a call (e.g., from a Texy notification).
- **Payload (`data`):** (Call object from notification)

  ```json
  {
    "phone": "string",
    "source": "string (e.g., 'texy')",
    // ... other data from notification
  }
  ```

### `call:list` (Search)

- **Direction:** Client → Server
- **Description:** Operator requests a list of calls based on search criteria.
- **Payload (`data`):**

  ```json
  {
    "count": "number",
    "sort": "string ('asc' or 'desc')",
    "source": "string (optional)",
    // ... other search filter fields (see Search Calls feature)
  }
  ```

### `schedule:add`

- **Direction:** Client → Server
- **Description:** Operator schedules a new call.
- **Payload (`data`):** (Call object with scheduling details)

  ```json
  {
    "phone": "string",
    "location": "string (optional)",
    "timestamp": "number (epoch ms)",
    "arrivaltime": "number (minutes)",
    "repeat": "Array<string|0> (optional, e.g., [0, \"08:00\", 0, \"08:00\", 0, 0, 0])"
    // ... other call fields
  }
  ```

### `schedule:update`

- **Direction:** Client → Server
- **Description:** Operator updates a scheduled call. Requires call to be locked.
- **Payload (`data`):** (Call object with `id` and fields to update)

  ```json
  {
    "id": "string (uuid)",
    "timestamp": "number (updated epoch ms)"
    // ... other fields to update
  }
  ```

### `schedule:remove`

- **Direction:** Client → Server
- **Description:** Operator removes a scheduled call.
- **Payload (`data`):**

  ```json
  {
    "id": "string (uuid)"
  }
  ```

### `schedule:cancel` (Activate Now)

- **Direction:** Client → Server
- **Description:** Operator cancels a future schedule, converting it to an immediate call.
- **Payload (`data`):** (Call object of the scheduled call)

  ```json
  {
    "id": "string (uuid)"
    // ... other fields of the scheduled call
  }
  ```

### `contact:info` (Request)

- **Direction:** Client → Server
- **Description:** Operator requests detailed information for a contact.
- **Payload (`data`):**

  ```json
  "string (phone number)"
  ```

### `contact:update`

- **Direction:** Client → Server
- **Description:** Operator updates contact information (e.g., sets a name).
- **Payload (`data`):**

  ```json
  {
    "<phone_number>": {
      "name": "string"
    }
  }
  ```

### `message:new` (Send SMS)

- **Direction:** Client → Server
- **Description:** Operator sends an SMS message.
- **Payload (`data`):**

  ```json
  {
    "temp_id": "string (client-generated temporary ID)",
    "type": "string ('sms')",
    "msg": "string (SMS content)",
    "phone": "string (recipient phone number)",
    "call_id": "string (optional, links SMS to a call)"
  }
  ```

### `stats:daily`

- **Direction:** Client → Server
- **Description:** Operator requests daily aggregated statistics.
- **Payload (`data`):**

  ```json
  {
    "from": { "year": "number", "month": "number", "date": "number" },
    "to": { "year": "number", "month": "number", "date": "number" }
  }
  ```

### `stats:detailed`

- **Direction:** Client → Server
- **Description:** Operator requests detailed statistics for a day, broken down by operator.
- **Payload (`data`):**

  ```json
  {
    "year": "number",
    "month": "number",
    "date": "number"
  }
  ```

### `ping`

- **Direction:** Client → Server
- **Description:** Keep-alive mechanism.
- **Payload (`data`):**

  ```json
  "string (timestamp_string)"
  ```

### `client:error`

- **Direction:** Client → Server
- **Description:** Client reports an internal error to the server.
- **Payload (`data`):**

  ```json
  {
    "info": "string (error context)",
    "msg": "string (error message)",
    "data": "any (problematic data, optional)"
  }
  ```

---

## Server → Client Events

### `client:init`

- **Direction:** Server → Client (Unicast to connecting client)
- **Description:** Server sends initial configuration and settings upon successful connection.
- **Payload (`data`):**

  ```json
  {
    "apps": "Array<string>",
    "columns": "Array<string>",
    "localNumber": "number (optional)",
    "mapConfig": "object",
    "messageTemplates": "object",
    "modules": "Array<string>",
    "scheduledNotification": "boolean",
    "sendSms": "boolean",
    "serverTimestamp": "number (epoch ms)",
    "title": "string",
    "smsRegex": "string (regex pattern)",
    "numRegex": "string (regex pattern)",
    "vehicles": "Array<number>",
    "aheadNotification": "number (minutes)",
    "locales": "Array<string>",
    "defaultLocale": "string",
    "operators": "Array<object {id, name, disabled}>",
    "disableAutocomplete": "boolean",
    "areas": "Array<any>",
    "preventDoubleBooking": "boolean",
    "contactDefault": "string ('name' or 'phone')",
    "serverOffset": "number (ms difference between client and server time)"
  }
  ```

### `call:init`

- **Direction:** Server → Client (Unicast to connecting client)
- **Description:** Server sends the initial list of live calls.
- **Payload (`data`):** (Array of Call objects - see `call:add` Client->Server for structure)

### `call:add`

- **Direction:** Server → Clients (Broadcast)
- **Description:** Notifies clients of a newly added call.
- **Payload (`data`):** (Full Call object)

### `call:update`

- **Direction:** Server → Clients (Broadcast)
- **Description:** Notifies clients of an updated call.
- **Payload (`data`):** (Full updated Call object)

### `call:remove`

- **Direction:** Server → Clients (Broadcast)
- **Description:** Notifies clients that a call has been removed.
- **Payload (`data`):**

  ```json
  "string (id of the removed call)"
  ```

### `call:locked`

- **Direction:** Server → Clients (Broadcast, excluding locker)
- **Description:** Notifies other clients that a call has been locked for editing.
- **Payload (`data`):**

  ```json
  "string (call_id)"
  ```

### `call:unlocked`

- **Direction:** Server → Clients (Broadcast, excluding unlocker)
- **Description:** Notifies other clients that a call's lock has been released.
- **Payload (`data`):**

  ```json
  "string (call_id)"
  ```

### `call:edit` (Lock Acknowledgment)

- **Direction:** Server → Client (Unicast to locker)
- **Description:** Acknowledges to the requesting operator that the call is now locked by them.
- **Payload (`data`):**

  ```json
  "string (call_id)"
  ```

### `call:metadata`

- **Direction:** Server → Clients (Broadcast)
- **Description:** Sends updates to a call's metadata (e.g., call duration from telephony events).
- **Payload (`data`):**

  ```json
  {
    "id": "string (call_id)",
    "data": {
      "duration": "number (seconds, 0 for active, <0 for hold, >0 for finished)"
      // ... other metadata fields
    }
  }
  ```

### `call:list` (Search Results)

- **Direction:** Server → Client (Unicast to searcher)
- **Description:** Sends search results to the client that initiated the search.
- **Payload (`data`):** (Array of Call objects)

### `schedule:init`

- **Direction:** Server → Client (Unicast to connecting client)
- **Description:** Server sends the initial list of all scheduled calls.
- **Payload (`data`):** (Array of scheduled Call objects)

### `schedule:active`

- **Direction:** Server → Client (Unicast to connecting client)
- **Description:** Server sends scheduled calls that are currently active or due soon.
- **Payload (`data`):** (Array of active scheduled Call objects)

### `schedule:add`

- **Direction:** Server → Clients (Broadcast)
- **Description:** Notifies clients of a newly scheduled call.
- **Payload (`data`):** (Full scheduled Call object)

### `schedule:update`

- **Direction:** Server → Clients (Broadcast)
- **Description:** Notifies clients of an updated scheduled call.
- **Payload (`data`):** (Full updated scheduled Call object)

### `schedule:remove`

- **Direction:** Server → Clients (Broadcast)
- **Description:** Notifies clients that a scheduled call has been removed.
- **Payload (`data`):**

  ```json
  "string (id of the removed scheduled call)"
  ```

### `contact:info` (Response)

- **Direction:** Server → Client (Unicast to requester)
- **Description:** Sends detailed contact information to the client that requested it.
- **Payload (`data`):**

  ```json
  {
    "<phone_number>": {
      "name": "string (optional)",
      "served": "number (optional)",
      // ... other contact fields (see Contact Management - Get Contact Info feature)
    }
  }
  ```

### `contact:update`

- **Direction:** Server → Clients (Broadcast)
- **Description:** Notifies clients of updated contact information.
- **Payload (`data`):**

  ```json
  {
    "<phone_number>": {
      "name": "string"
      // ... other updated contact fields
    }
  }
  ```

### `message:new` (Incoming/System/Texy)

- **Direction:** Server → Clients (Broadcast)
- **Description:** Server pushes various types of messages/notifications to clients.
- **Payload (`data`):** (Structure varies by message type - see Messaging - Receive Incoming Messages feature)

  ```json
  // Example for SMS
  {
    "id": "string",
    "type": "sms",
    "timestamp": "number",
    "phone": "string",
    "autohide": "number",
    "msg": "string",
    "userInfo": "object (optional)",
    "callInfo": "object (optional)"
  }
  // Example for System message
  {
    "operator_id": "number (optional)",
    "type": "system",
    "timestamp": "number",
    "from": "string",
    "autohide": "number",
    "msg": "Array<[string, object]>"
  }
  ```

### `message:sent`

- **Direction:** Server → Client (Unicast to SMS sender)
- **Description:** Confirms to the sender that their SMS was successfully processed by the server (not necessarily delivered).
- **Payload (`data`):**

  ```json
  "string (temp_id of the sent message)"
  ```

### `stats:daily`

- **Direction:** Server → Client (Unicast to requester)
- **Description:** Sends daily aggregated statistics.
- **Payload (`data`):**

  ```json
  {
    "YYYY-MM-DD": {
      "answered": "number",
      // ... other daily stats (see Statistics - Fetch Daily Statistics feature)
    }
  }
  ```

### `stats:detailed`

- **Direction:** Server → Client (Unicast to requester)
- **Description:** Sends detailed statistics for a day, broken down by operator.
- **Payload (`data`):**

  ```json
  {
    "YYYY-MM-DD": {
      "operator_id_1": { "answered": "number", /* ...other_stats */ },
      "operator_id_2": { /* ...stats */ }
    }
  }
  ```

### `ping`

- **Direction:** Server → Client
- **Description:** Keep-alive mechanism.
- **Payload (`data`):**

  ```json
  "string (timestamp_string)"
  ```

### `pong`

- **Direction:** Server → Client
- **Description:** Response to client's `ping`.
- **Payload (`data`):**

  ```json
  "string (timestamp_string, echoed from client's ping)"
  ```

### `client:reload`

- **Direction:** Server → Client (Unicast or Broadcast)
- **Description:** Instructs the client application to reload.
- **Payload (`data`):**

  ```json
  "string (new client version)"
  ```

### `server:error`

- **Direction:** Server → Client (Unicast to relevant client)
- **Description:** Server reports an operational error related to a client's request.
- **Payload (`data`):**

  ```json
  {
    "event": "string (original event name that caused error)",
    "data": "any (original data from the failed request, optional)",
    "key": "string (e.g., temp_id for messages, optional)",
    "msg": "string (error message description)"
  }
  ```

```

---
**`docs/data-models.md`**
```markdown
# Data Models & Relationships

This document outlines the core data entities and their relationships.

## Core Entities ER Diagram (Conceptual)

```mermaid
erDiagram
  OPERATORS {
    int id PK
    string name
    boolean disabled
    timestamp last_login "Nullable"
  }

  CALLS {
    uuid id PK
    timestamp timestamp "Initial event time"
    timestamp timeanswered "Nullable, when call was answered"
    timestamp timeassigned "Nullable, when vehicle was assigned/expected"
    string phone
    string location "Nullable"
    string destination "Nullable"
    string area "Nullable"
    int arrivaltime "Nullable, minutes"
    string source "e.g., incoming, manual, schedule, texy"
    int extension "Nullable"
    int[] operator_ids "Nullable, FK to OPERATORS"
    text comment "Nullable"
    jsonb gisdata "Nullable, {location: {lat, lon, address}, destination: {lat, lon, address}}"
    text[] vehicles "Nullable, vehicle numbers"
    jsonb metadata "Nullable, e.g., {locked, sending, duration, served, canceled, lang}"
  }

  CONTACTS_CACHE_REDIS {
    string phone PK "Key: contacts:<phone>"
    string name "Nullable"
    int served_count "Nullable"
    int canceled_count "Nullable"
    timestamp firstcontact "Nullable"
    string lang "Nullable"
    string last_assigned_call_details "JSON string, Nullable, Key: contacts:<phone>:assigned"
    zset common_locations "Key: contacts:<phone>:location"
    zset common_destinations "Key: contacts:<phone>:destination"
  }

  SCHEDULED_CALLS_REDIS {
    string id PK "Key: schedule:<id>"
    timestamp timestamp "Scheduled time / base for recurring"
    int arrivaltime "Minutes prior to notify/activate"
    string phone
    string location "Nullable"
    string destination "Nullable"
    string area "Nullable"
    text comment "Nullable"
    string source "'schedule'"
    int[] operator_ids "Nullable"
    text[] vehicles "Nullable"
    jsonb gisdata "Nullable"
    string[] repeat "Nullable, [\"HH:MM\" or 0, ... 7 days]"
  }

  ACTIVE_ORDERS_MEMORY {
    string vehicle_number PK
    object call_details "Details of the active call assigned to this vehicle"
  }

  UNSERVED_ORDERS_MEMORY {
    string call_id PK
    object call_details "Details of the unserved call"
  }

  LOCKED_CALLS_MEMORY {
    int operator_id PK
    string call_id "ID of the call locked by this operator"
  }

  OPERATORS ||--o{ CALLS : "handles"
  CALLS o--o{ CONTACTS_CACHE_REDIS : "relates to (by phone)"
  SCHEDULED_CALLS_REDIS ||..o{ CALLS : "can become"
  CALLS ||..o{ ACTIVE_ORDERS_MEMORY : "can be"
  CALLS ||..o{ UNSERVED_ORDERS_MEMORY : "can be"
  OPERATORS ||..o{ LOCKED_CALLS_MEMORY : "locks"
```

## Entity Details

### OPERATORS (PostgreSQL: `operators`)

- **`id`**: INT, Primary Key - Unique identifier for the operator.
- **`name`**: VARCHAR - Name of the operator.
- **`disabled`**: BOOLEAN - Whether the operator account is active.
- **`last_login`**: TIMESTAMP (Conceptual) - Last login time, useful for activity tracking.

### CALLS (PostgreSQL: `calls`)

- **`id`**: UUID, Primary Key - Unique identifier for the call.
- **`timestamp`**: TIMESTAMP - The initial timestamp of the call event (e.g., ring time, creation time).
- **`timeanswered`**: TIMESTAMP, Nullable - Timestamp when the call was answered by an operator or system.
- **`timeassigned`**: TIMESTAMP, Nullable - Timestamp when a vehicle was assigned or when the customer is expected to be met.
- **`phone`**: VARCHAR - Customer's phone number.
- **`location`**: TEXT, Nullable - Pickup location address.
- **`destination`**: TEXT, Nullable - Drop-off location address.
- **`area`**: VARCHAR, Nullable - Predefined geographical area for the location.
- **`arrivaltime`**: INTEGER, Nullable - Estimated/requested arrival time in minutes for assigned vehicle. For scheduled calls, minutes prior to `timestamp` for activation/notification.
- **`source`**: VARCHAR - Source of the call (e.g., 'incoming', 'outgoing', 'manual', 'schedule', 'sms', 'texy', 'multiple').
- **`extension`**: INTEGER, Nullable - PBX extension involved in the call.
- **`operator_ids`**: INTEGER[], Nullable - Array of operator IDs who handled or were involved with the call.
- **`comment`**: TEXT, Nullable - Operator's comments about the call.
- **`gisdata`**: JSONB, Nullable - Geographic data: `{ "location": {"lat": float, "lon": float, "address": string}, "destination": {"lat": float, "lon": float, "address": string} }`.
- **`vehicles`**: TEXT[], Nullable - Array of vehicle numbers assigned to the call. Empty array `"{}"` means unserved. `NULL` means info call or not yet processed for vehicles.
- **`metadata`**: JSONB, Nullable - Additional non-relational data, e.g., `{"locked": boolean, "sending_sms": boolean, "duration": int, "served_history": int, "canceled_history": int, "contact_lang": string}`.

### CONTACTS_CACHE_REDIS (Redis)

- **Key `contacts:<phone_number>` (HASH)**:
  - `name`: STRING, Nullable - Contact's name.
  - `served`: INTEGER - Count of historically served calls.
  - `canceled`: INTEGER - Count of historically canceled calls.
  - `unserved`: INTEGER - Count of historically unserved calls.
  - `firstcontact`: TIMESTAMP (epoch ms) - Timestamp of the first recorded interaction.
  - `lastaction`: TIMESTAMP (epoch ms) - Timestamp of the last action related to this contact.
  - `lang`: STRING, Nullable - Preferred language for the contact.
  - `finaldestination`: STRING, Nullable - A default or final destination set for the contact.
- **Key `contacts:<phone_number>:assigned` (STRING)**:
  - JSON string of the last assigned call details for this contact (`{id, timeassigned, vehicles, arrivaltime, location, area, destination}`).
- **Key `contacts:<phone_number>:location` (SORTED SET)**:
  - Members: Common pickup locations (string, may include area like "Street Name | Area Name").
  - Scores: Frequency count.
- **Key `contacts:<phone_number>:destination` (SORTED SET)**:
  - Members: Common drop-off destinations (string).
  - Scores: Frequency count.

### SCHEDULED_CALLS_REDIS (Redis)

- **Key `schedule:<call_id>` (STRING)**:
  - JSON string representing a scheduled call object. Fields are similar to `CALLS` but with specific meaning for scheduling:
    - `id`: UUID - Unique ID for the scheduled entry.
    - `timestamp`: NUMBER (epoch ms) - The exact future date/time for a one-off scheduled call, or the base time for a recurring call's next occurrence.
    - `arrivaltime`: NUMBER (minutes) - How many minutes *before* the `timestamp` the call should become "active" or trigger a notification.
    - `repeat`: ARRAY, Nullable - For recurring calls. An array of 7 elements (Sun to Sat). Each element is either `0` (not scheduled for this day) or a string "HH:MM" indicating the time of day.
    - Other fields: `phone`, `location`, `destination`, `area`, `comment`, `source` (always 'schedule'), `operator_ids`, `vehicles`, `gisdata`.

### STATS_CACHE_REDIS (Redis)

- **Key `stats:YYYY:MM:DD` (HASH)**:
  - Fields: `answered`, `unanswered`, `served`, `unserved`, `canceled`, `manual` (counts for the day).
  - Field: `servedByVehicle` (JSON string: `[[vehicle_number, count], ...]`).
- **Key `stats:YYYY:MM:DD:<status>:op` (HASH)**: (where `<status>` is one of the above, excluding `servedByVehicle`)
  - Fields: `<operator_id>`
  - Values: Count for that operator for that status on that day.
- **Key `stats:YYYY:MM:DD:<status>:sh` (HASH)**: (where `<status>` is one of the above)
  - Fields: `<shift_start_HH:MM>+<duration_minutes>` (e.g., "06:00+480")
  - Values: Count for that status during that shift on that day.

### In-Memory Data Structures (Backend State)

- **`lockedCalls` (Map)**: `operator_id -> call_id` - Tracks which operator has locked which call for editing.
- **`activeDrivers` (Map)**: `driver_phone_number -> vehicle_number` - Maps active drivers to their vehicles.
- **`assignedCalls` (Map)**: `vehicle_number -> call_object` - Tracks the current active order assigned to each vehicle.
- **`unservedOrders` (Map)**: `call_id -> call_object` - Tracks orders that were marked as unserved.
- **`unansweredCalls` (Map)**: `phone_number -> call_object` - Tracks currently ringing/unanswered calls.
- **`invitedCalls` (Map)**: `originator_phone_or_ext -> call_object_or_dial_id` - Tracks outgoing call attempts.
- **`lastAnsweredCalls` (Map)**: `extension:phone -> {call, timer}` - Temporarily holds calls just answered by an extension before line status updates confirm.
- **`liveCalls` (Map)**: `extension:phone -> call_object` - Tracks calls currently active on an extension.
- **`scheduled_timers` (Map)**: `scheduled_call_id -> timer_object` - In-memory timers for activating scheduled calls.
