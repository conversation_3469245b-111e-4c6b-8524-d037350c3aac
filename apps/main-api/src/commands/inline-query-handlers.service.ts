// Import the specific context type
// biome-ignore lint/style/useImportType: <explanation>
import {
	AppBot,
	AppBotErrorDefinitions,
	AppInlineQueryContext,
} from "@/types/bot-context.js";
// src/commands/inline-query-handlers.service.ts
// import { InlineQueryContext } from "@gramio/contexts"; // Not needed if using AppInlineQueryContext
import { Injectable, Logger } from "@nestjs/common";
// biome-ignore lint/style/useImportType: <explanation>
import {
	Bot,
	InlineKeyboard,
	InlineQueryResult,
	InputMessageContent,
} from "gramio";

@Injectable()
export class InlineQueryHandlersService {
	private readonly logger = new Logger(InlineQueryHandlersService.name);

	private botInstance!: Bot<AppBotErrorDefinitions>;
	// Use AppBot type for the bot instance
	public initialize(bot: AppBot): void {
		this.botInstance = bot as Bot<AppBotErrorDefinitions>; // Cast if necessary, but AppBot should be compatible
	}

	registerHandlers() {
		this.logger.debug("Registering inline query handlers.");
		this.botInstance.inlineQuery(
			/find (.*)/i,
			this.handleFindQuery.bind(this),
			// `onResult` context will be ChosenInlineResultContext extended with AppContextExtensions
			// {
			//   onResult: this.handleFindQueryResult.bind(this), // This handler is actually for ChosenInlineResultContext
			// }
			// For onResult, it's better to register it via chosenInlineResult handler if you have complex logic
			// or use the onResult from the inlineQuery options if it's simple.
			// If using the onResult option, the context type will be ChosenInlineResultContext<AppBot> & AppContextExtensions.
			// Let's assume you handle this in ChosenInlineResultHandlersService for clarity or keep it simple here.
		);
	}

	private async handleFindQuery(context: AppInlineQueryContext): Promise<void> {
		if (!context.query) {
			this.logger.debug("Received empty inline query");
			return;
		}

		const match = context.query.match(/find (.*)/i);
		const searched = match?.[1]?.trim();

		if (!searched) {
			this.logger.debug("No search term in inline query");
			return;
		}

		this.logger.verbose(
			`Received inline query: "${context.query}" from user ${context.from?.id}`,
		);
		if (searched) {
			await context.answer(
				[
					InlineQueryResult.article(
						"result-1",
						String(
							context.t("inlineQuery.result_title", searched) ??
								`Results for "${searched}"`,
						),
						InputMessageContent.text(
							context.t("inlineQuery.result_content", searched),
						),
						{
							reply_markup: new InlineKeyboard().text(
								context.t("inlineQuery.button_details"),
								"details-callback", // This would be handled by CallbackQueryHandlersService
							),
						},
					),
				],
				{
					cache_time: 0,
				},
			);
			this.logger.debug(
				`Answered inline 'find' query with localized result for: ${searched}`,
			);
		} else {
			this.logger.debug(
				"No search term extracted from inline query; nothing answered.",
			);
		}
	}

	// This method is for `chosen_inline_result` updates.
	// It should be registered via `bot.chosenInlineResult` or as onResult.
	// If it's part of chosen_inline_result, its context should be AppChosenInlineResultContext.
	// For simplicity, if this was intended for the `onResult` option of `bot.inlineQuery`:
	// private handleFindQueryResult(
	//   context: AppChosenInlineResultContext // Context for onResult or bot.chosenInlineResult
	// ): void { // GramIO's onResult for inlineQuery might not expect a Promise
	//   this.logger.verbose(
	//     `User ${context.from?.id} selected an inline query result: ${context.resultId}`
	//   );
	//   // Example: Log selection. No need to `answerInlineQuery` here as that's for the main query response.
	// }
}
