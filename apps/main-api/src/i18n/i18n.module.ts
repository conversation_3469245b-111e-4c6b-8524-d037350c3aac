import { join } from "node:path";
import { HttpModule } from "@nestjs/axios";
import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import {
	AcceptLanguageResolver,
	HeaderResolver,
	I18nModule as NestI18nModule,
	QueryResolver,
} from "nestjs-i18n";
import { I18nUtilsService } from "./i18n-utils.service";

@Module({
	imports: [
		ConfigModule,
		HttpModule,
		NestI18nModule.forRoot({
			fallbackLanguage: "en",
			loaderOptions: {
				path: join(__dirname, "/"),
				watch: true,
			},
			resolvers: [
				{ use: QueryResolver, options: ["lang", "locale"] },
				AcceptLanguageResolver,
				new HeaderResolver(["x-lang"]),
			],
		}),
	],
	providers: [I18nUtilsService],
	exports: [I18nUtilsService, NestI18nModule],
})
export class I18nModule {}
