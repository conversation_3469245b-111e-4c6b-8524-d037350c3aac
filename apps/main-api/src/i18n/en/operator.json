{"operator": {"status": {"active": "Active", "inactive": "Inactive", "suspended": "Suspended"}, "validation": {"name_required": "Name is required", "name_string": "Name must be a string", "name_min_length": "Name must be at least 2 characters long", "password_required": "Password is required", "password_min_length": "Password must be at least 8 characters", "password_complexity": "Password must contain at least one uppercase letter, one lowercase letter and one number", "invalid_data": "Invalid operator data"}, "errors": {"load_failed": "Failed to load operators", "create_failed": "Failed to create operator", "not_found": "Operator with ID {id} not found", "update_failed": "Failed to update operator", "delete_failed": "Failed to delete operator", "translation_failed": "Failed to translate operator status", "find_failed": "Failed to find operator", "status_invalid": "Invalid operator status", "database_error": "Database operation failed", "auth_error": "Authentication required", "validation_error": "Validation failed"}}}