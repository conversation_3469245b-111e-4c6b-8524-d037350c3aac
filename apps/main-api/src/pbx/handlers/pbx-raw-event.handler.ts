// # Consumes raw PBX events from RabbitMQ, triggers domain logic
import { Injectable, Logger } from "@nestjs/common";
import { <PERSON><PERSON>and<PERSON> } from "@/rabbitmq/handler.decorator";
import type {
	PbxCallCreatedDomainEvent,
	PbxCallInitiatedStandardizedEvent,
	PbxCallTerminatedStandardizedEvent,
	AnyStandardizedPbxEvent,
	PbxCallAnsweredStandardizedEvent,
} from "@repo/types";
import {
	users as UsersSchema,
	eq,
	and,
	pbxCalls as PbxCallSchema,
	userTenants as UserTenantsSchema, // Import userTenants schema
	operators as OperatorsSchema,
} from "@repo/db";
import { v4 as uuidv4 } from "uuid";
import type { AckOrNack } from "rascal";
import type { RascalService } from "@/rabbitmq/rascal.service";
import type { DatabaseService } from "@/services/database/database.service";

@Injectable()
export class PbxRawEventHandler {
	private readonly logger = new Logger(PbxRawEventHandler.name);

	constructor(private readonly dbService: DatabaseService, private readonly rascalService: RascalService) {}

	@RabbitHandler({ subscription: 'sub_pbx_event_processor' })
	async handleRawPbxEvent(
		content: AnyStandardizedPbxEvent, // Use a union type for all possible events on this queue // Corrected type
		ackOrNack: AckOrNack,
	): Promise<void> {
		// Corrected type
		const { eventId, tenantId, data, sourceService } = content;
		this.logger.log(
			`[${eventId}] Received raw PBX event from ${sourceService} for tenant ${tenantId}, type: ${data.standardizedEventType}`,
		);

		try {
			const db = this.dbService.getDb();

			if (!tenantId) {
				this.logger.error(
					`[${eventId}] Cannot process PBX event: tenantId is null.`,
				);
				return ackOrNack(new Error("Tenant ID is null"), {
					strategy: "nack",
					requeue: false,
				});
			}

			// Handle Standardized PBX Events published by PbxEventRouterService
			if (data.standardizedEventType === "PBX_CALL_INITIATED") {
				const callInitiatedData =
					data as PbxCallInitiatedStandardizedEvent["data"];
				this.logger.log(
					`[${eventId}] Handling Standardized PBX_CALL_INITIATED for externalCallId: ${callInitiatedData.externalCallId}`,
				);

				// 1. Find or create User based on caller ID
				// Find user_tenant first, then the user
				let customerUser = await db.query.users.findFirst({
					where: callInitiatedData.callerIdNum ? eq(UsersSchema.phone, callInitiatedData.callerIdNum) : undefined, // Find user by phone globally
					with: { userTenants: { where: eq(UserTenantsSchema.tenantId, tenantId) } }, // Check if they have a user_tenant entry for this tenant
				});

				// If user exists but not for this tenant, or user doesn't exist at all
				if (!customerUser && callInitiatedData.callerIdNum) {
					// TODO: Implement proper user creation/linking logic, potentially involving user_tenants
					// For now, create a basic user record if one doesn't exist globally by phone number.
					// A separate process should link this user to the tenant via user_tenants if needed.
					try { // Use callerIdNum from core data
						const [createdUser] = await db
							.insert(UsersSchema)
							.values(<any>{ // Cast to any to bypass Drizzle type issue with nullable phone
								phone: callInitiatedData.callerIdNum || null, // Handle undefined phone
								displayName:
									callInitiatedData.callerIdName ||
									callInitiatedData.callerIdNum,
								// Add other required fields for User
								// tenantId should NOT be set here if using user_tenants for tenant association, but Drizzle schema requires it. Revisit this. // Corrected: Removed tenantId as it's not on the User schema
											})
							.returning();
						this.logger.log(
							`[${eventId}] Created new User ${createdUser.id} for phone ${callInitiatedData.callerIdNum}.`,
						); // Use callerIdNum
					} catch (userError) {
						this.logger.error(
							`[${eventId}] Failed to create User for phone ${callInitiatedData.callerIdNum}:`,
							userError,
						);
						// Continue without a linked user if creation fails
						customerUser = undefined;
					}
				}

				// 2. Create PbxCall record
				const [newPbxCall] = await db
					.insert(PbxCallSchema)
					.values({
						tenantId: tenantId, // tenantId is guaranteed non-null here
						userId: customerUser?.id,
						externalPbxId: callInitiatedData.externalCallId,
						fromPhoneNumber: callInitiatedData.callerIdNum, // Use callerIdNum from core data
						toPhoneNumber: callInitiatedData.dialedNum, // Use dialedNum from core data
						callerName: callInitiatedData.callerIdName,
						direction: callInitiatedData.direction as any, // Cast to any to bypass Drizzle type issue if enum is not perfectly aligned
						status: "RINGING", // Use appropriate status
						source: "PBX_INCOMING", // Or derive from event data
						startedAt: new Date(callInitiatedData.eventTimestamp),
						numericExtension: callInitiatedData.extension ? Number.parseInt(callInitiatedData.extension, 10) : undefined, // Use extension field
						pbxRawDetails: callInitiatedData.rawPdsEvent, // Store the raw event
					})
					.returning();

				this.logger.log(
					`[${eventId}] Created PbxCall ${newPbxCall.id} for tenant ${tenantId}.`,
				);

				// 3. Publish Domain Event
				const callCreatedEvent: PbxCallCreatedDomainEvent = {
					eventId: uuidv4(),
					timestamp: new Date().toISOString(),
					version: "1.0",
					sourceService: "PbxRawEventHandler",
					tenantId: tenantId,
					correlationId: eventId,
					actor: content.actor, // Propagate actor
					data: {
						...newPbxCall, // Include all fields from the newly created PbxCall
					},
				};
				await this.rascalService.publish(
					"pub_call_domain_event",
					callCreatedEvent,
					{
						routingKeyCtx: {
							domain: "pbxcall",
							action: "created",
							tenantId: tenantId,
							entityId: newPbxCall.id,
						},
					},
				);
			} else if (data.standardizedEventType === "PBX_CALL_TERMINATED") {
				const callTerminatedData =
					data as PbxCallTerminatedStandardizedEvent["data"];
				// Find the PbxCall by externalCallId and update its status, duration, endedAt, terminationReason, dispositionCode
				this.logger.log(
					`[${eventId}] Handling Standardized PBX_CALL_TERMINATED for externalCallId: ${callTerminatedData.externalCallId}`,
				);

				const existingCall = await db.query.pbxCalls.findFirst({
					where: and(
						eq(PbxCallSchema.externalPbxId, callTerminatedData.externalCallId),
						eq(PbxCallSchema.tenantId, tenantId),
					),
				});

				if (existingCall) {
					const endedAt = new Date(callTerminatedData.eventTimestamp);
					const duration = existingCall.startedAt ? Math.floor((endedAt.getTime() - existingCall.startedAt.getTime()) / 1000
							)
						: undefined;

					await db
						.update(PbxCallSchema)
						.set({
							status: "ENDED", // Use 'ENDED' or map from terminationReason/dispositionCode
							endedAt: endedAt,
							duration: duration,
							terminationReason: callTerminatedData.terminationReason,
							dispositionCode: callTerminatedData.dispositionCode,
							pbxRawDetails: callTerminatedData.rawPdsEvent, // Update with final event details
							// Add other fields like hangup cause if available
						})
						.where(eq(PbxCallSchema.id, existingCall.id))
						.returning();

					this.logger.log(
						`[${eventId}] Updated PbxCall ${existingCall.id} status to COMPLETED.`,
					);

					// Publish PbxCallTerminatedDomainEvent
					// TODO: Define and publish PbxCallTerminatedDomainEvent
					// Example:
					// const callTerminatedDomainEvent: PbxCallTerminatedDomainEvent = { ... };
					// await this.rascalService.publish('pub_call_domain_event', callTerminatedDomainEvent, { ... });
				} else {
					this.logger.warn(
						`[${eventId}] PbxCall not found for externalCallId: ${callTerminatedData.externalCallId}. Cannot update status.`,
					);
					// Optionally log this as an unhandled event or create a minimal record
				}
			} else if (data.standardizedEventType === "PBX_CALL_ANSWERED") {
				const callAnsweredData =
					data as PbxCallAnsweredStandardizedEvent["data"];
				this.logger.log(
					`[${eventId}] Handling Standardized PBX_CALL_ANSWERED for externalCallId: ${callAnsweredData.externalCallId}`,
				);

				const existingCall = await db.query.pbxCalls.findFirst({
					where: and(
						eq(PbxCallSchema.externalPbxId, callAnsweredData.externalCallId),
						eq(PbxCallSchema.tenantId, tenantId),
					),
				});

				if (existingCall && existingCall.status !== "ANSWERED") {
					// Prevent multiple updates
					// Find the operator by the answeredByTarget (assuming it's an extension or operator ID)
					// This requires mapping the PBX target to an internal operator ID.
					// For simplicity, let's assume answeredByTarget is the numeric extension and we find an operator by that.
					let answeringOperatorId: string | undefined | null = undefined;
					// Assuming answeredByTarget is the numeric extension
					const operator = await db.query.operators.findFirst({
						where: and(
							eq(OperatorsSchema.tenantId, tenantId),
							eq(OperatorsSchema.phone, callAnsweredData.answeredByTarget), // Assuming answeredByTarget is the extension/phone number // Corrected schema reference
						),
					});
					answeringOperatorId = operator?.id;
					// answeringOperatorId = operator?.id;
					await db
						.update(PbxCallSchema)
						.set({
							status: "ANSWERED", // Use 'ANSWERED'
							answeredAt: new Date(callAnsweredData.eventTimestamp),
							operatorId: answeringOperatorId || existingCall.operatorId, // Assign operator if found, otherwise keep existing
							pbxRawDetails: callAnsweredData.rawPdsEvent, // Update with answered event details
						})
						.where(eq(PbxCallSchema.id, existingCall.id))
						.returning();

					this.logger.log(
						`[${eventId}] Updated PbxCall ${existingCall.id} status to ANSWERED.`,
					);

					// Publish CallAnsweredPayload domain event
					// TODO: Define and publish CallAnsweredPayload
					// Example:
					// const callAnsweredDomainEvent: CallAnsweredPayload = { ... };
					// await this.rascalService.publish('pub_call_domain_event', callAnsweredDomainEvent, { ... });
				} else if (!existingCall) {
					this.logger.warn(
						`[${eventId}] PbxCall not found for externalCallId: ${callAnsweredData.externalCallId}. Cannot update status to ANSWERED.`,
					);
				} else {
					this.logger.debug(
						`[${eventId}] PbxCall ${existingCall.id} already marked as ANSWERED or later status.`,
					);
				}
			}
			// ... other event types

			ackOrNack();
		} catch (error) {
			this.logger.error(
				`[${eventId}] Error processing raw PBX event for tenant ${tenantId}:`,
				error,
			);
			ackOrNack(error as Error, { strategy: "nack", requeue: false }); // Consider your DLQ/retry strategy
		}
	}
}
