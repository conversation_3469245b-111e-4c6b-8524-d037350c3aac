# Call Management Features

## Call Management - Fetch Initial Calls

**Functional Description:**
Loads the initial list of live calls when the application starts or after an operator logs in.

### API Interface

- **Protocol:** WebSocket (Server Push)
- **Event/Endpoint:** `call:init` (sent by server after `client:init`)
- **Request:** Implicitly triggered after `client:init`. The `client:init` handler on the backend may take an optional `from` timestamp from client options to filter calls.
- **Response:**

  ```json
  {
    "event": "call:init",
    "data": [
      {
        "id": "string (uuid)",
        "timestamp": "number (epoch ms)",
        "vehicles": "Array<string> (optional)",
        "phone": "string",
        "location": "string (optional)",
        "destination": "string (optional)",
        "area": "string (optional)",
        "arrivaltime": "number (minutes, optional)",
        "source": "string (e.g., 'incoming', 'manual', 'schedule')",
        "extension": "number",
        "operator_id": "number (optional, last operator)",
        "operator_ids": "Array<number> (optional, all involved operators)",
        "timeassigned": "number (epoch ms, optional)",
        "timeanswered": "number (epoch ms, optional)",
        "gisdata": "object (optional, { location?: {lat, lon, address}, destination?: {lat, lon, address} })",
        "comment": "string (optional)",
        "metadata": "object (optional, e.g., { locked?: boolean, sending?: boolean, duration?: number, served?: number, canceled?: number, lang?: string })"
      }
    ]
  }
  ```

- **Authentication:** WebSocket (Operator Session)

### Business Rules

- Backend fetches calls from the `calls` table (PostgreSQL) within a defined backlog period (e.g., last 8 hours).
- Attempts to enrich calls with contact information (name, served/canceled history) by looking up the phone number.
- Attempts to add metadata about active orders if the call was recently assigned a vehicle.
- Filters out calls older than the backlog unless the operator's login timestamp is older.
- If `autosetInfo` is configured, unanswered calls from certain sources might automatically transition to "info" calls (vehicles=null) after a timeout.

### Data Model

```mermaid
erDiagram
  CALLS {
    uuid id PK
    timestamp timestamp
    timestamp timeanswered
    timestamp timeassigned
    string phone
    text[] vehicles
    string source
    int extension
    int[] operator_ids
    jsonb metadata
  }
  CONTACTS_CACHE_REDIS {
    string phone PK
    string name
    int served_count
    int canceled_count
  }
  CALLS o--o{ CONTACTS_CACHE_REDIS : "uses_for_info"
```

### Error Handling

- Errors in fetching data on the backend would typically prevent the `call:init` from being sent or result in an empty list. Connection issues are handled by WebSocket protocols.

---

## Call Management - Add New Call

**Functional Description:**
Allows an operator to manually add a new call or duplicate an existing call (source 'multiple').

### API Interface

- **Protocol:** WebSocket
- **Event/Endpoint:** `call:add`
- **Request:**

  ```json
  {
    "event": "call:add",
    "data": {
      "extension": "number (optional)",
      "phone": "string",
      "location": "string (optional)",
      "area": "string (optional)",
      "timestamp": "number (epoch ms, optional, defaults to now)",
      "timeanswered": "number (epoch ms, optional, defaults to now for manual non-numeric ext)",
      "comment": "string (optional)",
      "source": "string (e.g., 'manual', 'multiple')",
      "destination": "string (optional)",
      "vehicles": "Array<string> (optional)",
      "arrivaltime": "number (minutes, optional)",
      "gisdata": "object (optional)"
    }
  }
  ```

- **Response (Broadcast to all clients):**

  ```json
  {
    "event": "call:add",
    "data": { /* Full Call object with server-generated id, timestamp, operator_ids, etc. */ }
  }
  ```

- **Authentication:** WebSocket (Operator Session)

### Business Rules

- Backend assigns a UUID if `id` is not present.
- Sets `timestamp` and `timeanswered` if not provided (for certain sources like 'manual').
- Adds current operator to `operator_ids`.
- If `vehicles` are provided and `timeassigned` is not, `timeassigned` is set to `Date.now()` (+ `arrivaltime` if present), and order creation logic in `orders.js` is triggered (updates Redis, may send SMS).
- The call is saved to the `calls` PostgreSQL table.
- If the source is 'multiple', the new call might automatically enter edit mode for the creating operator (server sends `call:edit` to self after `call:add`).

### Data Model

```mermaid
erDiagram
  CALLS {
    uuid id PK
    timestamp timestamp
    timestamp timeanswered
    timestamp timeassigned
    string phone
    text[] vehicles
    string source
    int[] operator_ids
  }
  OPERATORS {
    int id PK
    string name
  }
  CALLS ||--o{ OPERATORS : "created_by"
```

### Error Handling

- If required fields (e.g., `phone`) are missing, the backend might ignore the request or log an error. No specific error event is defined for this back to the client in the provided code, but a robust implementation would send a `server:error`.

---

## Call Management - Update Call Details

**Functional Description:**
Allows an operator to edit details of an existing call, such as location, destination, assigned vehicles, arrival time, area, or phone number (for manual calls).

### API Interface

- **Protocol:** WebSocket
- **Event/Endpoint:** `call:update`
- **Request:**

  ```json
  {
    "event": "call:update",
    "data": { /* Call object with 'id' and fields to update */
      "id": "string (uuid)",
      "location": "string (updated)",
      "vehicles": ["101"],
      "arrivaltime": 10
      // ... other fields
    }
  }
  ```

- **Response (Broadcast to all clients):**

  ```json
  {
    "event": "call:update",
    "data": { /* Full updated Call object */ }
  }
  ```

- **Authentication:** WebSocket (Operator Session). The call must be locked by the requesting operator.

### Business Rules

- Backend verifies the call is locked by the operator (`lockedCalls` map).
- Updates `operator_ids` to include the current operator if not already last.
- If `destination` starts with `!`, it's treated as a command to set/clear default destination for the contact in Redis.
- If `vehicles` field is not present in the update, it's set to `null` (info call).
- **Vehicle Assignment Logic (`orders.js`):**
  - If `vehicles` are newly assigned (were null/empty, now have entries) and `timeassigned` is not set:
    - `timeassigned` is set to `Date.now()` (+ `arrivaltime` if present).
    - `orders.create(call)` is called: updates Redis (`contacts:<phone>:assigned`, `contacts:<phone>:location/destination` scores, `served` count), updates `assignedCalls` map, may send "vehicleAssigned" SMS.
  - If `vehicles` are changed (and `timeassigned` exists):
    - `orders.update(call)` is called: complex logic to update Redis scores, `served`/`unserved` counts, `assignedCalls` map, `unservedOrders` map, and may send "vehicleUpdated", "noVehicle", or "vehicleAssigned" SMS depending on the change.
  - If `vehicles` are removed (become empty or null) and `timeassigned` existed:
    - `timeassigned` is set to `null`.
    - `orders.delete(call)` is called: updates Redis scores, `served`/`unserved` counts, `assignedCalls` map, may send "vehicleUnassigned" or "vehicleCanceled" SMS.
- Updates the record in the `calls` PostgreSQL table.
- The call is unlocked after the update (`lockedCalls.delete(operator_id)`).
- `gisdata` can be updated.

### Data Model

```mermaid
erDiagram
  CALLS {
    uuid id PK
    timestamp timeassigned
    text[] vehicles
    string location
    string destination
    int[] operator_ids
    jsonb gisdata
  }
  CONTACTS_CACHE_REDIS {
    string phone PK
    string last_assigned_call_details
    zset common_locations
    zset common_destinations
    int served_count
  }
  ACTIVE_ORDERS_MEMORY {
    string vehicle_number PK
    object call_details
  }
  CALLS ||--o{ OPERATORS : "updated_by"
  CALLS o--o{ CONTACTS_CACHE_REDIS : "updates_stats_for"
  CALLS ||..o{ ACTIVE_ORDERS_MEMORY : "updates_state_of"
```

### Error Handling

- If the call is not locked by the operator, the update is rejected (implicitly, no `server:error` defined, but `checkLocked` in `eventHandlers.js` would prevent execution).

---

## Call Management - Remove Call

**Functional Description:**
Allows an operator to remove a call, typically if it was manually added, duplicated, or is a scheduled entry that needs deletion.

### API Interface

- **Protocol:** WebSocket
- **Event/Endpoint:** `call:remove`
- **Request:**

  ```json
  {
    "event": "call:remove",
    "data": {
      "id": "string (uuid)",
      "source": "string (must be 'multiple', 'manual', or 'schedule')"
    }
  }
  ```

- **Response (Broadcast to all clients):**

  ```json
  {
    "event": "call:remove",
    "data": "string (id of the removed call)"
  }
  ```

- **Authentication:** WebSocket (Operator Session). The call must be locked by the requesting operator.

### Business Rules

- Backend verifies the call is locked and the `source` is one of 'multiple', 'manual', or 'schedule'.
- If vehicles were assigned, `orders.delete(call)` is called to update related stats and states (vehicles are set to null before calling `orders.delete` in this context).
- The call is unlocked (`lockedCalls.delete(operator_id)`).
- The call is deleted from the `calls` PostgreSQL table.

### Data Model

```mermaid
erDiagram
  CALLS {
    uuid id PK
    string source
  }
```

### Error Handling

- If the call is not locked or the source is not removable, the action is rejected (implicitly by `checkLocked` or source check in `eventHandlers.js`).

---

## Call Management - Cancel Call Assignment

**Functional Description:**
Cancels a vehicle assignment for a call. The call record remains but is marked as canceled (e.g., `timeassigned` becomes null).

### API Interface

- **Protocol:** WebSocket
- **Event/Endpoint:** `call:cancel`
- **Request:**

  ```json
  {
    "event": "call:cancel",
    "data": { /* Call object with id and vehicles that were assigned */
      "id": "string (uuid)",
      "vehicles": ["101"]
      // ... other relevant call fields
    }
  }
  ```

- **Response (Broadcast to all clients):**

  ```json
  {
    "event": "call:update",
    "data": { /* Call object with 'timeassigned' set to null */ }
  }
  ```

- **Authentication:** WebSocket (Operator Session). The call must be locked by the requesting operator.

### Business Rules

- Backend verifies the call is locked and has vehicles assigned.
- Sets `timeassigned` to `null`.
- Updates `operator_ids`.
- Unlocks the call (`lockedCalls.delete(operator_id)`).
- Calls `orders.delete(call)` which updates Redis stats (e.g., increments `canceled` count for the contact, decrements `served`, updates location/destination scores) and may send "vehicleCanceled" SMS.
- Updates the call record in the `calls` PostgreSQL table.

### Data Model

```mermaid
erDiagram
  CALLS {
    uuid id PK
    timestamp timeassigned
    text[] vehicles
    int[] operator_ids
  }
  CONTACTS_CACHE_REDIS {
    string phone PK
    int canceled_count
    int served_count
  }
  CALLS o--o{ CONTACTS_CACHE_REDIS : "updates_stats_for"
```

### Error Handling

- If the call is not locked or has no vehicles, the action is rejected.
- User confirmation is handled client-side; if denied, the lock is released on the server.

---

## Call Management - Activate/Undo Cancel Call

**Functional Description:**
Re-activates a previously canceled call by restoring its vehicle assignment and `timeassigned` field, effectively making it an active order again.

### API Interface

- **Protocol:** WebSocket
- **Event/Endpoint:** `call:update` (This is handled as a standard update)
- **Request:**

  ```json
  {
    "event": "call:update",
    "data": { /* Call object with 'id', 'vehicles' array, and 'timeassigned' restored/recalculated */
      "id": "string (uuid)",
      "vehicles": ["101"],
      "timeassigned": "number (epoch ms)"
      // ... other fields
    }
  }
  ```

- **Response (Broadcast to all clients):**

  ```json
  {
    "event": "call:update",
    "data": { /* Full updated Call object */ }
  }
  ```

- **Authentication:** WebSocket (Operator Session). The call must be locked by the requesting operator.

### Business Rules

- This is processed by the standard `call:update` handler.
- The client prepares the call object to reflect an active state (e.g., setting `timeassigned`, providing `vehicles`).
- Backend `call:update` logic applies, which will trigger `orders.create(call)` or `orders.update(call)` if `timeassigned` is now set and vehicles are present. This updates Redis stats (e.g., decrements `canceled`, increments `served`) and may send SMS.

### Data Model

```mermaid
erDiagram
  CALLS {
    uuid id PK
    timestamp timeassigned
    text[] vehicles
  }
  CONTACTS_CACHE_REDIS {
    string phone PK
    int canceled_count
    int served_count
  }
  CALLS o--o{ CONTACTS_CACHE_REDIS : "updates_stats_for"
```

### Error Handling

- Standard `call:update` error handling (e.g., if call not locked).
- User confirmation is handled client-side.

---

## Call Management - Lock/Unlock Call for Editing

**Functional Description:**
Manages exclusive editing rights for a call to prevent concurrent modifications by different operators.

### API Interface

- **Protocol:** WebSocket
- **Event/Endpoint (Lock Request):** `call:edit`
- **Request (Lock):**

  ```json
  {
    "event": "call:edit",
    "data": "string (call_id)"
  }
  ```

- **Response (Lock, Unicast to self):**

  ```json
  {
    "event": "call:edit",
    "data": "string (call_id)"
  }
  ```

- **Response (Lock, Broadcast to others):**

  ```json
  {
    "event": "call:locked",
    "data": "string (call_id)"
  }
  ```

- **Event/Endpoint (Unlock Request):** `call:unlocked`
- **Request (Unlock):**

  ```json
  {
    "event": "call:unlocked",
    "data": "string (call_id)"
  }
  ```

- **Response (Unlock, Broadcast to others):**

  ```json
  {
    "event": "call:unlocked",
    "data": "string (call_id)"
  }
  ```

- **Authentication:** WebSocket (Operator Session)

### Business Rules

- Backend maintains an in-memory map (`lockedCalls`: operator_id -> call_id).
- **Locking (`call:edit` from client):**
  - If the call is already locked by another operator, a `client:error` ("This call is already locked") is sent to the requester.
  - If the requesting operator already has another call locked, that call is first unlocked (broadcasts `call:unlocked`).
  - The call_id is then associated with the requesting operator_id in `lockedCalls`.
  - `call:locked` is broadcast to other clients.
  - `call:edit` (as acknowledgment) is sent back to the requesting client.
- **Unlocking (`call:unlocked` from client):**
  - If the call was indeed locked by the requesting operator, the entry is removed from `lockedCalls`.
  - `call:unlocked` is broadcast to other clients.
- Unlocking also happens implicitly after a successful `call:update`, `call:remove`, or `call:cancel`.
- If an operator disconnects (`client:exit`), any call they had locked is automatically unlocked.

### Data Model

```mermaid
erDiagram
  LOCKED_CALLS_MEMORY {
    int operator_id PK
    string call_id
  }
  OPERATORS ||--o{ LOCKED_CALLS_MEMORY : "locks"
  CALLS ||..o{ LOCKED_CALLS_MEMORY : "is_locked_as"
```

### Error Handling

- `client:error` with message "This call is already locked" if attempting to lock an already locked call.
- `client:error` with message "call not locked" if attempting to update/cancel a call not locked by the operator.

---

## Call Management - Dial/Originate Call

**Functional Description:**
Initiates an outgoing call from the operator's station to a customer, or between operator and customer, via the PBX/Asterisk gateway.

### API Interface

- **Protocol:** WebSocket
- **Event/Endpoint:** `call:new`
- **Request:**

  ```json
  {
    "event": "call:new",
    "data": {
      "phone": "string (customer's number)",
      "localNumber": "number (operator's extension/line, optional)",
      "id": "string (call_id if related to an existing call, optional)"
    }
  }
  ```

- **Response (Unicast to self, on error):**

  ```json
  {
    "event": "server:error",
    "data": {
      "event": "call:new",
      "data": { /* original request data */ },
      "msg": "error description (e.g., 'bad number', 'not answering', 'Dial already in progress')"
    }
  }
  ```

  OR

  ```json
  {
    "event": "message:new",
    "data": {
      "type": "system",
      "from": "connecting to <phone>",
      "timestamp": "string (epoch ms)",
      "autohide": "number",
      "msg": [
        ["Error dialing {{phone}}\n", { "phone": "<phone>" }],
        ["User is unreachable\n", null],
        ["<error_details>", null]
      ]
    }
  }
  ```

- **Authentication:** WebSocket (Operator Session)

### Business Rules

- Backend (`eventHandlers.js` -> `core.js`) uses `core.inviteCall(phone, operator_id, localNumber, id)` to track the attempt. This prevents duplicate dial attempts to the same number if one is already in progress by checking the `invitedCalls` map.
- If `inviteCall` returns true (new attempt):
  - Calls `asteriskGateway.originate(num1, num2, callback)`.
    - If `localNumber` is provided: `num1` = `localNumber`, `num2` = `phone`.
    - Else: `num1` = `phone`, `num2` = `null`.
  - The Asterisk gateway makes an HTTP request to a configured path (e.g., `texyDialPath` in `asteriskGW.json`) with `num1` and `num2`.
  - Errors from `originate` (e.g., gateway timeout, HTTP error from Asterisk script) are sent back to the client via `server:error` or a system `message:new`.
- Call progress (ringing, answered, terminated) is expected to come via other channels (e.g., TCP listener or `/api/lineStatusUpd`).

### Data Model

```mermaid
erDiagram
  INVITED_CALLS_MEMORY {
    string originator_phone_or_ext PK
    object_or_string call_details_or_dial_id
  }
  CALLS {
    uuid id
    string source "'operator'"
    int[] operator_ids
  }
  INVITED_CALLS_MEMORY ||..o{ CALLS : "may_create"
```

- `asteriskGW.json` stores gateway connection details.

### Error Handling

- "Dial already in progress" if `core.inviteCall` returns false.
- Errors from `asteriskGateway.originate` (e.g., "bad number", "not answering", "request timeout", "http status: XXX") are relayed.

---

## Call Management - Answer Call (from Notification)

**Functional Description:**
Allows an operator to mark a call (typically from a Texy/app notification) as answered, which may then transition it to an editable state.

### API Interface

- **Protocol:** WebSocket
- **Event/Endpoint:** `call:answer`
- **Request:**

  ```json
  {
    "event": "call:answer",
    "data": { /* Call object from notification */
      "id": "string (uuid, often pre-generated by client or source system)",
      "phone": "string",
      "source": "string (e.g., 'texy', 'bookmark')",
      "location": "string (optional)",
      "comment": "string (optional, often location for texy)"
      // ... other data from notification
    }
  }
  ```

- **Response (Broadcast to all clients):**

  ```json
  {
    "event": "call:update",
    "data": { /* Call object with 'timeanswered' set and operator assigned */ }
  }
  ```

  Followed by (Unicast to self, if successful):

  ```json
  {
    "event": "call:edit",
    "data": "string (call_id)"
  }
  ```

- **Authentication:** WebSocket (Operator Session)

### Business Rules

- Backend (`eventHandlers.js`) checks if `call.timeanswered` is already set. If so, sends a `client:error` ("This call is already answered").
- If not answered:
  - Sets `call.timeanswered = Date.now()`.
  - Adds the current `operator_id` to `call.operator_ids`.
  - Broadcasts the `call:update` event with the modified call object.
  - Internally triggers `restricted["call:edit"](operator_id, call.id, cb)` to attempt to lock the call for the answering operator.
  - Saves the call to the `calls` PostgreSQL table using `core.createCall(call)`.

### Data Model

```mermaid
erDiagram
  CALLS {
    uuid id PK
    timestamp timeanswered
    string source
    int[] operator_ids
  }
  OPERATORS {
    int id PK
  }
  CALLS ||--o{ OPERATORS : "answered_by"
```

### Error Handling

- `client:error` with message "This call is already answered" if `timeanswered` was already set.

```
