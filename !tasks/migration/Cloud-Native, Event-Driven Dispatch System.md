
# Revised Implementation Plan: Cloud-Native, Event-Driven Dispatch System

This plan incorporates the vision of a multi-tenant, cloud-native system with chatbot integration, emphasizing RabbitMQ for event-driven architecture and service decoupling.

## I. Database Schema (`packages/db/src/schema/`) - Refinements for Cloud & Multi-Tenancy

Your existing new schema in `packages/db` already shows a strong foundation for multi-tenancy (`tenants.schema.ts`, `bots.schema.ts`, `user-tenant.schema.ts`, etc.).

**Key Considerations & Actions:**

1. **`pbx-call.schema.ts` (Core Call Log):**
    * **Tenant ID:** Ensure `tenantId` is a non-nullable foreign key. All calls must belong to a tenant.
    * **Source Granularity:** The `source` field should clearly distinguish between calls from:
        * Traditional PBX (e.g., `SOURCE_PBX_INCOMING`, `SOURCE_PBX_OUTGOING`)
        * Operator Manual Entry (e.g., `SOURCE_OPERATOR_MANUAL`)
        * Chatbot (e.g., `SOURCE_CHATBOT_TELEGRAM`, `SOURCE_CHATBOT_VIBER`)
        * Driver App (future) (e.g., `SOURCE_DRIVER_APP`)
        * Passenger App (future) (e.g., `SOURCE_PASSENGER_APP`)
    * **Linking to Chat/Bot Sessions:** If a call originates from or is related to a chatbot interaction, consider adding nullable foreign keys like `chatbot_session_id` or `chatbot_message_id`.
2. **`scheduled_calls.schema.ts` (PostgreSQL):**
    * **Tenant ID:** Mandatory.
    * **Activation Mechanism:** Instead of in-memory timers in NestJS, scheduled call activation should publish an event to RabbitMQ (e.g., `schedule.activation.due`) when `timestamp - arrivaltime_offset` is reached. A dedicated worker service will consume these events.
3. **`contacts.schema.ts` (PostgreSQL):**
    * **Tenant ID:** Mandatory. A contact (phone number) can exist across multiple tenants but their associated data (name, history within that tenant) should be tenant-specific. This might mean a `tenant_contacts` table or ensuring all queries on a global `contacts` table are tenant-scoped.
    * **Action:** Define `contacts.schema.ts` with `tenantId`.
4. **`sms_logs.schema.ts` (PostgreSQL):**
    * **Tenant ID:** Mandatory.
    * Fields: `id`, `tenant_id`, `direction` ('inbound', 'outbound'), `phone_from`, `phone_to`, `message_content`, `status` ('sent', 'failed', 'delivered' - if gateway provides), `gateway_ref_id`, `timestamp`, `related_call_id` (nullable FK to `pbx-call`).
5. **Chatbot Related Schemas (`chatbot-messages`, `chatbot-sessions`, etc.):**
    * These seem well-defined for multi-tenant bot interactions. Ensure `tenantId` is consistently used.
6. **`audit-log.schema.ts`:**
    * Crucial for a cloud system. Ensure `tenantId` is captured for tenant-specific actions and can be null for system-wide actions.

## II. Frontend Pages/Views (Next.js `apps/dashboard`) - Event-Driven Updates

The dashboard will now primarily react to events received via a WebSocket connection that is fed by RabbitMQ messages, rather than direct WebSocket commands for all actions.

| Old Feature/View (Mapped to New) | New Next.js Page/Component(s) (`apps/dashboard`) | Core Data Entities (for display) | Primary Backend Interaction (Initial Load / Actions) | Real-time Updates Via |
| :------------------------------- | :------------------------------------------------- | :------------------------------- | :------------------------------------------------- | :-------------------- |
| **Operator Login**               | `/login`                                           | `Operators`, `Tenants`           | REST (AuthN/AuthZ service)                         | N/A                   |
| **Main Call List View**          | `/dashboard/[tenantId]/calls`                      | `PbxCall`, `Contacts`            | REST (initial load from `CallQueryService`), **RabbitMQ** (for actions like assign vehicle, which publishes an event) | WebSocket (fed by RabbitMQ: `call.created`, `call.updated`, `call.status.changed`) |
|  - Add New Call Modal            | Modal within `/calls`                              | `PbxCall`                        | REST (to `CallCommandService` to publish `call.create.request` event to RabbitMQ) | (Success/failure via WS) |
|  - Edit Call Form                | Modal or inline in `/calls`                        | `PbxCall`, `Vehicles`, `Areas`   | REST (to `CallCommandService` to publish `call.update.request` event) | (Success/failure via WS) |
| **Scheduled Calls View**         | `/dashboard/[tenantId]/scheduled`                  | `ScheduledCall`                  | REST (initial load), **RabbitMQ** (for actions)    | WebSocket (fed by RabbitMQ: `schedule.created`, `schedule.updated`, `schedule.deleted`) |
|  - Schedule New/Edit Modal       | Modal within `/scheduled`                          | `ScheduledCall`                  | REST (to `SchedulingCommandService` to publish events) | (Success/failure via WS) |
| **Search Calls / Results**       | `/dashboard/[tenantId]/search`                     | `PbxCall`, `Contacts`            | REST (to `CallQueryService`)                       | N/A (static result set) |
| **Daily Statistics View**        | `/dashboard/[tenantId]/statistics/daily`           | Aggregated Stats                 | REST (to `StatisticsQueryService`)                 | N/A (or periodic refresh) |
| **Mapping Modal**                | Modal                                              | `PbxCall`, External Geocoding    | REST (external geocoding), REST (to `CallCommandService` for GIS update event) | (Success/failure via WS) |
| **Send SMS Modal**               | Modal                                              | `PbxCall`, `Contacts`            | REST (to `MessagingCommandService` to publish `sms.send.request` event) | (Success/failure via WS) |
| **Notifications Area**           | Global Toast Component                             | `NotificationEvent` (from WS)    | N/A                                                | WebSocket (fed by RabbitMQ: `notification.user.new`, `system.alert`) |
| **Tenant Management** (New)      | `/admin/tenants`                                   | `Tenants`, `Bots`                | REST (to `TenantAdminService`)                     | WebSocket (for updates if admin is viewing) |
| **Bot Configuration** (New)      | `/dashboard/[tenantId]/bots`                       | `Bots`, `ChatbotConfigs`         | REST (to `BotAdminService`)                        | WebSocket (for updates) |

**Key Frontend Changes:**

* **Primary Data Fetching:** Initial page loads will likely use REST APIs to query "Query Services" in the backend.
* **Actions/Commands:** User actions (creating a call, assigning a vehicle) will send commands via REST to "Command Services" in the backend. These services will validate and then *publish events to RabbitMQ*.
* **Real-time Updates:** The frontend will maintain a single WebSocket connection. A dedicated NestJS service (`WebSocketPushService` or similar) will subscribe to relevant RabbitMQ topics/exchanges and push formatted updates to the connected dashboard clients. This decouples the dashboard from direct WebSocket command handling for many operations.

## III. Backend API/Service Layer (NestJS `apps/main-api`) - Decoupled Services & RabbitMQ

This is where the most significant architectural shift happens.

**Core Principles for Backend Services:**

* **Command Query Responsibility Segregation (CQRS) - Lite:**
  * **Command Services:** Handle requests that change state (e.g., `CallCommandService`, `ScheduleCommandService`). They validate commands, publish events to RabbitMQ, and DO NOT return data directly.
  * **Query Services:** Handle requests that retrieve data (e.g., `CallQueryService`, `TenantQueryService`). They read from the database (potentially read-replicas) and return data.
  * **Event Handlers/Workers:** Services that subscribe to RabbitMQ events and perform actions (e.g., update database, send notifications, call external APIs).
* **Event-Driven Architecture:** Changes in one part of the system (e.g., a PBX event creating a call) publish an event. Other interested services consume these events.

**NestJS Modules and Services with RabbitMQ Integration:**

| Domain                | NestJS Module(s)                               | Command Services (Publish to RabbitMQ) | Query Services (Read from DB) | Event Handlers/Workers (Consume from RabbitMQ) | REST Controllers (for Commands/Queries) | WebSocket Gateway (for Pushing Updates) | Key Data Entities |
| :-------------------- | :--------------------------------------------- | :------------------------------------- | :---------------------------- | :------------------------------------------- | :-------------------------------------- | :-------------------------------------- | :---------------- |
| **Authentication**    | `AuthModule`, `UsersModule`                    | -                                      | `UserQueryService`            | -                                            | `AuthController` (login, get_operators) | -                                       | `Operators`, `Users`, `Tenants`, `Roles` |
| **Call Management**   | `CallsModule`, `ContactsModule`, `PbxIntegrationModule` | `CallCommandService` (create, update, assign_vehicle, lock, cancel) | `CallQueryService`, `ContactQueryService` | `CallEventHandlerService` (updates DB from `pbx.call.created`, `call.vehicle.assigned` etc.), `PbxEventHandlerService` (processes raw PBX events, publishes domain events like `pbx.call.incoming`) | `CallController` (for commands), `CallQueryController` (for queries), `PbxEventController` (for PBX webhook) | `DashboardPushGateway` | `PbxCall`, `Contacts`, `Vehicles`, `Areas` |
| **Scheduling**        | `SchedulingModule`                             | `ScheduleCommandService` (create, update, delete, activate_now) | `ScheduleQueryService`        | `ScheduleActivationWorker` (consumes `schedule.activation.due`, publishes `call.create.request` for active call) | `ScheduleController`, `ScheduleQueryController` | `DashboardPushGateway` | `ScheduledCall` |
| **Messaging (SMS)**   | `MessagingModule`                              | `SmsCommandService` (send_sms_request) | -                             | `SmsSendingWorker` (consumes `sms.send.request`, calls SMS Gateway, publishes `sms.sent` or `sms.failed`), `IncomingSmsHandler` (consumes `sms.gateway.incoming`, publishes `sms.received.internal`) | `SmsController` (for commands), `SmsWebhookController` (for gateway status/incoming) | `DashboardPushGateway` | `SmsLog` |
| **Notifications**     | `NotificationsModule`                          | -                                      | -                             | `NotificationDispatchWorker` (consumes various events like `call.missed`, `user.alert`, formats and publishes `websocket.push.notification`) | -                                       | `DashboardPushGateway` (consumes `websocket.push.notification`) | - |
| **Statistics**        | `StatisticsModule`                             | -                                      | `StatisticsQueryService`      | `StatisticsAggregationWorker` (optional, for pre-calculating, consumes `call.completed`, etc.) | `StatisticsQueryController`             | -                                       | `PbxCall`, Stats Cache (Redis) |
| **Contacts**          | `ContactsModule`                               | `ContactCommandService` (update_name)  | `ContactQueryService`         | `ContactEventHandlerService` (updates contact aggregates from call events) | `ContactController`, `ContactQueryController` | `DashboardPushGateway` | `Contacts` |
| **Chatbots**          | `GramioBotModule`, `ChatbotLogicModule`        | `ChatbotCommandService` (e.g., send_message_to_user) | `ChatbotQueryService`       | `TelegramEventHandlerService`, `ViberEventHandlerService` (consume platform events, publish internal `chatbot.message.received`), `ChatbotMessageProcessor` (consumes internal events, applies NLP/logic, publishes `chatbot.send.reply`) | `BotWebhookController` (for Telegram, Viber webhooks) | - | `Bots`, `ChatbotMessages`, `ChatbotSessions`, `Tenants` |
| **Tenant Admin**      | `TenantAdminModule`                            | `TenantAdminCommandService`            | `TenantAdminQueryService`     | -                                            | `TenantAdminController`                 | -                                       | `Tenants`, `Bots`, `Roles` |
| **WebSocket Push**    | `WebSocketPushModule`                          | -                                      | -                             | `WebSocketPushService` (subscribes to specific RabbitMQ topics like `dashboard.updates.<tenantId>`, pushes to relevant WS clients) | `DashboardPushGateway` (manages connections) | - |

**RabbitMQ Event Storming (Examples):**

* **PBX Incoming Call:**
    1. PBX -> `PbxEventController` (REST)
    2. `PbxEventController` -> `PbxEventHandlerService`
    3. `PbxEventHandlerService` publishes `pbx.call.incoming` to RabbitMQ.
    4. `CallEventHandlerService` consumes `pbx.call.incoming`, creates `PbxCall` record, publishes `call.created` to RabbitMQ.
    5. `WebSocketPushService` consumes `call.created`, pushes update to relevant dashboards.
* **Operator Assigns Vehicle:**
    1. Dashboard (Next.js) -> `CallController` (REST POST `/calls/{id}/assign-vehicle`)
    2. `CallController` -> `CallCommandService.requestVehicleAssignment(callId, vehicleId, operatorId)`
    3. `CallCommandService` validates, publishes `call.assign-vehicle.request` to RabbitMQ.
    4. `CallEventHandlerService` consumes `call.assign-vehicle.request`, updates `PbxCall` (sets vehicles, timeassigned), publishes `call.vehicle.assigned`.
    5. `WebSocketPushService` consumes `call.vehicle.assigned`, pushes update.
    6. `SmsSendingWorker` (optional) consumes `call.vehicle.assigned`, sends "Vehicle Assigned" SMS.
* **Scheduled Call Activation:**
    1. `ScheduleActivationWorker` (triggered by cron or persistent queue check) finds due schedules.
    2. Publishes `schedule.activate.request` to RabbitMQ.
    3. `SchedulingService` (as an event handler) consumes `schedule.activate.request`, updates `ScheduledCall` status, publishes `call.create.request` (with details from scheduled call) to RabbitMQ.
    4. `CallEventHandlerService` consumes `call.create.request` (as if it's a new call from source 'schedule'), creates `PbxCall`, publishes `call.created`.
    5. `WebSocketPushService` pushes update.

**Decoupling Strategy:**

* **Services are self-contained:** A service should not directly call methods of another service in a different domain if it can be avoided. Instead, it publishes an event, and the other service consumes it.
* **RabbitMQ as the Event Bus:** All significant state changes or commands that require cross-domain action go through RabbitMQ.
* **Database as Single Source of Truth:** Event handlers update the database. Query services read from the database.
* **Frontend (Dashboard) is an Event Consumer:** It primarily reacts to state changes pushed via WebSockets, which are themselves triggered by backend services consuming RabbitMQ events. Actions from the dashboard initiate commands that result in events.

## IV. Key Decisions & Further Elaboration Needed (Revisited)

1. **Scheduled Calls Activation:** Use a robust job queue like BullMQ (backed by Redis) which can be signaled/managed via RabbitMQ messages, or have a dedicated NestJS worker service that polls the `scheduled_calls` table for due items and publishes activation events. *The latter is simpler to start with.*
2. **Contact Data Aggregation:** `ContactEventHandlerService` will consume `call.completed`, `call.canceled` events to update aggregated stats (locations, destinations, served/canceled counts) in the `contacts` table or its related history tables.
3. **Real-time Call State (Locks, Active Lists):**
    * **`lockedCalls`**: Can be managed by a `LockingService` using Redis (e.g., `SETEX lock:call:<callId> <operatorId> NX EX <timeout>`). This makes it distributed.
    * **`unansweredCalls`, `liveCalls`, `assignedCalls`**: These are essentially views/projections of the `PbxCall` table based on status and time. Query services can derive these. For very high-frequency updates or presence, Redis sets/hashes can still be used, updated by event handlers.
4. **Telegram Bot (`GramIO`) Integration with Core System:**
    * When a bot interaction needs to create a dispatch call: The `ChatbotMessageProcessor` (or similar service within `GramioBotModule`) will publish an event like `dispatch.request.chatbot` to RabbitMQ.
    * A `CallEventHandlerService` (or a dedicated `ChatbotDispatchHandlerService`) consumes this, creates a `PbxCall` with source `SOURCE_CHATBOT_TELEGRAM`, and the usual call lifecycle events follow.
    * Updates about the dispatch call (e.g., vehicle assigned, call completed) can be published back to a RabbitMQ topic that the `GramioBotModule` subscribes to, allowing it to send updates back to the Telegram user.
5. **Authentication for External Systems (PBX, SMS Gateway):**
    * Implement API Key authentication for NestJS REST endpoints receiving events from these systems. Store API keys securely (e.g., hashed in DB, managed via Vault).
    * IP Whitelisting as an additional layer.

This revised plan aligns better with your vision of a scalable, event-driven, cloud-native system. The key is the shift towards RabbitMQ for inter-service communication and decoupling, with the dashboard becoming a reactive client to these backend events.
