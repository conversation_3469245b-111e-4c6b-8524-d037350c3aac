import { pgEnum } from "drizzle-orm/pg-core";

// 1. Define enum values as the single source of truth
const PROMO_STATUS_VALUES = [
	"ACTIVE",
	"INACTIVE",
	"EXPIRED",
	"LIMIT_REACHED",
] as const;

// 2. Create the Drizzle ORM pgEnum for database schema definition
export const promoStatusEnum = pgEnum("promo_status", PROMO_STATUS_VALUES);

// 3. Define TypeScript union type for strict type safety
export type PromoStatusType = (typeof PROMO_STATUS_VALUES[number]);

// 4. Define TypeScript string enum for application logic
export enum PromoStatus {
	ACTIVE = "ACTIVE",
	INACTIVE = "INACTIVE",
	EXPIRED = "EXPIRED",
	LIMIT_REACHED = "LIMIT_REACHED",
}
