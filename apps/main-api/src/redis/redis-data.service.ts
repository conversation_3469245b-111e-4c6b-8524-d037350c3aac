import type { AppConfigService } from "@/config/app-config.service.js";
import { Inject, Injectable, Logger, type OnModule<PERSON><PERSON>roy } from "@nestjs/common";
import type { Redis } from "ioredis";
import { REDIS_CACHE_CLIENT, REDIS_PUBSUB_CLIENT } from "./redis.constants.js";
import type { IRedisService } from "./redis.interface.js";

@Injectable()
export class RedisDataService implements IRedisService, OnModuleDestroy {
	private readonly logger = new Logger(RedisDataService.name);

	constructor(
		@Inject(REDIS_CACHE_CLIENT) private readonly cacheClient: Redis,
		@Inject(REDIS_PUBSUB_CLIENT) private readonly pubSubClient: Redis,
		private readonly appConfigService: AppConfigService,
	) {}

	private getKey(tenantId: string, key: string): string {
		// Assuming redisPrefix is global, tenantId is used for namespacing within that prefix
		return `${this.appConfigService.redisPrefix}:${tenantId}:${key}`;
	}

	async get(tenantId: string, key: string): Promise<string | null> {
		return this.cacheClient.get(this.getKey(tenantId, key));
	}

	async set(tenantId: string, key: string, value: string | number, ttl?: number): Promise<void> {
		const actualKey = this.getKey(tenantId, key);
		if (ttl) {
			await this.cacheClient.set(actualKey, value, "EX", ttl);
		} else {
			await this.cacheClient.set(actualKey, value);
		}
	}

	async del(tenantId: string, key: string): Promise<void> {
		await this.cacheClient.del(this.getKey(tenantId, key));
	}

	async exists(tenantId: string, key: string | string[]): Promise<number> {
		const keys = Array.isArray(key) ? key.map(k => this.getKey(tenantId, k)) : [this.getKey(tenantId, key)];
		return this.cacheClient.exists(...keys);
	}

	async expire(tenantId: string, key: string, seconds: number): Promise<number> {
		return this.cacheClient.expire(this.getKey(tenantId, key), seconds);
	}

	async ttl(tenantId: string, key: string): Promise<number> {
		return this.cacheClient.ttl(this.getKey(tenantId, key));
	}

	async keys(tenantId: string, pattern: string): Promise<string[]> {
		// Note: KEYS can be slow in production. Use with caution.
		return this.cacheClient.keys(this.getKey(tenantId, pattern));
	}

	async incr(tenantId: string, key: string): Promise<number> {
		return this.cacheClient.incr(this.getKey(tenantId, key));
	}

	async decr(tenantId: string, key: string): Promise<number> {
		return this.cacheClient.decr(this.getKey(tenantId, key));
	}

	async hGet(tenantId: string, key: string, field: string): Promise<string | null> {
		return this.cacheClient.hget(this.getKey(tenantId, key), field);
	}

	async hSet(tenantId: string, key: string, field: string, value: string | number): Promise<number> {
		return this.cacheClient.hset(this.getKey(tenantId, key), field, value);
	}

	async hDel(tenantId: string, key: string, field: string | string[]): Promise<number> {
		const fields = Array.isArray(field) ? field : [field];
		return this.cacheClient.hdel(this.getKey(tenantId, key), ...fields);
	}

	async hGetAll(tenantId: string, key: string): Promise<Record<string, string>> {
		return this.cacheClient.hgetall(this.getKey(tenantId, key));
	}

	async hmSet(tenantId: string, key: string, data: Record<string, string | number>): Promise<"OK"> {
		return this.cacheClient.hmset(this.getKey(tenantId, key), data);
	}

	async hExists(tenantId: string, key: string, field: string): Promise<number> {
		return this.cacheClient.hexists(this.getKey(tenantId, key), field);
	}

	async hIncrBy(tenantId: string, key: string, field: string, increment: number): Promise<number> {
		return this.cacheClient.hincrby(this.getKey(tenantId, key), field, increment);
	}

	async sAdd(tenantId: string, key: string, members: string | number | (string | number)[]): Promise<number> {
		const actualMembers = Array.isArray(members) ? members : [members];
		return this.cacheClient.sadd(this.getKey(tenantId, key), ...actualMembers.map(String));
	}

	async sRem(tenantId: string, key: string, members: string | number | (string | number)[]): Promise<number> {
		const actualMembers = Array.isArray(members) ? members : [members];
		return this.cacheClient.srem(this.getKey(tenantId, key), ...actualMembers.map(String));
	}

	async sMembers(tenantId: string, key: string): Promise<string[]> {
		return this.cacheClient.smembers(this.getKey(tenantId, key));
	}

	async sIsMember(tenantId: string, key: string, member: string | number): Promise<number> {
		return this.cacheClient.sismember(this.getKey(tenantId, key), String(member));
	}

	async zAdd(tenantId: string, key: string, score: number, member: string | number): Promise<number> {
		return this.cacheClient.zadd(this.getKey(tenantId, key), score, String(member));
	}

	async zAddMultiple(tenantId: string, key: string, members: (string | number | { score: number; member: string | number })[]): Promise<number> {
		const args = members.flatMap(m => typeof m === 'object' ? [m.score, String(m.member)] : [m]);
		return this.cacheClient.zadd(this.getKey(tenantId, key), ...args);
	}

	async zRange(tenantId: string, key: string, start: number, stop: number, withScores?: "WITHSCORES"): Promise<string[]> {
		if (withScores) {
			return this.cacheClient.zrange(this.getKey(tenantId, key), start, stop, "WITHSCORES");
		}
		return this.cacheClient.zrange(this.getKey(tenantId, key), start, stop);
	}

	async zRem(tenantId: string, key: string, members: string | number | (string | number)[]): Promise<number> {
		const actualMembers = Array.isArray(members) ? members : [members];
		return this.cacheClient.zrem(this.getKey(tenantId, key), ...actualMembers.map(String));
	}

	async zScore(tenantId: string, key: string, member: string | number): Promise<string | null> {
		return this.cacheClient.zscore(this.getKey(tenantId, key), String(member));
	}

	async zIncrBy(tenantId: string, key: string, increment: number, member: string | number): Promise<string> {
		return this.cacheClient.zincrby(this.getKey(tenantId, key), increment, String(member));
	}

	async publish(channel: string, message: string): Promise<number> {
		return this.pubSubClient.publish(channel, message);
	}

	async subscribe(channel: string, callback: (channel: string, message: string) => unknown): Promise<void> {
		await this.pubSubClient.subscribe(channel);
		this.pubSubClient.on("message", (ch, msg) => {
			if (ch === channel) {
				callback(ch, msg);
			}
		});
	}

	async unsubscribe(channel: string): Promise<void> {
		await this.pubSubClient.unsubscribe(channel);
	}

	async checkHealth(): Promise<{ status: string; error?: string }> {
		try {
			await this.cacheClient.ping();
			await this.pubSubClient.ping();
			return { status: "ok" };
		} catch (e: any) {
			this.logger.error("Redis health check failed", e.message);
			return { status: "error", error: e.message };
		}
	}

	getCacheClient(): Redis {
		return this.cacheClient;
	}

	async onModuleDestroy() {
		// Clients are managed by ioredis and NestJS lifecycle via redis.providers.ts
		// No explicit disconnect here unless specific cleanup is needed.
		this.logger.log("RedisDataService destroyed. Clients managed by provider.");
	}
}