{"extends": "@repo/typescript-config/base.json", "compilerOptions": {"rootDir": "src", "outDir": "dist", "module": "NodeNext", "moduleResolution": "NodeNext", "target": "ES2021", "lib": ["ES2021"], "declaration": true, "declarationMap": true, "emitDeclarationOnly": false, "sourceMap": true, "composite": true, "noEmit": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "baseUrl": ".", "paths": {"#*": ["./src/*"]}, "types": ["node"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}