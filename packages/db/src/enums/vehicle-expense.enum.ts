import { pgEnum } from "drizzle-orm/pg-core";

const VEHICLE_EXPENSES_VALUES = [
	"TAX",
	"INSURANCE",
	"REGISTRATION",
	"MAINTENANC<PERSON>",
	"FUEL",
	"OTHER",
] as const;

export const vehicleExpenseEnum = pgEnum(
	"vehicle_expenses_type",
	VEHICLE_EXPENSES_VALUES,
);

export type VehicleExpensesType = (typeof VEHICLE_EXPENSES_VALUES)[number];

export enum VehicleExpenses {
	TAX = "TAX",
	INSURANCE = "INSURANCE",
	REGISTRATION = "REGISTRATION",
	MAINTENANCE = "MAINTENANCE",
	FUEL = "FUEL",
	OTHER = "OTHER",
}
