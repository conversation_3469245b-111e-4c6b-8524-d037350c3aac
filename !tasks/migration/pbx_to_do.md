# PBX Integration Requirements and Design

## Introduction

Here's what's needed regarding PBX integration, data from Asterisk/FreePBX, and how it connects:

### I. Data We Need from Asterisk/FreePBX for Each Call Event

To effectively manage calls in your new system, your `PbxIntegrationService` (which will receive events from Asterisk/FreePBX) needs to extract or be provided with the following information for various call lifecycle events:

1. **Tenant Identifier (`tenantId`):**
    * **CRUCIAL for Multi-Tenancy.** This is the most important piece of information that might not be standard in PBX events.
    * **How to get it:**
        * **DID/Trunk Mapping:** If different tenants have dedicated DIDs (Direct Inward Dialing numbers) or incoming SIP trunks, your `PbxIntegrationService` or a configuration layer can map the DID/trunk (present in the call event) to a `tenantId`.
        * **Channel Variables/Headers:** Asterisk can be configured to set custom channel variables (e.g., `X-Tenant-ID`) on calls, especially if they pass through a specific context in the dialplan. These variables can be read via AMI or passed by AGI.
        * **Source IP/Context:** Less reliable, but if tenants connect via distinct IP addresses or specific Asterisk contexts, this could be a hint.
    * **Action:** This needs to be designed into your PBX dialplan or integration script.

2. **Call Identifiers:**
    * **`UniqueID` (Asterisk):** The unique ID for an Asterisk channel/call leg. This will be your `externalPbxId` in `pbx-call.schema.ts`.
    * **`LinkedID` (Asterisk):** The `UniqueID` of the channel this channel is bridged to. Essential for tracking two-party conversations, transfers.

3. **Timestamps (UTC):**
    * Precise timestamp for *each event* (e.g., when ringing started, when answered, when hung up).

4. **Party Information:**
    * **`CallerIDNum`**: Calling party's number.
    * **`CallerIDName`**: Calling party's name (if available).
    * **`ConnectedLineNum`**: Number of the connected party (after answer).
    * **`ConnectedLineName`**: Name of the connected party.
    * **`DNID` (Dialed Number ID):** The number the caller originally dialed to reach the system.
    * **`Exten`**: The specific extension or number dialed within your PBX.

5. **Channel Information:**
    * **`Channel`**: The name of the Asterisk channel (e.g., `SIP/mytrunk-00001a2b`, `PJSIP/101-00001c3d`).
    * **`DestChannel`**: The channel it's connected to (for answered calls).

6. **Call Lifecycle Events & Statuses (from AMI or AGI):**
    * **`Newchannel`**: Indicates a new call leg has been created. Provides initial channel details.
        * *Maps to*: Initial `pbx-call` record creation, status `RINGING` (for inbound) or `DIALING` (for outbound).
    * **`Newstate`**: Channel state changes (e.g., `Ringing`, `Up`, `Down`).
        * `Up` state often signifies an answered call.
    * **`DialBegin` / `DialEnd`**: For tracking outgoing call attempts. `DialEnd` provides the disposition (e.g., `ANSWER`, `BUSY`, `NOANSWER`, `CANCEL`, `CONGESTION`).
        * *Maps to*: `pbx-call.status` updates.
    * **`BridgeEnter` / `BridgeLeave`**: When two channels are connected (call answered) or disconnected.
        * `BridgeEnter` is a strong indicator for `pbx-call.answeredAt`.
    * **`Hangup`**: Channel has been hung up.
        * Provides `Cause` and `Cause-txt` (e.g., "16" for Normal Clearing, "17" for User Busy).
        * *Maps to*: `pbx-call.endedAt`, `pbx-call.status` ('ENDED', 'MISSED', 'FAILED'), `pbx-call.duration`, `pbx-call.terminationReason`.
    * **`AgentCalled`, `AgentConnect`, `AgentComplete`, `AgentRingNoAnswer` (for Queues):**
        * If using Asterisk Queues, these events are vital for tracking which operator (`Agent`) handled a queue call.
        * `AgentConnect` signifies an operator answered.
        * *Maps to*: `pbx-call.operatorId`, `pbx-call.answeredAt`.
    * **`Hold`, `Unhold` (or `MusicOnHoldStart`, `MusicOnHoldStop`):**
        * *Maps to*: `pbx-call.metadata` or a dedicated status if granular hold tracking is needed.
    * **`BlindTransfer`, `AttendedTransfer`**:
        * Important for tracking if a call was transferred and to whom. This might involve creating new related `pbx-call` records or updating existing ones with transfer details.
    * **`DTMFBegin`, `DTMFEnd`**: If you need to capture DTMF tones (e.g., for IVR interactions that lead to a dispatch).

7. **Queue Information (if applicable):**
    * `QueueName`: The queue the call entered.
    * `Position`: Caller's position in the queue.
    * `HoldTime`/`WaitTime`: Time spent in queue.

8. **Recording Information (if call recording is enabled):**
    * Often provided in `Hangup` events or specific recording-related events (e.g., `MonitorStart`, `MonitorStop`).
    * `RecordingFile` or similar variable.
    * *Maps to*: `pbx-call.recordingUrl`.

### II. How to Get This Data from Asterisk/FreePBX

1. **Asterisk Manager Interface (AMI) - Recommended for Real-time Events:**
    * **Mechanism:** Your NestJS `PbxIntegrationService` will connect to Asterisk's AMI (typically TCP port 5038).
    * **Events to Listen For:** `Newchannel`, `Newstate`, `Hangup`, `BridgeEnter`, `BridgeLeave`, `DialBegin`, `DialEnd`, `AgentConnect`, `Hold`, `Unhold`, `DTMFEnd`, etc.
    * **Data Extraction:** AMI events are text-based key-value pairs. You'll parse these to get the fields listed above.
    * **Action:**
        * Implement an AMI client in `PbxIntegrationService` (e.g., using a Node.js library like `asterisk-manager` (from old backend) or a more modern equivalent like `asterisk-ami-client` or by writing a simple TCP socket handler).
        * Define parsers for each relevant AMI event to extract the necessary data points.
        * Crucially, figure out how to get `tenantId` associated with each event (see point I.1).

2. **Asterisk Gateway Interface (AGI) - For In-Dialplan Actions & Data Collection:**
    * **Mechanism:** AGI scripts are executed by Asterisk at specific points in the dialplan. These scripts can collect call variables and then make an HTTP POST request to a NestJS endpoint (e.g., `/webhooks/pbx/events` handled by `PbxEventController`).
    * **Use Cases:**
        * Sending a "call started" event with initial data as soon as a call hits a certain part of your dialplan.
        * Sending call completion data with custom dispositions set in the dialplan.
        * Passing `tenantId` if it's determined within the dialplan context.
    * **Action:** If AMI isn't sufficient or you need data from specific dialplan stages, write AGI scripts (e.g., in Perl, PHP, Node.js running on the PBX server) that POST to your NestJS API.

3. **Call Detail Records (CDRs):**
    * **Mechanism:** Asterisk logs CDRs after calls complete. These are usually stored in a database (e.g., `asteriskcdrdb.cdr`) or CSV files.
    * **Use Cases:** Primarily for billing, reporting, and historical analysis. Not ideal for real-time dashboard updates but can be used to enrich `pbx-call` records post-call or for reconciliation.
    * **Action:** If needed, a batch process in NestJS could periodically read CDRs to update/verify call durations or other final details.

4. **FreePBX Considerations:**
    * FreePBX is a GUI and management layer on top of Asterisk. It uses standard Asterisk mechanisms like AMI and AGI.
    * You might find FreePBX-specific AMI events or dialplan variables if you're using its queue modules, etc.
    * Check FreePBX documentation for any specific integration points or modules that might facilitate event pushing.

### III. Connecting PBX Data to the Dashboard & System (Event Flow)

This refines the previous event flow:

1. **PBX Event Occurs** (e.g., new incoming call, call answered).
2. **Data Sent to NestJS:**
    * **AMI:** `PbxIntegrationService`'s AMI listener receives the raw AMI event.
    * **AGI:** AGI script on PBX POSTs data to `PbxEventController`.
3. **Initial Processing (`PbxIntegrationService` / `PbxEventController`):**
    * Parse the raw PBX data.
    * **Determine `tenantId`** (CRITICAL). If it cannot be determined, the event might be logged to an "unassigned" pool or rejected.
    * Transform the raw data into one of your defined, structured `Pbx...Event` types (from `@repo/types`).
    * Publish this structured event to a "raw PBX events" topic on RabbitMQ (e.g., exchange `pbx_events_ex`, routing key `pbx.raw.<eventType>.<tenantId>`).
        * *Payload Example:* `PbxIncomingCallEvent`
4. **Core Call Logic (`CallEventHandlerService` - consumes from RabbitMQ):**
    * Subscribes to topics like `pbx.raw.#`.
    * Receives the structured `Pbx...Event`.
    * **Idempotency Check:** Use `eventId` from the payload to prevent reprocessing if RabbitMQ redelivers.
    * **Database Interaction (Drizzle):**
        * If `PbxIncomingCallEvent`:
            * Look up/create `users` record for `fromPhoneNumber` (scoped by `tenantId`).
            * Create a new record in `pbx-call` table with status `RINGING`, `source` `PBX_INCOMING`, `tenantId`, `userId`, `externalPbxId`, timestamps, etc.
        * If `PbxCallAnsweredEvent`:
            * Find existing `pbx-call` record by `externalPbxId` and `tenantId`.
            * Update `status` to `ANSWERED`, set `answeredAt`, map `answeredByExtension` to an `operatorId` (FK to `operators` or `users`).
        * If `PbxCallTerminatedEvent`:
            * Find existing `pbx-call`.
            * Update `status` (e.g., `ENDED`, `MISSED`), set `endedAt`, `duration`, `terminationReason`.
    * **Publish Domain Event:** After DB update, publish a more specific domain event to a different RabbitMQ exchange (e.g., `call_domain_events_ex`):
        * `call.created.tenantId.callId` (payload: full `PbxCall` object from DB)
        * `call.status_changed.tenantId.callId` (payload: `callId`, new `status`, `timestamp`)
        * `call.answered.tenantId.callId` (payload: `callId`, `operatorId`, `answeredAt`)
5. **WebSocket Push (`WebSocketPushService` - consumes domain events from RabbitMQ):**
    * Subscribes to topics like `call.created.#`, `call.status_changed.#`, etc.
    * Receives the domain event.
    * Formats a UI-friendly payload.
    * Pushes the update to the Next.js dashboard clients connected to the relevant `tenantId` room/topic.
6. **Operator Actions from Dashboard (e.g., Dial, Hold, Transfer):**
    * Dashboard UI action -> REST API call to a `CallCommandController`.
    * `CallCommandController` -> `CallCommandService`.
    * `CallCommandService` publishes a command event to RabbitMQ (e.g., `call.action.dial_request.tenantId`).
    * A `PbxActionHandlerService` (worker) consumes this command.
    * `PbxActionHandlerService` calls `PbxIntegrationService` methods.
    * `PbxIntegrationService` sends the appropriate command to Asterisk via AMI (e.g., `Originate`, `Hold`, `Redirect`).
    * Asterisk then generates new AMI events (e.g., `OriginateResponse`, `Hold`, `Transfer`) which flow back through steps 1-5, updating the dashboard.

### IV. Missing Pieces & Actions (Focus on PBX Integration)

1. **`PbxIntegrationService` Implementation:**
    * **AMI Client:** Choose and implement a Node.js AMI library or raw TCP socket handling. Connect to Asterisk, handle authentication, listen to events.
    * **Event Parsers:** For each relevant AMI event (`Newchannel`, `Hangup`, etc.), create a parser to extract key data points.
    * **Tenant ID Resolver:** Implement the logic to determine `tenantId` for each incoming PBX event. This might involve querying a DB table that maps DIDs/Trunks to tenants.
    * **Publish to RabbitMQ:** Use `RascalService` to publish the structured `Pbx...Event` types.
    * **AMI Actions:** Implement methods for `originateCall`, `hangupCall`, `holdCall`, `transferCall`, etc., that send commands to Asterisk via AMI.
2. **`PbxEventController` (REST Webhook):**
    * If using AGI for some events, this controller will receive POSTs from AGI scripts.
    * It should validate the incoming data and then pass it to `PbxIntegrationService` to publish to RabbitMQ.
    * **Security:** Secure this endpoint (API key, IP whitelist).
3. **`CallEventHandlerService` Logic:**
    * Flesh out the handlers for all `Pbx...Event` types to correctly update the `pbx-call` table and other related entities (like `users` if a new caller).
    * Implement logic for mapping PBX extensions to `operatorId`.
4. **Asterisk/FreePBX Dialplan Configuration:**
    * Ensure AMI user has necessary read/write permissions for events and actions.
    * If using AGI, configure dialplan to execute AGI scripts at appropriate points.
    * **Crucially, implement how `tenantId` will be passed or determined for calls.**
        * Example: In Asterisk, when a call comes in on a specific DID:

            ```
            exten => _X.,1,NoOp(Call for DID ${EXTEN} on channel ${CHANNEL(channeltype)}/${CHANNEL(peername)})
            exten => _X.,n,Set(TENANT_ID=tenant_abc) ; Based on DID or trunk
            exten => _X.,n,UserEvent(TenantCallStart,TenantID: ${TENANT_ID},UniqueID: ${UNIQUEID},CallerIDNum: ${CALLERID(num)}) ; Custom AMI event
            ; or call AGI script
            exten => _X.,n,AGI(send_call_event.sh,${TENANT_ID},${UNIQUEID},${CALLERID(num)})
            ```

5. **Error Handling & Retries for PBX Events:**
    * If `PbxIntegrationService` fails to publish to RabbitMQ.
    * If `CallEventHandlerService` fails to process an event from RabbitMQ (use DLQs and retry mechanisms in Rascal).

By focusing on these areas, you'll establish a robust pipeline for getting PBX data into your new system and enabling real-time updates on the dashboard. The key is the clear separation of concerns: PBX integration for raw events, RabbitMQ for event transport, dedicated services for processing, and a push service for UI updates.
