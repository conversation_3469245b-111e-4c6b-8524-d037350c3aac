{"extends": "@repo/typescript-config/base.json", "compilerOptions": {"jsx": "preserve", "jsxImportSource": "solid-js", "outDir": "./dist", "rootDir": "./src", "baseUrl": "./src", "paths": {"@/*": ["*"]}, "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "sourceMap": true, "noEmit": false, "composite": true, "lib": ["ES2022", "DOM", "DOM.Iterable"], "types": ["node", "vite/client"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "test", "**/*spec.ts"]}