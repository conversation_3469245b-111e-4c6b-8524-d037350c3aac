# **I. Address Critical Backend Errors First**

**1. Fix Module Resolution & Core Drizzle Setup (`@monorepo/db`)**

Many errors stem from how the `@monorepo/db` package is structured and consumed.

* **Issue**: `Cannot find module '@monorepo/db/src/schema'` in `apps/bot/src/services/database.service.ts` and Drizzle schema awareness (`Property 'bots' does not exist on type 'DrizzleTypeError<"Seems like the schema generic is missing..."`).
* **Explanation**:
  * The `packages/db/package.json` correctly defines `exports: { ".": "./src/index.ts" }`. This means consumers should import from `'@monorepo/db'`.
  * The Drizzle instance in `packages/db/src/index.ts` needs to be initialized *with* your schemas to be type-aware. The compiled output `packages/db/src/index.js` shows `exports.db = (0, bun_sql_1.drizzle)({ client: exports.sql, casing: "snake_case" });` which is missing the schema.
* **Suggestion**:
    1. Modify `packages/db/src/index.ts`:

        ```typescript
        import { SQL } from "bun";
        import { drizzle }sfrom "drizzle-orm/bun-sql";
        import { config } from "./config";
        import * as schema from './schema'; // Import all your schemas

        export const sql = new SQL(config.DATABASE_URL);

        // Pass the imported schemas to the drizzle function
        export const db = drizzle(sql, { schema, casing: "snake_case" });

        // Optionally, export the fully typed db instance for consumers
        export type DbType = typeof db;

        // Re-export all schemas so consumers can import them from '@repo/db'
        export * from './schema';
        ```

    2. In `apps/bot/src/services/database.service.ts`:
        * Import directly from the package entry point:

            ```typescript
            // At the top of database.service.ts
            import { db, bots, tenants, userBotRoles, roles, telegramUsers, type DbType } from '@repo/db'; // Adjust if telegramUsers is not directly exported
            import { eq, and } from 'drizzle-orm';
            ```

        * Your `DatabaseService` constructor should then inject this `DbType`:

            ```typescript
            // In DatabaseService constructor
            constructor(
                // private readonly configService: ConfigService, // Keep if used elsewhere
                @Inject('DATABASE') private readonly injectedDb: DbType, // 'DATABASE' token from your DatabaseModule
            ) {
                this.db = injectedDb; // Assign the injected, fully-typed db instance
            }
            ```

        * Remove dynamic imports like `await import('@monorepo/db/src/schema')`. Use the static imports above.
            For example, `getActiveBots` becomes:

            ```typescript
            async getActiveBots(): Promise<BotConfigFromDB[]> { // Ensure BotConfigFromDB is imported or defined
                const dbInstance = this.getDb(); // or just use this.db
                const botsFromDB = await dbInstance.query.bots.findMany({
                    where: eq(bots.isEnabled, true),
                });
                return botsFromDB as BotConfigFromDB[]; // Ensure BotConfigFromDB matches the selected fields
            }
            ```

    3. Ensure your `apps/bot/tsconfig.json` correctly resolves `@monorepo/db`. If you're using path aliases, they must be set up. However, with correct package exports and standard Node module resolution (especially in a monorepo managed by Bun/PNPM/Yarn workspaces), direct imports like `@monorepo/db` should work.

**2. Revise Core Bot Context Types (`apps/bot/src/types/bot-context-fixed.ts`)**

This is crucial as many errors relate to incorrect context types. The main issue is a circular dependency in `AppBot` and `AppBaseContext` definitions.

* **Issue**: `Type 'AppBaseContext' does not satisfy the constraint 'DeriveDefinitions'`, and related errors like `Property 'X' does not exist on type 'AppBaseContext'`.
* **Explanation**: In GramIO, `Bot<E, D extends DeriveDefinitions>`, `D` represents the *properties added by the `.derive()` method*, not the full context type. `Context<B extends BotLike>` then automatically includes `B['Derive']`.
* **Suggestion**: Refactor `apps/bot/src/types/bot-context-fixed.ts` (assuming this is the intended canonical version, otherwise apply to `bot-context.ts` and remove the duplicate):

    ```typescript
    // src/types/bot-context-fixed.ts (or your canonical bot-context.ts)
    import {
      Context,
      Bot as GramioBot,
      MessageContext, // etc. import other specific contexts you need
      UpdateName,
      // ... other GramIO types
    } from "gramio";
    import type { i18n as I18nInstanceType } from "../shared/locales"; // Corrected import
    import type { TenantFromDB, BotConfigFromDB } from "./database"; // Assuming these are correctly defined

    // 1. Define Error Definitions
    export type AppBotErrorDefinitions = {
      [K in UpdateName | "global"]: Error;
    } & {
      "prompt-cancel": Error;
    };

    // 2. Define Tenant and Bot Info structures (as in your plan)
    export interface TenantInfo extends Pick<TenantFromDB, 'id' | 'name'> {}
    export interface BotInstanceInfo extends Pick<BotConfigFromDB, 'id' | 'botUsername'> {}

    // 3. Define the extensions that your .derive() function will add to the context
    export interface AppDeriveExtensions {
      readonly t: ReturnType<typeof I18nInstanceType['buildT']>; // Corrected i18n type usage
      tenant?: TenantInfo;
      botInfo?: BotInstanceInfo;
      tenantId?: string; // Keep if you use it directly, though tenant.id is better
      userRoles?: string[];
      tenantBotApiClient?: GramioBot<AppBotErrorDefinitions, AppDeriveExtensions>['api']; // API client for the specific bot
    }

    // 4. Define your AppBot type using these extensions
    // AppBot's second generic is AppDeriveExtensions
    export type AppBot = GramioBot<AppBotErrorDefinitions, AppDeriveExtensions>;

    // 5. Define your base augmented context.
    // Context<AppBot> will automatically include AppDeriveExtensions.
    export type AppBaseContext = Context<AppBot>;

    // --- Scene-Specific Context Typing (as in your plan, ensure they use AppBot) ---
    // (Your existing scene context types seem mostly okay, just ensure they use the corrected AppBot)
    // For example:
    export type AppMessageContext = MessageContext<AppBot> & {
        scene?: any; // from scenes plugin, adjust type if possible
        mediaGroup?: AppMessageContext[];
        mediaGroupId?: string;
    };

    export type AppCommandContext = AppMessageContext & {
        args: string | null;
    };
    // ... other specific contexts like AppCallbackQueryContext, etc.

    // Type for context entering the derive function (useful for typing derive's input)
    export type ContextBeforeAppDerive = Context<GramioBot<any, any>> & { // Base GramIO context
        from?: { languageCode?: string; id?: number };
        chat?: { id: number };
        update: any; // TelegramUpdate
        updateId: number;
        updateType: UpdateName;
        // session?: any; // if session plugin runs before your derive
        // scene?: any;   // if scenes plugin runs before your derive
    };
    ```

  * **Important**: Ensure `TenantContextExtension` in your original plan is merged into `AppDeriveExtensions`. The key is that `AppDeriveExtensions` must accurately reflect *exactly* what your `derive` function returns.

**3. Update `BotProcessingService` with Corrected Types**

* **`universalGramioProcessor` type**:

    ```typescript
    public universalGramioProcessor!: AppBot; // Should now be correct
    ```

* **`tenantBotApiClients` type**:

    ```typescript
    private tenantBotApiClients = new Map<string, AppBot['api']>();
    ```

* **`derive` function signature**:

    ```typescript
    .derive(async (ctx: ContextBeforeAppDerive) // Use the more specific input type
      : Promise<Partial<AppDeriveExtensions>> => { // Return Partial of your extensions
        // ... your existing derive logic ...
        const derivedProps: Partial<AppDeriveExtensions> = {
            t: I18nInstance.buildT(ctx.from?.languageCode) // Use your actual i18n instance
        };
        // ...
        // Ensure all properties set on derivedProps are defined in AppDeriveExtensions
        return derivedProps;
    })
    ```

* **`getSessionKey` in `session` plugin**:
    The context `ctx` here will be an instance of a specific context type (e.g., `MessageContext`, `CallbackQueryContext`) *after* the `derive` function has run. So it should be typed as `AppBaseContext` (which includes `AppDeriveExtensions`).

    ```typescript
    getSessionKey: (ctx: AppBaseContext) => {
        // ctx now has tenantId, botInfo if your derive function added them
        const botId = ctx.botInfo?.id;
        const telegramIdentifier = ctx.senderId ?? ctx.chatId; // GramIO provides senderId and chatId on derived contexts

        if (!botId || !telegramIdentifier) {
            this.logger.warn(`[getSessionKey] Cannot generate session key. Bot ID: ${botId}, Identifier: ${telegramIdentifier}`);
            return `global:${ctx.updateId}`;
        }
        return `${ctx.tenantId}:${botId}:${telegramIdentifier}`;
    },
    ```

* **`onError` context**:
    `onError((errCtx: AppBaseContext) => { ... })` should be correct if `AppBaseContext` is fixed. Properties like `errCtx.error` are part of GramIO's error context structure, but `errCtx.botInfo` or `errCtx.tenantId` would come from your `AppDeriveExtensions`.

**4. Handler Services and Registrations**

* Once `AppBot` and specific context types like `AppCommandContext` are corrected based on the revised `AppBaseContext` and `AppDeriveExtensions`, the errors in handler `initialize` methods and `registerHandlers` (e.g., `this.botInstance.command(...)`) should largely resolve.
* The main thing is that the function signature you provide (e.g., `this.handleStartCommand.bind(this)`) must match what GramIO expects for that handler type, given the (now correctly typed) `AppBot` instance.

**II. Address Other Backend Issues**

* **`apps/bot/src/generate-metadata.ts`**:
  * `Cannot find module '../../libs/metadata-generator/src'`: Verify this path. Is `libs/metadata-generator` a local package within your monorepo? If so, how is it built and exposed? If it's meant to be an external NPM package, it's missing. This seems like a custom script, so ensure its dependencies and paths are correct relative to your project structure.
* **Decorators (`RequireRoles`)**:
  * The `else` clause warning is a linting suggestion for code style.
  * Ensure `ctx: any` in the decorator is eventually compatible with your `AppCommandContext` or other relevant contexts if you intend to access `ctx.userRoles`, `ctx.tenantBotApiClient` etc. safely.

**III. Address `apps/mini-app` Errors Later**

Focus on getting the bot backend stable first. For the mini-app:

1. **Dependencies**: Run `bun install` (or your package manager's install command) within the `apps/mini-app` directory or the monorepo root to ensure `@solidjs/router`, `@tonconnect/ui`, and `eruda` are installed.
2. **Telegram SDK for SolidJS**:
    * `classNames`, `parseInitData`, `$debug` not found: The `@telegram-apps/sdk-solid` API might have changed, or these are not exported as you expect. Consult its documentation.
    * Deprecated methods (`mount`): Look for alternatives in the SDK's documentation.
3. **Vite Config**:
    * `process.env.HTTPS && mkcert()`: `mkcert()` is likely a function from `vite-plugin-mkcert`. Ensure it's imported. The error "This expression is not callable" suggests `mkcert` might be undefined if `process.env.HTTPS` is false. You might need to adjust the conditional logic or ensure `mkcert` is always a function (even if a no-op one).
4. **Type Errors in Components**: These are mostly downstream effects of the SDK issues or incorrect prop types.

**IV. General Advice**

* **One `bot-context` file**: Decide whether `bot-context.ts` or `bot-context-fixed.ts` is the source of truth and remove/consolidate the other to avoid confusion. The plan refers to `src/types/bot-context.ts`.
* **Iterate and Test**: Make changes step-by-step. After fixing the Drizzle setup, check if `DatabaseService` errors are gone. After fixing context types, check `BotProcessingService` and then one handler service.
* **Clean Build Artifacts**: Regularly, and especially after major type or dependency changes, remove `dist`, `.turbo`, and `node_modules` (in the relevant package or root) and reinstall/rebuild.
* **TypeScript Version**: Ensure your VS Code and terminal are using the same TypeScript version specified in your `package.json`.
* **Review Plan vs. Code**: Double-check that the implementation in files like `BotProcessingService` and handler services aligns with the logic described in your multi-tenant plan, especially regarding the use of `tenantBotApiClient` for API calls and data scoping.

This is a lot, but by tackling module resolution and core types first, you should see a significant reduction in the number of errors. Good luck! Let me know if you have specific questions as you work through these steps.
