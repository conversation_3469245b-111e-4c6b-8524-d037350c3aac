{"bot.passenger.rateDriver.enteredLog": "User {{userId}} entered rate driver scene", "bot.passenger.rateDriver.prompt": "How would you rate your driver?", "bot.passenger.rateDriver.star1": "⭐", "bot.passenger.rateDriver.star2": "⭐⭐", "bot.passenger.rateDriver.star3": "⭐⭐⭐", "bot.passenger.rateDriver.star4": "⭐⭐⭐⭐", "bot.passenger.rateDriver.star5": "⭐⭐⭐⭐⭐", "bot.passenger.rateDriver.leftLog": "User {{userId}} left rate driver scene", "bot.passenger.rateDriver.thankYou": "Thank you for your feedback!", "bot.passenger.rateDriver.additionalFeedbackPrompt": "Would you like to provide additional feedback?", "bot.passenger.rateDriver.yes": "Yes", "bot.passenger.rateDriver.no": "No", "bot.passenger.rateDriver.thankYou5Star": "Thank you for your 5-star rating!", "bot.passenger.rateDriver.provideFeedbackCb": "Please provide your feedback", "bot.passenger.rateDriver.typeFeedbackPrompt": "Please type your feedback below:", "bot.passenger.rateDriver.thankYouCb": "Thank you for your rating", "bot.passenger.rateDriver.callbackLog": "User {{userId}} triggered callback: {{data}}", "bot.passenger.rateDriver.ratedLog": "User {{userId}} rated driver with {{rating}} stars", "bot.passenger.rateDriver.ratedCb": "You rated the driver {{rating}} stars", "bot.passenger.rateDriver.feedbackLog": "User {{userId}} sent feedback: {{feedback}}", "bot.passenger.rideStatus.enteredLog": "User {{userId}} entered ride status scene", "bot.passenger.rideStatus.statusPrompt": "Your ride status:", "bot.passenger.rideStatus.refreshStatus": "Refresh Status", "bot.passenger.rideStatus.cancelRide": "Cancel Ride", "bot.passenger.rideStatus.contactDriver": "Contact Driver", "bot.passenger.rideStatus.backToMenu": "Back to Menu", "bot.passenger.rideStatus.interactPrompt": "Use the buttons below to interact with your ride:", "bot.passenger.rideStatus.leftLog": "User {{userId}} left ride status scene", "bot.passenger.rideStatus.callbackLog": "User {{userId}} triggered callback: {{data}}", "bot.passenger.rideStatus.textLog": "User {{userId}} sent message: {{text}}", "bot.passenger.rideStatus.refreshingStatusCb": "Refreshing ride status...", "bot.passenger.rideStatus.driver5minAway": "Your driver is 5 minutes away.", "bot.passenger.rideStatus.cancellingRideCb": "Cancelling your ride...", "bot.passenger.rideStatus.rideCancelled": "Your ride has been cancelled.", "bot.passenger.rideStatus.contactingDriverCb": "Contacting your driver...", "bot.passenger.rideStatus.connectingWithDriver": "Connecting you with your driver...", "bot.passenger.rideStatus.returningToMenuCb": "Returning to menu...", "bot.passenger.rideStatus.unknownActionCb": "Unknown action. Please use the buttons.", "bot.passenger.rideStatus.returningToMenu": "Returning to the main menu...", "bot.passenger.rideStatus.driverFound": "Driver found! Driver is on the way to your location.", "bot.passenger.rideStatus.statusUpdateErrorLog": "Error sending status update: {{error}}", "bot.passenger.bookRideWizard.rideTypePrompt": "Please select your ride type:", "bot.passenger.bookRideWizard.providedDropoffAddressLog": "User {{userId}} provided dropoff address: {{address}}", "bot.passenger.bookRideWizard.selectedRideTypeLog": "User {{userId}} selected ride type: {{rideType}}", "bot.passenger.bookRideWizard.confirmPrompt": "Please confirm your ride details:\n\nPickup: {{pickup}}\nDropoff: {{dropoff}}\nRide Type: {{rideType}}\nEstimated Fare: {{estimatedFare}} MKD\n\nWould you like to confirm this ride? (Yes/No)", "bot.passenger.bookRideWizard.confirmedRideLog": "User {{userId}} confirmed ride", "bot.passenger.bookRideWizard.creatingRideLog": "Creating ride: {{rideData}}", "bot.passenger.bookRideWizard.lookingForDrivers": "Looking for drivers...", "bot.passenger.bookRideWizard.cancelRideButton": "Cancel Ride", "bot.passenger.bookRideWizard.rideBooked": "Your ride has been booked! We are looking for drivers nearby.", "bot.passenger.bookRideWizard.rejectedRideLog": "User {{userId}} rejected ride", "bot.passenger.bookRideWizard.bookingCancelled": "Ride booking cancelled. What would you like to do next?", "bot.passenger.bookRideWizard.enteredLog": "User {{userId}} entered book ride wizard", "bot.passenger.bookRideWizard.pickupPrompt": "Please share your pickup location by:\n1. Sending your current location using the button below\n2. Typing an address\n3. Sending a location pin", "bot.passenger.bookRideWizard.locationButtonPrompt": "Please share your location using the button below.", "bot.passenger.bookRideWizard.sharedPickupLocationLog": "User {{userId}} shared pickup location: {{latitude}}, {{longitude}}", "bot.passenger.bookRideWizard.dropoffPrompt": "Great! Now please share your destination location:", "bot.passenger.bookRideWizard.providedPickupAddressLog": "User {{userId}} provided pickup address: {{address}}", "bot.passenger.confirmYes": "Yes", "bot.passenger.confirmYesShort": "Y", "bot.passenger.confirmedLog": "User {{userId}} confirmed registration information", "bot.passenger.savingUserDataLog": "Saving user data: {{userData}}", "bot.passenger.registrationCompleteMenu": "Registration complete! What would you like to do next?", "bot.passenger.bookRide": "Book a Ride", "bot.passenger.myRides": "My Rides", "bot.passenger.settings": "Settings", "bot.passenger.help": "Help", "bot.passenger.registrationSuccess": "You are now registered as a passenger!", "bot.passenger.rejectedLog": "User {{userId}} rejected registration information", "bot.passenger.restartPrompt": "Let's start over. Please select your preferred language:", "bot.passenger.registrationWelcome": "Welcome to passenger registration, {{firstName}}!\n\nPlease select your preferred language:", "bot.passenger.selectedLanguageLog": "User {{userId}} selected language: {{language}}", "bot.passenger.phonePrompt": "Please share your phone number:", "bot.passenger.contactButtonPrompt": "Please share your contact using the button below.", "bot.passenger.sharedPhoneLog": "User {{userId}} shared phone number: {{phone}}", "bot.passenger.emailPrompt": "Please enter your email address:", "bot.passenger.providedEmailLog": "User {{userId}} provided email: {{email}}", "bot.passenger.confirmPrompt": "Please confirm your information:\n\nName: {{name}}\nPhone: {{phone}}\nEmail: {{email}}\nLanguage: {{language}}\n\nIs this correct? (Yes/No)", "passengers": {"registerPrompt": "To book a ride, you must register as a passenger. Use /passenger to register.", "alreadyRegistered": "You are already registered as a passenger.", "registrationSuccess": "Registration successful! You can now book rides.", "bookingStarted": "Let's start booking your ride.", "bookingCancelled": "Booking cancelled. What would you like to do next?", "notFound": "Passenger not found. Please register first."}}