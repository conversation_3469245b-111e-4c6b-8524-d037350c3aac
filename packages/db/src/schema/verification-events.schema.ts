import { relations } from "drizzle-orm";
import {
	jsonb,
	pgTable,
	timestamp,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";
import { verificationMethodEnum } from "../enums/verification-method.enum.js";
import { verificationStatusEnum } from "../enums/verification-status.enum.js";
import { chatbotUsers } from "./chatbot-users.schema.js";

export const verificationEvents = pgTable("verification_event", {
	id: uuid("id").primaryKey().defaultRandom(),
	chatbotUserId: uuid("chatbot_user_id")
		.references(() => chatbotUsers.id)
		.notNull(),
	method: verificationMethodEnum("method").notNull(),
	status: verificationStatusEnum("status").notNull(),
	reference: varchar("reference", { length: 255 }),
	initiatedAt: timestamp("initiated_at").notNull().defaultNow(),
	completedAt: timestamp("completed_at"),
	expiresAt: timestamp("expires_at"),
	metadata: jsonb("metadata").$type<{
		provider_reference?: string;
		error_message?: string;
		verification_code?: string;
		attempts?: number;
		ip_address?: string;
		device_info?: string;
	}>(),
});

export const verificationEventsRelations = relations(
	verificationEvents,
	({ one }) => ({
		chatbotUser: one(chatbotUsers, {
			fields: [verificationEvents.chatbotUserId],
			references: [chatbotUsers.id],
		}),
	}),
);
