# Database Schema Review

The goal is to identify:

1. **Existing Schemas that Map Well:** Tables that are already suitable or very close.
2. **Schemas Needing Expansion/Refinement:** Tables that exist but need additional fields or modified relationships to support old/new features.
3. **Missing Schemas:** Entirely new tables required for features from the old system or the new vision.
4. **Data Migration Considerations:** High-level thoughts on moving data from an old (presumably different) schema.

Here's a breakdown:

---

## Database Schema Review & Expansion Plan (PostgreSQL via Drizzle)

This plan reviews the existing Drizzle schemas in `packages/db/src/schema/` and outlines necessary additions or modifications to support the full feature set of the reimplemented dispatch system, including legacy features and new chatbot/ride-hailing capabilities.

**Guiding Principles:**

* **PostgreSQL as Source of Truth:** Prioritize storing core, relational data in PostgreSQL.
* **Redis for Ephemeral/Cache:** Use Redis for caching, session state, rate limiting, counters, active lists (like `lockedCalls`, potentially `unansweredCalls` if performance demands), and job queues (if not using RabbitMQ's delayed exchanges directly).
* **Multi-Tenancy:** Ensure `tenantId` is a foreign key in almost all relevant tables.
* **Clear Relationships:** Define explicit relations in Drizzle for data integrity and ORM convenience.

### I. Existing Schemas Mapping Well (Minimal Changes Needed)

These schemas from your `packages/db/src/schema/` seem largely well-suited for their intended purpose in the new system:

1. **`tenants.schema.ts`**: Good foundation.
2. **`users.schema.ts`**: Generic user model.
3. **`roles.schema.ts`**: For RBAC.
4. **`user_tenant.schema.ts`**: Links users to tenants with roles. This is crucial for operator/admin access.
5. **`bots.schema.ts`**: For managing different chatbot instances per tenant.
    * `token` field: Ensure it's always stored encrypted (as per `encryptBotToken` utility).
6. **`chatbot_providers.schema.ts`, `chatbot_configs.schema.ts`, `chatbot_instances.schema.ts`, `chatbot_sessions.schema.ts`, `chatbot_messages.schema.ts`, `chatbot_users.schema.ts`, `chatbot_events.schema.ts`**: A comprehensive set for chatbot functionality.
7. **`telegram_*.schema.ts` (users, chats, messages, chat_members, user_settings)**: Specific to Telegram integration.
8. **`vehicle.schema.ts`**: For vehicle details.
9. **`area.schema.ts`**: For geographical zones.
10. **`audit_log.schema.ts`**: Essential for tracking.
11. **`i18n_translation.schema.ts`**: If DB-backed translations are chosen.
12. **`tenant_settings.schema.ts`, `tenant_localization.schema.ts`, `tenant_billing_profile.schema.ts`**: Good for tenant-specific configurations.
13. **Ride-Hailing Schemas (`rides.schema.ts`, `ride-order.schema.ts`, `ride-event.schema.ts`, `ride-rating.schema.ts`, `dispatch-assignment.schema.ts`, `driver-vehicle.schema.ts`, `promos.schema.ts`, `promotion.schema.ts`, `payment.schema.ts`, `invoice.schema.ts`, `wallet.schema.ts`)**: These are new features and seem to provide a solid base for the "Uber-like" system.

### II. Schemas Needing Expansion or Refinement

1. **`operators.schema.ts`**:
    * **Current:** Links to `user_tenant_id`, has `firstName`, `lastName`, `phone`, `status`.
    * **Needs:**
        * The old system had an `operators` table with `id`, `name`, `disabled`. Your new `operators` schema is more of an extension of a `user` who has an "OPERATOR" role within a tenant via `user_tenant`. This is a good model.
        * Ensure the `status` enum (`operator-status.enum.ts`) covers all necessary states (e.g., `ACTIVE`, `INACTIVE`, `DISABLED` from old system, plus any new ones).
        * The old system's operator login used a simple ID. The new system will likely use the `users.id` (UUID) or `users.phone`/`users.email` for login, then associate with the `operators` record via `user_tenant`.
    * **Action:** Confirm `operatorStatusEnum` is sufficient. The structure itself is good.

2. **`pbx-call.schema.ts` (This is the new "Calls" table)**:
    * **Current (from `drizzle/meta/0000_snapshot.json` as `pbx_call`):** `id`, `tenant_id`, `user_id` (nullable, for customer), `operator_id` (nullable), `ride_id` (nullable), `direction`, `status`, `duration`, `recording_url`, `started_at`, `ended_at`.
    * **Needs to incorporate/map fields from the old system's `Call` model:**
        * `timestamp` (initial event time, your `started_at` might be this or `timeanswered`): Clarify. `started_at` seems like the initial ring/event time.
        * `timeanswered` (timestamp): **Add this field.** Crucial for call lifecycle.
        * `timeassigned` (timestamp, when vehicle assigned/expected): **Add this field.**
        * `phone` (customer's phone): **Add this field.** `user_id` can link to the customer, but storing the actual phone number on the call record is often useful for quick display and history if the user changes their primary phone.
        * `location` (text): **Add this field.**
        * `destination` (text): **Add this field.**
        * `area` (varchar, FK to `area.schema.ts`): **Add `area_id` (UUID, FK) and potentially denormalized `area_name` (varchar).**
        * `arrivaltime` (integer, minutes for assigned vehicle): **Add this field.**
        * `source` (enum, e.g., 'PBX_INCOMING', 'MANUAL', 'CHATBOT_TELEGRAM', 'SCHEDULED_ACTIVATION'): Your `call-direction.enum.ts` has INBOUND/OUTBOUND. Expand `source.enum.ts` to be more granular. **Add `source` field using this expanded enum.**
        * `extension` (integer, PBX extension): **Add this field.**
        * `operator_ids` (array of operator UUIDs who handled it): Your current `operator_id` is singular. For a history of touches, an array or a separate junction table (`call_operator_assignments`) might be better. For simplicity, `operator_id` (last/primary) and a JSONB `metadata` field for historical operator IDs could work initially.
            * **Action:** Decide on single `operator_id` (FK to `operators.id` or `users.id` if operator is a user type) vs. array/junction.
        * `comment` (text): **Add this field.**
        * `gisdata` (jsonb for lat/lon of location/destination): **Add this field.**
        * `vehicles` (text array for vehicle numbers/IDs): **Add this field.**
        * `metadata` (jsonb for other dynamic data like `locked_by_operator_id`, `is_sending_sms`, old system's `served`/`canceled` history for *this specific call if different from contact history*): **Add this field.**
    * **Enums:**
        * `call-direction.enum.ts`: `INBOUND`, `OUTBOUND`. Good.
        * `call-status.enum.ts`: `RINGING`, `ANSWERED`, `MISSED`, `ENDED`, `FAILED`. This is good for PBX state. You'll also need application-level statuses that might be derived or stored in `metadata` or a separate field (e.g., `PENDING_ASSIGNMENT`, `VEHICLE_ASSIGNED`, `CUSTOMER_PICKED_UP`, `COMPLETED_UNCONFIRMED`, `CANCELED_BY_USER`, `CANCELED_BY_OPERATOR`, `NO_VEHICLE_AVAILABLE`). The `ride-status.enum.ts` (`SEARCHING`, `ASSIGNED`, `PICKED_UP`, `COMPLETED`, `CANCELLED`) is excellent for the ride-hailing aspect and might cover many of these.
    * **Action:** Perform a detailed field-by-field mapping from the old `Call` model to `pbx-call.schema.ts` and add missing fields. Clarify the meaning of `started_at` vs. `timestamp` vs. `timeanswered`.

3. **`message.schema.ts` & `chatbot-messages.schema.ts`**:
    * `message.schema.ts` seems generic. `chatbot-messages.schema.ts` is specific to chatbots.
    * **Need for SMS Log:** If operator-initiated SMS (via PBX gateway, not necessarily a chatbot) needs to be logged persistently (recommended for audit), decide if `message.schema.ts` is suitable or if a dedicated `sms_log.schema.ts` is better.
        * An `sms_log` table would have: `id`, `tenant_id`, `direction` ('outbound_operator', 'inbound_reply'), `from_number` (operator's line or system number), `to_number` (customer), `content`, `status` ('sent', 'failed', 'delivered'), `gateway_ref_id`, `timestamp`, `operator_user_id` (FK to `users`), `related_pbx_call_id` (FK).
    * **Action:** Clarify if `message.schema.ts` will store operator-sent SMS or if a new schema is needed.

### III. Missing Schemas (to be created in `packages/db/src/schema/`)

1. **`scheduled_calls.schema.ts`**:
    * **Purpose:** To store master records of scheduled calls, especially recurring ones.
    * **Fields:**
        * `id` (UUID, PK)
        * `tenant_id` (UUID, FK to `tenants`)
        * `created_by_operator_id` (UUID, FK to `users` where role is operator)
        * `customer_user_id` (UUID, FK to `users`, optional if just a phone number)
        * `phone_number` (VARCHAR, if no `customer_user_id`)
        * `base_call_details` (JSONB, containing location, destination, area, comment, vehicles, gisdata, etc. – essentially a snapshot of what to create when activated)
        * `scheduled_timestamp` (TIMESTAMP, for one-time schedules)
        * `arrival_time_offset_minutes` (INTEGER, e.g., notify 10 mins before)
        * `repeat_pattern` (JSONB, e.g., `{"type": "weekly", "days":, "time": "08:00"}` or `{"type": "daily", "time": "09:00"}`)
        * `next_activation_timestamp` (TIMESTAMP, calculated, indexed for polling by worker)
        * `last_activated_timestamp` (TIMESTAMP, nullable)
        * `is_active` (BOOLEAN, default true)
        * `status` (VARCHAR, e.g., 'PENDING', 'PROCESSED_ONCE', 'RECURRING_ACTIVE', 'DISABLED')
        * `created_at`, `updated_at`
    * **Note:** When a scheduled call activates, it would trigger the creation of a record in `pbx-call.schema.ts` (or `ride-order.schema.ts` if it's directly a ride).

2. **`contacts.schema.ts` (Optional, if not relying solely on `users` and Redis for contact-specific history):**
    * **Purpose:** To store aggregated history and preferences for phone numbers within a tenant, separate from the generic `users` table if a user can have multiple phone numbers or if non-user phone numbers need tracking.
    * **Fields:**
        * `id` (UUID, PK)
        * `tenant_id` (UUID, FK to `tenants`)
        * `phone_number` (VARCHAR, Indexed, Unique per tenant)
        * `name_override` (VARCHAR, nullable, if operator sets a custom name for this number within this tenant)
        * `linked_user_id` (UUID, FK to `users`, nullable)
        * `last_call_timestamp` (TIMESTAMP)
        * `total_calls` (INTEGER)
        * `total_served_calls` (INTEGER)
        * `total_canceled_calls` (INTEGER)
        * `total_no_vehicle_calls` (INTEGER)
        * `preferred_language` (VARCHAR, nullable)
        * `default_pickup_location` (TEXT, nullable)
        * `default_destination` (TEXT, nullable)
        * `notes` (TEXT, nullable)
        * `metadata` (JSONB for frequent locations/destinations if not using Redis sorted sets: `{"frequent_locations": [{"address": "...", "count": N}], "frequent_destinations": [...]}`)
        * `created_at`, `updated_at`
    * **Alternative:** Keep `users.display_name` for name. Aggregate history on-the-fly for display and use Redis for caching frequent locations/destinations (as per old system). This might be simpler if a "contact" is always a "user". The main driver for a separate `contacts` table is if you need to track history for phone numbers *not* yet registered as users, or if users can have multiple phone numbers with distinct histories. Your current `users.phone` is unique, which simplifies things.

3. **`operator_performance_stats.schema.ts` (Optional, for pre-aggregated stats):**
    * **Purpose:** If on-the-fly stats calculation becomes too slow for reporting.
    * **Fields:** `id`, `tenant_id`, `operator_user_id`, `date`, `shift_id` (optional), `answered_calls`, `missed_calls`, `outbound_calls`, `total_call_duration`, `avg_handle_time`, `rides_dispatched`, `sms_sent`, etc.
    * This would be populated by a batch job or an event consumer listening to call/ride completion events.

### IV. Redis Usage (Mapping Old Logic to New)

* **`lockedCalls` (Old: in-memory `lockedCalls` Map)**
  * **New:** Redis HASH `locks:call <call_id> <operator_user_id>` with an expiry (SETEX). A `LockingService` in NestJS would manage this.
* **`unansweredCalls` (Old: in-memory Map `unansweredCalls`)**
  * **New:** Could be a Redis SET per tenant `unanswered_calls:<tenantId>` storing `call_id`s, or derived from `pbx-call` table by querying for `status='RINGING'` and recent timestamps. For high-volume, Redis might be better for quick "is this number currently ringing?" checks.
* **`liveCalls` (Old: in-memory Map `liveCalls`)**
  * **New:** Derived from `pbx-call` table (`status='ANSWERED'` and `ended_at IS NULL`). Redis caching for dashboards if needed.
* **`assignedCalls` (Old: in-memory Map `assignedCalls` vehicle -> call)**
  * **New:** This is effectively part of the `ride-order.schema.ts` (a ride with a `driverId` and `vehicleId` and status `ASSIGNED` or `PICKED_UP`). Or, if for non-ride calls, a Redis HASH `assigned_vehicles:<tenantId> <vehicle_id> <call_id>`.
* **Contact History (Locations/Destinations - Old: Redis Sorted Sets)**
  * **New:**
    * Option A: Store in PostgreSQL within `contacts.schema.ts` (e.g., in a JSONB field or related tables) and cache heavily in Redis.
    * Option B: Continue using Redis Sorted Sets `contact_history:<tenantId>:<phone>:locations` and `...:destinations`, updated by event handlers. This is good for "top N" queries.
* **Daily/Detailed Statistics (Old: Redis Hashes)**
  * **New:** Continue this pattern. `stats.js` logic can be adapted. Keys like `stats:<tenantId>:daily:YYYY-MM-DD` (HASH) and `stats:<tenantId>:operator:YYYY-MM-DD:<operatorId>` (HASH).
* **Chatbot Session State:** Redis is excellent for this. Your `ChatbotSessionService` would use it.

### V. Data Migration Considerations

* **Operators:** Migrate from old `operators` table to new `users` (with role 'OPERATOR' in `user_tenant`) and `operators` (extension table).
* **Calls:** Migrate historical calls from old `calls` table to new `pbx-call` table. This will require significant data transformation:
  * Mapping old status/source values to new enums.
  * Converting `operator_id` (singular) to `operator_ids` (array or linking to the new operator structure).
  * Extracting GIS data if it was embedded differently.
* **Scheduled Calls:** If old schedules are in Redis, extract, transform, and load into new `scheduled_calls` table or new Redis structure.
* **Contact History (Redis):** If possible, migrate Redis sorted sets for locations/destinations. If names were stored, migrate to `users.display_name` or `contacts.name_override`.

**Summary of Actions for Database Schema:**

1. **Finalize `pbx-call.schema.ts`**: Add all missing fields from the old `Call` model, clarify timestamp meanings, and ensure appropriate enums and FKs are used.
2. **Define `scheduled_calls.schema.ts`**: Decide on PostgreSQL vs. hybrid storage and create the schema.
3. **Define `contacts.schema.ts` OR Formalize Redis Strategy:** Decide how detailed contact history (beyond basic user info) will be stored and define the schema or Redis structure.
4. **Create `sms_log.schema.ts`**: For logging operator-initiated SMS.
5. **Review all `tenantId` FKs**: Ensure they are present and correctly configured (`NOT NULL`, `ON DELETE CASCADE` where appropriate).
6. **Review Enums**: Ensure all enums in `packages/db/src/enums/` cover the states and types needed from the old system plus new requirements. Expand `source.enum.ts` for `pbx-call`.
7. **Indexes**: Add necessary indexes for performance on all new/modified tables, especially on FKs, `tenantId`, status fields, and timestamps used in common queries. Your Drizzle snapshot shows some indexes; review comprehensively.

This detailed review should help you align your existing Drizzle schemas with the full scope of the reimplementation. The next step would be to actually implement these schema changes and additions in your Drizzle files and generate the migrations.
