import { pgEnum } from "drizzle-orm/pg-core";

// 1. Define enum values as the single source of truth
const PBX_TENANT_ASSOCIATION_VALUES = [
	"SINGLE_TENANT",
	"MULTI_TENANT_DID",
	"MULTI_TENANT_CHANNEL_VAR",
	"MULTI_TENANT_QUEUE_NAME",
	"MULTI_TENANT_EXTENSION_PREFIX",
	"MULTI_TENANT_CHANNEL_NAME_PREFIX",
	"MULTI_TENANT_CALLER_ID_PREFIX",
] as const;

// 2. Create the Drizzle ORM pgEnum for database schema definition
export const pbxTenantAssociationTypeEnum = pgEnum(
	"pbx_tenant_association_type",
	PBX_TENANT_ASSOCIATION_VALUES,
);

// 3. Define TypeScript union type for strict type safety
export type PbxTenantAssociationType =
	(typeof PBX_TENANT_ASSOCIATION_VALUES)[number];

// 4. Define TypeScript string enum for application logic
export enum PbxTenantAssociation {
	SINGLE_TENANT = "SINGLE_TENANT",
	MULTI_TENANT_DID = "MULTI_TENANT_DID",
	MULTI_TENANT_CHANNEL_VAR = "MULTI_TENANT_CHANNEL_VAR",
	MULTI_TENANT_QUEUE_NAME = "MULTI_TENANT_QUEUE_NAME",
	MULTI_TENANT_EXTENSION_PREFIX = "MULTI_TENANT_EXTENSION_PREFIX",
	MULTI_TENANT_CHANNEL_NAME_PREFIX = "MULTI_TENANT_CHANNEL_NAME_PREFIX",
	MULTI_TENANT_CALLER_ID_PREFIX = "MULTI_TENANT_CALLER_ID_PREFIX",
}
