// apps/main-api/src/websocket/websocket-push.service.ts (Conceptual)
import { Injectable, Logger } from "@nestjs/common";
import type { PbxCall } from "@repo/db"; // Import PbxCall from @repo/db
import type {
	CallLoggedPayload,
	WsPushNotificationPayload,
} from "@repo/types";
import { v4 as uuidv4 } from "uuid";
import { <PERSON>Hand<PERSON> } from "@/rabbitmq/handler.decorator"; // Import the decorator
import type { AckOrNack } from "rascal";

// Assuming you have a WebSocket Gateway service or instance to inject
// import { AppWebSocketGateway } from './app-websocket.gateway'; // Example gateway service
// import { Server } from 'socket.io'; // Or your specific WebSocket library Server type

@Injectable()
// @WebSocketGateway() // If this service also manages the WebSocket server
export class WebSocketPushService {
	private readonly logger = new Logger(WebSocketPushService.name);
	/**
	 * This method is triggered by the RabbitMQLoader due to the @RabbitHandler decorator.
	 */
	@RabbitHandler({ subscription: "sub_websocket_push_service" }) // Matches the subscription name in rascal-definitions.ts
	async handleWebSocketPushEvent(
		eventPayload: CallLoggedPayload, // Expecting CallLoggedPayload for call.created event
		ackOrNack: AckOrNack,
	): Promise<void> {
		const { eventId, tenantId, data: pbxCallData } = eventPayload;
		const eventType = "call.created"; // Determine this based on the event or routing key if more generic // Corrected: tenantId is string | null, but WsPushNotificationPayload requires string. Need to handle null tenantId case.

		this.logger.log(
			`[${eventId}] Consumed domain event '${eventType}' for tenant ${tenantId} to prepare WebSocket push. Call ID: ${pbxCallData.id}`,
		);

		// 1. Format the PbxCall data for the UI
		const uiPayload = this.formatCallForUi(pbxCallData);

		// Handle the case where tenantId is null (e.g., system-wide events, though unlikely for call.created)
		if (!tenantId) {
			this.logger.warn(`[${eventId}] Cannot push WebSocket notification for event '${eventType}': tenantId is null.`);
			ackOrNack(); // Acknowledge as unprocessable for UI push
			return;
		}
		const targetRoom = `tenant:${tenantId}:dashboard`; // Example target room // tenantId is now guaranteed non-null here

		// 2. Construct WsPushNotificationPayload
		const pushNotification: WsPushNotificationPayload<any> = {
			pushId: uuidv4(),
			timestamp: new Date().toISOString(), // timestamp is string
			tenantId: tenantId, // tenantId is guaranteed non-null here
			correlationId: eventId,
			type: eventType, // e.g., 'call.created'
			title: "New Incoming Call", // Example title
			message: `Incoming call from ${pbxCallData.fromPhoneNumber || "Unknown"}`, // Example message
			payload: uiPayload,
			targetRoom,
		};

		try {
			// 1. Use the injected WebSocket server/gateway to emit the event
			//    The event name 'ui_notification' is a suggestion, use what your frontend expects.
			// if (this.server) {
			//   if (targetRoom) {
			//     this.server.to(targetRoom).emit('ui_notification', content);
			//   } else if (targetUserIds && targetUserIds.length > 0) {
			//     // Logic to map userIds to socketIds/clients and emit
			//     // This might involve a separate service tracking user-socket mapping
			//     this.logger.warn(`Targeting specific users (${targetUserIds.join(',')}) is not implemented yet.`);
			//   } else {
			//     this.server.to(targetRoom).emit('ui_notification', pushNotification); 
			//   }
			//   this.logger.debug(`[${pushNotification.pushId}] Emitted WebSocket event 'ui_notification' for type '${eventType}'.`);
			// } else {
			//   this.logger.warn(`[${pushNotification.pushId}] WebSocket server not available to push notification.`);
			// }

			// Example logging if WebSocket server is not available
			this.logger.log(
				`[${pushNotification.pushId}] Prepared push event '${eventType}'. WebSocket emission logic commented out. Payload: ${JSON.stringify(pushNotification)}`,
			);

			ackOrNack(); // Acknowledge successful processing
		} catch (error) {
			this.logger.error(
				`[${pushNotification.pushId}] Error processing domain event '${eventType}' for WebSocket push:`, error);
			const nackError = error instanceof Error ? error : new Error(String(error));
			ackOrNack(nackError, [{ strategy: "nack", requeue: false }]); // Nack and don't requeue
		}
	}

	// Helper to format PbxCall/ScheduledCall for UI consumption
	// This helper might be moved to a service responsible for preparing UI payloads
	private formatCallForUi(
		call: PbxCall & { scheduledActivationTime?: string },
	): any {
		// Example: return a subset of fields or a transformed object
		return {
			id: call.id,
			status: call.status,
			startTime: call.startedAt,
			pickup: (call.pbxRawDetails as any)?.pickupAddress, // Ensure pbxRawDetails structure is consistent or handle null/undefined
			dropoff: (call.pbxRawDetails as any)?.dropoffAddress, // Ensure pbxRawDetails structure is consistent or handle null/undefined
			notes: (call.pbxRawDetails as any)?.notes,
			scheduledActivationTime: call.scheduledActivationTime,
			// Add other fields relevant to the UI
		};
	}
}
