This file is a merged representation of a subset of the codebase, containing specifically included files, combined into a single document by <PERSON>omix.

<file_summary>
This section contains a summary of this file.

<purpose>
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.
</purpose>

<file_format>
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  - File path as an attribute
  - Full contents of the file
</file_format>

<usage_guidelines>
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.
</usage_guidelines>

<notes>
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Only files matching these patterns are included: apps/main-api/src/rabbitmq/**/*.{ts,tsx,js,jsx,json,css,scss,html,yaml,yml}
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Files are sorted by Git change count (files with more changes are at the bottom)
</notes>

</file_summary>

<directory_structure>
apps/
  main-api/
    src/
      rabbitmq/
        dto/
          create-rabbit-m-q.dto.ts
          update-rabbit-m-q.dto.ts
        rabbit-m-q.controller.ts
        rabbit-m-q.module.ts
        rabbit-m-q.service.ts
</directory_structure>

<files>
This section contains the contents of the repository's files.

<file path="apps/main-api/src/rabbitmq/dto/create-rabbit-m-q.dto.ts">
// apps/main-api/src/rabbitmq/dto/create-rabbitmq.dto.ts

import { IsString, IsInt, IsOptional } from 'class-validator';

export class CreateRabbitMQDto {
  @IsString()
  name!: string;

  @IsInt()
  @IsOptional()
  age?: number;
}
</file>

<file path="apps/main-api/src/rabbitmq/dto/update-rabbit-m-q.dto.ts">
import { PartialType } from '@nestjs/mapped-types';
import { CreateRabbitMQDto } from '@/rabbitmq/dto/create-rabbit-m-q.dto';

export class UpdateRabbitMQDto extends PartialType(CreateRabbitMQDto) {}
</file>

<file path="apps/main-api/src/rabbitmq/rabbit-m-q.controller.ts">
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Patch,
  Post,
} from '@nestjs/common';
import type { CreateRabbitMQDto } from './dto/create-rabbit-m-q.dto';
import type { UpdateRabbitMQDto } from './dto/update-rabbit-m-q.dto';
import type { RabbitMQService } from './rabbit-m-q.service';

@Controller('rabbit-m-qs')
export class RabbitMQController {
  constructor(private readonly rabbitMQService: RabbitMQService) {}

  @Post()
  create(@Body() createRabbitMQDto: CreateRabbitMQDto) {
    return this.rabbitMQService.create(createRabbitMQDto);
  }

  @Get()
  findAll() {
    return this.rabbitMQService.findAll();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.rabbitMQService.findOne(+id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateRabbitMQDto: UpdateRabbitMQDto) {
    return this.rabbitMQService.update(+id, updateRabbitMQDto);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    // TODO: Implement actual removal logic in RabbitMQService
 return `This action removes a #${id} rabbitMQ`;
  }
}
</file>

<file path="apps/main-api/src/rabbitmq/rabbit-m-q.module.ts">
import { RabbitMQController } from "./rabbit-m-q.controller";
import { Module } from "@nestjs/common";
import { RabbitMQService } from "./rabbit-m-q.service";
import { ClientsModule, Transport } from "@nestjs/microservices";
import { ConfigModule } from "@/config/config.module"; // Assuming you have a ConfigModule
import { AppConfigService } from "@/config/app-config.service"; // Assuming you have an AppConfigService
import { RABBITMQ_SERVICE } from "@/types/bot.constants";

@Module({
	imports: [
		ConfigModule, // Make sure ConfigModule is imported
		ClientsModule.registerAsync([
			{
				name: RABBITMQ_SERVICE, // Use the constant
				imports: [ConfigModule], // Import ConfigModule here too if AppConfigService needs it
				useFactory: (configService: AppConfigService) => ({
					transport: Transport.RMQ,
					options: {
						urls: [configService.rabbitMQUrl], // Get URL from AppConfigService
						queue: configService.rabbitMQDefaultQueue || "default_queue", // Example: get queue name
						// persistent: true, // Example: make messages persistent
						queueOptions: {
							durable: true, // Example: make queue durable
						},
					},
				}),
				inject: [AppConfigService],
			},
		]),
	],
	controllers: [RabbitMQController],
	providers: [RabbitMQService],
	exports: [RabbitMQService, ClientsModule], // Export ClientsModule if other modules need to inject the client
})
export class RabbitMQModule {}
</file>

<file path="apps/main-api/src/rabbitmq/rabbit-m-q.service.ts">
import { Inject, Injectable, Logger, type OnModuleInit } from '@nestjs/common';
import type { CreateRabbitMQDto } from '@/rabbitmq/dto/create-rabbit-m-q.dto';
import type { UpdateRabbitMQDto } from '@/rabbitmq/dto/update-rabbit-m-q.dto';
import type { ClientProxy } from '@nestjs/microservices';
import { RABBITMQ_SERVICE } from '@/types/bot.constants';
import { firstValueFrom } from 'rxjs';

@Injectable()
export class RabbitMQService implements OnModuleInit {
  private readonly logger = new Logger(RabbitMQService.name);

  constructor(
    @Inject(RABBITMQ_SERVICE) private readonly client: ClientProxy,
  ) {}

  async onModuleInit() {
    try {
      await this.client.connect();
      this.logger.log('RabbitMQ client connected successfully');
    } catch (err) {
      this.logger.error('Failed to connect RabbitMQ client', err);
    }
  }

  async create(createRabbitMQDto: CreateRabbitMQDto) {
    // TODO: Implement actual creation logic
    console.log('Creating RabbitMQ DTO:', createRabbitMQDto);
    // Example: sending a message (event or command)
    // For an event (fire and forget):
    // this.client.emit('item_created', createRabbitMQDto);
    // For a command (expects a response):
    return firstValueFrom(
      this.client.send({ cmd: 'create_item' }, createRabbitMQDto),
    );
  }

  findAll() {
    return firstValueFrom(this.client.send({ cmd: 'find_all_items' }, {}));
  }

  findOne(id: number) {
    return firstValueFrom(this.client.send({ cmd: 'find_item_by_id' }, { id }));
  }

  async update(id: number, updateRabbitMQDto: UpdateRabbitMQDto) {
    return firstValueFrom(this.client.send({ cmd: 'update_item' }, { id, ...updateRabbitMQDto }));
  }
}
</file>

</files>
