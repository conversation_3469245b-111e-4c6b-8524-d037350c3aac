import { relations } from "drizzle-orm";
import {
	date,
	index,
	numeric,
	pgTable,
	text,
	timestamp,
	uuid,
} from "drizzle-orm/pg-core";
import { vehicleExpenseEnum } from "../enums/vehicle-expense.enum.js";
import { tenants } from "./tenants.schema.js";
import { vehicles } from "./vehicle.schema.js";

export const vehicleExpenses = pgTable(
	"vehicle_expense",
	{
		id: uuid("id").primaryKey().defaultRandom(),
		tenantId: uuid("tenant_id")
			.references(() => tenants.id, { onDelete: "cascade" })
			.notNull(),
		vehicleId: uuid("vehicle_id")
			.references(() => vehicles.id, { onDelete: "cascade" })
			.notNull(),
		expenseType: vehicleExpenseEnum("expense_type").notNull(),
		date: date("date").notNull(),
		description: text("description").notNull(),
		cost: numeric("cost", { precision: 10, scale: 2 }).notNull(),
		notes: text("notes"),
		createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
	},
	(table) => [
		index("vehicle_expense_tenant_id_idx").on(table.tenantId),
		index("vehicle_expense_vehicle_id_idx").on(table.vehicleId),
		index("vehicle_expense_type_idx").on(table.expenseType),
		index("vehicle_expense_date_idx").on(table.date),
	],
);

export const vehicleExpensesRelations = relations(vehicleExpenses, ({ one }) => ({
	tenant: one(tenants, { fields: [vehicleExpenses.tenantId], references: [tenants.id] }),
	vehicle: one(vehicles, { fields: [vehicleExpenses.vehicleId], references: [vehicles.id] }),
}));

export type VehicleExpense = typeof vehicleExpenses.$inferSelect;
export type NewVehicleExpense = typeof vehicleExpenses.$inferInsert;