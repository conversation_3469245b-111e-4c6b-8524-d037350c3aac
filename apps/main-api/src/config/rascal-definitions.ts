// apps/main-api/src/config/rascal-definitions.ts
import type { BrokerConfig } from "rascal";

export const rascalDefinitions: BrokerConfig = {
	vhosts: {
		"/": {
			connection: {
				// URL will be injected by AppConfigService from environment variables
				// Ensure your AppConfigService provides this (e.g., this.appConfigService.rabbitMQUrl)
			},
			exchanges: {
				system_events_ex: { assert: true, type: "topic" },
				tenant_events_ex: { assert: true, type: "topic" }, // For tenant-specific lifecycle, config changes
				pbx_events_ex: { assert: true, type: "topic" }, // Raw events from PBX integration
				call_domain_events_ex: { assert: true, type: "topic" }, // Domain events for calls/rides
				schedule_domain_events_ex: { assert: true, type: "topic" },
				messaging_domain_events_ex: { assert: true, type: "topic" },
				chatbot_platform_events_ex: { assert: true, type: "topic" }, // Raw events from chatbot platforms
				chatbot_domain_events_ex: { assert: true, type: "topic" }, // Processed chatbot domain events
				dashboard_push_events_ex: { assert: true, type: "topic" }, // Events for WebSocket push service
				dead_letter_ex: { assert: true, type: "fanout" }, // Single DLX for simplicity
			},
			queues: {
				// Worker Queues (examples)
				q_worker_pbx_event_processor: {
					assert: true,
					options: { durable: true, deadLetterExchange: "dead_letter_ex" },
				},
				q_worker_call_command_processor: {
					// Processes commands like create_call_request
					assert: true,
					options: { durable: true, deadLetterExchange: "dead_letter_ex" },
				},
				q_worker_schedule_activation: {
					assert: true,
					options: { durable: true, deadLetterExchange: "dead_letter_ex" },
				},
				q_worker_sms_sender: {
					assert: true,
					options: {
						durable: true,
						deadLetterExchange: "dead_letter_ex",
						arguments: { "x-max-priority": 10 },
					}, // Example with priority
				},
				q_worker_chatbot_telegram_ingest: {
					assert: true,
					options: { durable: true, deadLetterExchange: "dead_letter_ex" },
				},
				q_worker_chatbot_domain_processor: {
					assert: true,
					options: { durable: true, deadLetterExchange: "dead_letter_ex" },
				},
				// Queue for WebSocketPushService
				q_websocket_push_service: {
					assert: true,
					options: { durable: true, deadLetterExchange: "dead_letter_ex" }, // Durable if you don't want to lose UI updates on restart
				},
				// Central Dead Letter Queue
				q_dead_letter: {
					assert: true,
					options: { durable: true },
				},
			},
			bindings: {
				// PBX Events
				bind_pbx_events_to_processor: {
					source: "pbx_events_ex",
					destination: "q_worker_pbx_event_processor",
					bindingKey: "pbx.#", // All PBX events
				},
				// Call Commands & Domain Events
				bind_call_commands_to_processor: {
					source: "call_domain_events_ex", // Assuming commands are also events
					destination: "q_worker_call_command_processor",
					bindingKey: "call.*.requested.#", // e.g., call.creation.requested.tenant123
				},
				bind_call_domain_events_to_websocket_push: {
					source: "call_domain_events_ex",
					destination: "q_websocket_push_service",
					bindingKey: "call.*.created.#", // Only push created/updated events
				},
				bind_call_domain_events_updated_to_websocket_push: {
					source: "call_domain_events_ex",
					destination: "q_websocket_push_service",
					bindingKey: "call.*.updated.#",
				},
				// SMS Events
				bind_sms_send_request_to_sender: {
					source: "messaging_domain_events_ex",
					destination: "q_worker_sms_sender",
					bindingKey: "sms.send.request.#", // e.g. sms.send.request.tenant123
				},
				// ... other bindings for schedule, chatbot, notifications ...

				// DLQ Binding
				bind_dlx_to_dlq: {
					source: "dead_letter_ex",
					destination: "q_dead_letter",
				},
			},
			publications: {
				// Define publications for each event type, using dynamic routing keys for multi-tenancy (using functions for routingKey)
				pub_pbx_event: {
					exchange: "pbx_events_ex",
					routingKey:
						"pbx.${eventType}${tenantId ? '.' + tenantId : '.global'}", // tenantId might be null for global PBX events initially
				},
				pub_call_domain_event: {
					exchange: "call_domain_events_ex",
					routingKey:
						"${domain}.${action}.${tenantId}${entityId ? '.' + entityId : ''}",
					// e.g., call.created.tenant123.callXYZ or call.creation_requested.tenant123
				},
				pub_schedule_domain_event: {
					exchange: "schedule_domain_events_ex",
					routingKey:
						"schedule.${action}.${tenantId}${scheduleId ? '.' + scheduleId : ''}",
				},
				pub_messaging_domain_event: {
					exchange: "messaging_domain_events_ex",
					routingKey: "${entity}.${action}.${tenantId}", // e.g., sms.send_request.tenant123
				},
				pub_chatbot_platform_event: {
					exchange: "chatbot_platform_events_ex",
					routingKey: "chatbot.${platform}.${eventType}.${tenantId}.${botId}",
				},
				pub_chatbot_domain_event: {
					exchange: "chatbot_domain_events_ex",
					routingKey:
						"chatbot.domain.${action}.${tenantId}.${botId}${sessionId ? '.' + sessionId : ''}",
				},
				pub_dashboard_push_event: {
					// For WebSocketPushService to consume.
					exchange: "dashboard_push_events_ex",
					routingKey:
						"dashboard.push.${tenantId}${userId ? '.user.' + userId : '.all_operators'}.${dataType}", // Use template string for routing key
				},
			},
			subscriptions: {
				// Added subscriptions section if it wasn't complete
				sub_pbx_event_processor: {
					// For CallEventHandlerService
					queue: "q_worker_pbx_event_processor",
					contentType: "application/json",
					prefetch: 5, // Example prefetch
				},
				sub_websocket_push_service: {
					// For WebSocketPushService
					queue: "q_websocket_push_service",
					contentType: "application/json",
					prefetch: 10, // Example prefetch for push service
				},
				// ... other subscriptions
			},
		},
	},
};
