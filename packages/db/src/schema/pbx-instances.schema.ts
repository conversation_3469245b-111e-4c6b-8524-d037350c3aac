// packages/db/src/schema/pbx_instances.schema.ts
import { pgTable, uuid, varchar, jsonb, boolean, timestamp } from 'drizzle-orm/pg-core';
import { tenants } from './tenants.schema.js';
import { pbxTypeEnum, pbxTenantAssociationTypeEnum } from '../enums/index.js'; // Assuming these enums are exported from enums/index.ts

// Define the types locally to avoid circular dependency
interface AriConnectionDetails {
  type: 'ASTERISK_ARI';
  url: string;
  username: string;
  password: string;
  appName: string;
}

interface FreePbxGraphqlConnectionDetails {
  type: 'FREEPBX_GRAPHQL';
  apiUrl: string;
  clientId?: string;
  clientSecret?: string;
  tokenUrl?: string;
  apiKey?: string;
  apiKeyHeaderName?: string;
  apiKeyPrefix?: string;
  basicAuthUsername?: string;
  basicAuthPassword?: string;
}

type PbxConnectionDetails = AriConnectionDetails | FreePbxGraphqlConnectionDetails;

interface TenantRoutingRule {
  type: 'DID' | 'CHANNEL_VAR_MATCH' | 'QUEUE_NAME' | 'EXTENSION_PREFIX' | 'CHANNEL_NAME_PREFIX' | 'CALLER_ID_PREFIX';
  tenantId: string;
  pattern?: string;
  variableName?: string;
  variableValue?: string;
  priority?: number;
}


export const pbxInstances = pgTable('pbx_instances', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull().unique(),
  type: pbxTypeEnum('type').notNull(),
  isEnabled: boolean('is_enabled').default(true).notNull(),
  
  // Use .$type<>() to tell Drizzle about the shape of the JSONB data
  connectionDetails: jsonb('connection_details').notNull().$type<PbxConnectionDetails>(),
  
  tenantAssociationType: pbxTenantAssociationTypeEnum('tenant_association_type').notNull(),
  defaultTenantId: uuid('default_tenant_id').references(() => tenants.id),
  
  // Also type tenantRoutingRules
  tenantRoutingRules: jsonb('tenant_routing_rules').$type<TenantRoutingRule[] | null>().default(null),

  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull().$onUpdate(() => new Date()),
});

// Export types for use in other modules
export type PbxInstance = typeof pbxInstances.$inferSelect;
export type NewPbxInstance = typeof pbxInstances.$inferInsert;