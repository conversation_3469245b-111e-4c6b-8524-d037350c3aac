import { pgEnum } from "drizzle-orm/pg-core";

// 1. Define enum values as the single source of truth
const MESSAGE_DIRECTION_VALUES = ["IN", "OUT"] as const;

// 2. Create the Drizzle ORM pgEnum for database schema definition
export const messageDirectionEnum = pgEnum(
	"message_direction",
	MESSAGE_DIRECTION_VALUES,
);

// 3. Define TypeScript union type for strict type safety
export type MessageDirectionType = (typeof MESSAGE_DIRECTION_VALUES)[number];

// 4. Define TypeScript string enum for application logic
export enum MessageDirection {
	IN = "IN",
	OUT = "OUT",
}
