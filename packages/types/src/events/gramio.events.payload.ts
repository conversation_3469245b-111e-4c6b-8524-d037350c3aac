 // @repo/types/src/events/chatbot-platform.events.ts
 import type { BaseEventPayload } from "./base-event.payload.js";
 import type { Update as TelegramUpdate } from 'gramio'; // Platform-specific type

 export type TelegramUpdateReceivedEvent = BaseEventPayload & {
 platform: 'telegram'; // The platform identifier
 botDbId: string; // Your internal DB ID for the bot (from bots.schema.ts)
 rawUpdate: TelegramUpdate; // The complete raw update from Telegram
 };