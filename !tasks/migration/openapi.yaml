openapi: 3.0.0
info:
  title: Traksi Backend API
  version: 1.0.0
  description: HTTP Endpoints for the Traksi application.

servers:
  - url: http://localhost:9990/api # Or your actual backend URL
    description: Development server

paths:
  /getOperators:
    get:
      summary: Fetches a list of available operators
      tags:
        - Authentication
      responses:
        '200':
          description: A list of operators
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/OperatorInfo'
        '500':
          description: Internal server error

  /getContactName:
    get:
      summary: Get contact name for a given phone number (for Caller ID)
      tags:
        - Contacts
      parameters:
        - name: number
          in: query
          required: true
          description: The phone number to look up.
          schema:
            type: string
      responses:
        '200':
          description: Contact name (Latin characters) or empty string.
          content:
            text/plain:
              schema:
                type: string
        '422':
          description: Missing 'number' parameter.
        '500':
          description: Internal server error

  /vehicleAtDestination:
    get:
      summary: Notification that a vehicle has arrived at the destination
      tags:
        - Telephony
      parameters:
        - name: driverNum
          in: query
          required: true
          description: The driver's phone number.
          schema:
            type: string
      responses:
        '200':
          description: Action details for Asterisk (e.g., vehicle, phone, action).
          content:
            text/plain: # Example: "vehicle=123&phone=070123456&action=sms"
              schema:
                type: string
        '422':
          description: Missing 'driverNum' parameter.
        '424':
          description: No vehicle or active order found for the driver.
        '500':
          description: Internal server error

  /lineStatusUpd:
    get:
      summary: Receives line status updates from external phone systems
      tags:
        - Telephony
      parameters:
        - name: num
          in: query
          description: Phone number involved in the event.
          schema:
            type: string
        - name: ext
          in: query
          description: Extension involved in the event.
          schema:
            type: string
        - name: call_id
          in: query
          description: Call identifier from the phone system.
          schema:
            type: string
        - name: event
          in: query
          required: true
          description: The type of line event (e.g., 'incoming', 'established', 'terminated', 'hold', 'resume').
          schema:
            type: string
            enum: [incoming, established, terminated, hold, resume, outgoing, idle, busy]
      responses:
        '200':
          description: Event received successfully.
        '422':
          description: Missing required parameters.
        '500':
          description: Internal server error

  /updateTranslationDictionary:
    get:
      summary: Reports a missing translation key (for admin review)
      tags:
        - System
      parameters:
        - name: lang
          in: query
          required: true
          description: Language code (e.g., 'en', 'mk').
          schema:
            type: string
        - name: key
          in: query
          required: true
          description: The missing translation key.
          schema:
            type: string
        - name: plural
          in: query
          required: false
          description: Whether the key is for a plural form.
          schema:
            type: boolean
            default: false
      responses:
        '200':
          description: Echoes back the received parameters.
          content:
            application/json:
              schema:
                type: object
                properties:
                  lang:
                    type: string
                  key:
                    type: string
                  plural:
                    type: boolean
        '500':
          description: Internal server error

components:
  schemas:
    OperatorInfo:
      type: object
      properties:
        id:
          type: integer
          format: int32
          description: Operator ID
        name:
          type: string
          description: Operator name
        disabled:
          type: boolean
          description: Whether the operator account is disabled
    # Define other common schemas like CallObject if needed for HTTP responses
    # For now, most complex objects are via WebSocket